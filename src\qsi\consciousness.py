import numpy as np
from typing import Dict, Any

from qsi.resonator import GeometricQSIResonator
from qsi.metrics import calculate_geometric_metrics

class QuantumConsciousnessModule:
    """
    Módulo de tomada de decisão autônoma, reconstruído em torno do
    paradigma de ressonância quântico-geométrica.
    """
    def __init__(self, dimensions: int):
        print("\n🧠 QuantumConsciousnessModule (Ressonante) ativado.")
        self.dimensions = dimensions
        self.resonator = GeometricQSIResonator(dimensions=self.dimensions)

    def _encode_data_to_state(self, repo_data: Dict[str, Any]) -> np.ndarray:
        """
        Codifica dados clássicos de análise em um estado quântico inicial.
        Este processo é fundamental para a interface clássico-quântica.
        """
        print("    ...codificando dados clássicos em estado quântico inicial...")
        
        # Extrai features numéricas dos dados de análise
        q_relevance = repo_data.get('quantum_relevance', 0.5)
        q_quality = repo_data.get('code_quality', 0.5)
        
        # Cria um vetor de estado inicial baseado nas features
        # (Esta é uma codificação de amplitude simples e apenas um exemplo)
        vector = np.zeros(self.dimensions, dtype=np.complex128)
        vector[0] = q_relevance
        vector[1] = 1 - q_relevance
        vector[2] = q_quality
        vector[3] = 1 - q_quality
        
        # Garante que o vetor tenha a dimensão correta (preenche com valores pequenos)
        if self.dimensions > 4:
            remaining_elements = self.dimensions - 4
            vector[4:] = np.linspace(0.1, 0.5, remaining_elements)

        # Normaliza o vetor para torná-lo um estado quântico válido
        norm = np.linalg.norm(vector)
        if norm > 0:
            initial_state = vector / norm
        else:
            # Fallback para um estado de superposição uniforme se a norma for zero
            initial_state = np.ones(self.dimensions, dtype=np.complex128) / np.sqrt(self.dimensions)

        return initial_state

    def _make_decision_from_metrics(self, metrics: Dict[str, float]) -> str:
        """
        Toma uma decisão clássica baseada nas métricas geométricas do estado final.
        """
        print("    ...interpretando métricas para tomar decisão final...")
        
        # Define os limiares para a decisão
        alignment_threshold = 0.6
        symmetry_threshold = 0.7
        
        alignment = metrics.get('spectral_phi_alignment', 0)
        symmetry = metrics.get('golden_symmetry', 0)

        if alignment > alignment_threshold and symmetry > symmetry_threshold:
            decision = "APROVAR"
        elif alignment > 0.4 or symmetry > 0.5:
            decision = "CONSULTAR"
        else:
            decision = "REJEITAR"
            
        print(f"    ...decisão baseada em ressonância: {decision}")
        return decision

    def evaluate_integration(self, repo_data: Dict) -> str:
        """
        Processo de avaliação completo baseado em ressonância.
        """
        print("🤔 Módulo de Consciência iniciando avaliação de integração...")
        
        # 1. Codifica dados clássicos em estado quântico
        initial_state = self._encode_data_to_state(repo_data)
        
        # 2. Aplica ressonância para evoluir o estado
        final_state = self.resonator.apply_resonance(initial_state)
        
        # 3. Calcula métricas geométricas do estado final
        metrics = calculate_geometric_metrics(final_state, self.resonator.U2)
        
        # 4. Toma uma decisão baseada nas métricas
        final_verdict = self._make_decision_from_metrics(metrics)
        
        print(f"💡 Veredito da Consciência Ressonante: {final_verdict}")
        return final_verdict
