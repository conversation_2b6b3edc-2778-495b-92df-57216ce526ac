"""Utility modules supporting ``Q<PERSON><PERSON><PERSON>RealTimeTrader``."""

from __future__ import annotations

from typing import Any

__all__ = [
    "initialize_exchange_connection",
    "is_exchange_connected",
    "close_exchange",
    "process_trading_decision",
    "open_position",
    "execute_close_order",
    "finalize_close",
    "close_position",
    "setup_risk_managers",
    "enforce_position_closure_rules",
    "sync_open_positions",
    "setup_paths",
    "configure_exchange",
    "save_open_positions",
    "load_open_positions",
    "handle_open_positions_on_shutdown",
    "HUDManager",
]


def __getattr__(name: str) -> Any:
    """Lazily import trader utilities to avoid heavy side effects."""

    if name in {
        "initialize_exchange_connection",
        "is_exchange_connected",
        "close_exchange",
    }:
        from .exchange_manager import (
            initialize_exchange_connection,
            is_exchange_connected,
            close_exchange,
        )

        return {
            "initialize_exchange_connection": initialize_exchange_connection,
            "is_exchange_connected": is_exchange_connected,
            "close_exchange": close_exchange,
        }[name]

    if name in {
        "process_trading_decision",
        "open_position",
        "execute_close_order",
        "finalize_close",
        "close_position",
    }:
        from .execution_engine import (
            process_trading_decision,
            open_position,
            execute_close_order,
            finalize_close,
            close_position,
        )

        return {
            "process_trading_decision": process_trading_decision,
            "open_position": open_position,
            "execute_close_order": execute_close_order,
            "finalize_close": finalize_close,
            "close_position": close_position,
        }[name]

    if name in {
        "setup_risk_managers",
        "enforce_position_closure_rules",
        "sync_open_positions",
    }:
        from .position_manager import (
            setup_risk_managers,
            enforce_position_closure_rules,
            sync_open_positions,
        )

        return {
            "setup_risk_managers": setup_risk_managers,
            "enforce_position_closure_rules": enforce_position_closure_rules,
            "sync_open_positions": sync_open_positions,
        }[name]

    if name in {"setup_paths", "configure_exchange"}:
        from .setup_utils import setup_paths, configure_exchange

        return {"setup_paths": setup_paths, "configure_exchange": configure_exchange}[
            name
        ]

    if name in {
        "save_open_positions",
        "load_open_positions",
        "handle_open_positions_on_shutdown",
    }:
        from .position_storage import (
            save_open_positions,
            load_open_positions,
            handle_open_positions_on_shutdown,
        )

        return {
            "save_open_positions": save_open_positions,
            "load_open_positions": load_open_positions,
            "handle_open_positions_on_shutdown": handle_open_positions_on_shutdown,
        }[name]

    if name == "HUDManager":
        from .hud_manager import HUDManager as _HUDManager

        return _HUDManager

    raise AttributeError(f"module 'qualia.trader' has no attribute {name}")
