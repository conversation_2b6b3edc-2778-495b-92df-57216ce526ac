from __future__ import annotations

from functools import lru_cache

from . import get_global_config_loader


@lru_cache(maxsize=1)
def get_min_counts_diversity_ratio() -> float:
    """Return the minimum counts diversity ratio from configuration.

    Returns
    -------
    float
        Configured ``min_counts_diversity_ratio`` value.

    Raises
    ------
    ValueError
        If the ``universe_config`` section or the parameter is missing.
    """

    config = get_global_config_loader().load()
    universe_cfg = config.get("universe_config")
    if (
        not isinstance(universe_cfg, dict)
        or "min_counts_diversity_ratio" not in universe_cfg
    ):
        raise ValueError(
            "Par\u00e2metro obrigat\u00f3rio 'min_counts_diversity_ratio' ausente em universe_config."
        )
    return universe_cfg["min_counts_diversity_ratio"]


@lru_cache(maxsize=1)
def get_pattern_ttl_seconds() -> float:
    """Return ``pattern_ttl_seconds`` from configuration.

    Returns
    -------
    float
        Configured ``pattern_ttl_seconds`` value.

    Raises
    ------
    ValueError
        If the ``qpm_config`` section or the parameter is missing.
    """

    config = get_global_config_loader().load()
    qpm_cfg = config.get("qpm_config")
    if not isinstance(qpm_cfg, dict) or "pattern_ttl_seconds" not in qpm_cfg:
        raise ValueError(
            "Par\u00e2metro obrigat\u00f3rio 'pattern_ttl_seconds' ausente em qpm_config."
        )
    return qpm_cfg["pattern_ttl_seconds"]


@lru_cache(maxsize=1)
def get_qast_state_history_max() -> int:
    """Return ``state_history_max`` from ``qast`` configuration."""

    config = get_global_config_loader().load()
    return int(config.get("qast", {}).get("state_history_max", 10000))


__all__ = [
    "get_min_counts_diversity_ratio",
    "get_pattern_ttl_seconds",
    "get_qast_state_history_max",
]
