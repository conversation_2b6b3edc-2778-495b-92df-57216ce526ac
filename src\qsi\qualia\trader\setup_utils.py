"""Utility helpers for configuring exchanges and paths for traders."""

from __future__ import annotations

import os
from typing import Any, Optional

from ..market.trading_engine import SecurityManager
from ..config.settings import settings
from ..utils.logger import get_logger

logger = get_logger(__name__)


def setup_paths(
    trader: Any,
    strategy_config_path: Optional[str],
    base_directory: Optional[str],
    qast_historical_data_path: Optional[str],
) -> None:
    """Normalize and store directory-related paths on ``trader``."""

    if strategy_config_path is None:
        strategy_config_path = settings.strategy_parameters_path

    trader.strategy_config_path = _normalize_path(strategy_config_path)
    trader.base_directory = _normalize_path(base_directory or "./")
    trader.qast_historical_data_path = (
        _normalize_path(qast_historical_data_path)
        if qast_historical_data_path
        else None
    )


def configure_exchange(
    trader: Any,
    kraken_api_key: Optional[str],
    kraken_secret_key: Optional[str],
    kucoin_api_key: Optional[str],
    kucoin_secret_key: Optional[str],
    kucoin_passphrase: Optional[str],
) -> None:
    """Configure security and exchange credentials for ``trader``."""

    trader.security = SecurityManager()
    if trader.data_source == "kraken" and kraken_api_key and kraken_secret_key:
        trader._secure_config = {
            "encrypted_credentials": trader.security.encrypt_credentials(
                kraken_api_key, kraken_secret_key
            ),
            "credentials_salt": os.urandom(32),
        }
        trader._api_key_hash, _ = trader.security.hash_sensitive_data(
            kraken_api_key, trader._secure_config["credentials_salt"]
        )
    elif trader.data_source == "kucoin" and kucoin_api_key and kucoin_secret_key:
        trader._secure_config = {
            "encrypted_credentials": trader.security.encrypt_credentials(
                kucoin_api_key, kucoin_secret_key
            ),
            "credentials_salt": os.urandom(32),
        }
        trader._api_key_hash, _ = trader.security.hash_sensitive_data(
            kucoin_api_key, trader._secure_config["credentials_salt"]
        )
    else:
        trader._secure_config = None
        trader._api_key_hash = None

    trader.kraken_api_key = kraken_api_key
    trader.kraken_secret_key = kraken_secret_key
    trader.kucoin_api_key = kucoin_api_key
    trader.kucoin_secret_key = kucoin_secret_key
    trader.kucoin_passphrase = kucoin_passphrase


def _normalize_path(path: Optional[str]) -> Optional[str]:
    if path is None:
        return None
    return os.path.normpath(path.replace("\\", os.sep))
