from __future__ import annotations

from concurrent.futures import ThreadPoolExecutor
import asyncio
from dataclasses import dataclass
from typing import List, Optional

import numpy as np

from ..farsight.embedder import Embedder
from ..utils.logger import get_logger

logger = get_logger(__name__)

try:  # Optional dependencies
    import umap  # type: ignore
except ModuleNotFoundError:  # pragma: no cover - best-effort message
    umap = None  # type: ignore
    logger.warning(
        "umap-learn not installed; install with 'pip install qualia_qast[metacog]'"
    )

try:
    import hdbscan  # type: ignore
except ModuleNotFoundError:  # pragma: no cover - best-effort message
    hdbscan = None  # type: ignore
    logger.warning(
        "hdbscan not installed; install with 'pip install qualia_qast[metacog]'"
    )


@dataclass
class ClusterInsights:
    """Container for cluster analysis results."""

    labels: List[int]
    curvature_avg: float
    burst_eta: float


class MetacogClusterAnalyzer:
    """Pipeline for semantic clustering with optional acceleration."""

    def __init__(self, embedder: Optional[Embedder] = None) -> None:
        self.embedder = embedder or Embedder()
        self._executor = ThreadPoolExecutor(max_workers=1)

    def _analyze_sync(self, payload: List[str]) -> ClusterInsights:
        if umap is None or hdbscan is None:
            raise RuntimeError(
                "UMAP/HDBSCAN unavailable. Install with 'pip install qualia_qast[metacog]'"
            )
        from .service import push_metric

        embeddings = self.embedder.encode(payload)
        reducer = umap.UMAP(n_components=min(10, embeddings.shape[1]), random_state=42)
        reduced = reducer.fit_transform(embeddings)
        clusterer = hdbscan.HDBSCAN(min_cluster_size=5)
        labels = clusterer.fit_predict(reduced)

        curvature = float(np.linalg.norm(reduced - reduced.mean(axis=0), axis=1).mean())
        burst_eta = float(
            getattr(clusterer, "cluster_persistence_", np.array([0.0])).mean()
        )

        push_metric("metacog.cluster.curvature_avg", curvature)
        push_metric("metacog.cluster.burst_eta", burst_eta)
        return ClusterInsights(
            labels=labels.tolist(), curvature_avg=curvature, burst_eta=burst_eta
        )

    async def analyze(self, payload: List[str]) -> ClusterInsights:
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(self._executor, self._analyze_sync, payload)
