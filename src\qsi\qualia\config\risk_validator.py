"""
TASK 4: Validador Robusto de Parâmetros de Risco
Módulo para validação, correção e fallback de configurações de risco do sistema QUALIA.
"""

import json
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from src.qualia.utils.logger import get_logger
from .risk_profiles import _FALLBACK_RISK_PROFILE_SETTINGS
from .risk_profile_definitions import (
    OPTIONAL_PARAMETERS,
    REQUIRED_PARAMETERS,
)

logger = get_logger(__name__)


def _parse_bool(value: Any) -> Optional[bool]:
    """Parse boolean values from common string and numeric representations."""
    if isinstance(value, bool):
        return value

    if isinstance(value, (int, float)) and value in {0, 1}:
        return bool(value)

    if isinstance(value, str):
        normalized = value.strip().lower()
        if normalized in {"1", "true", "yes", "y", "on"}:
            return True
        if normalized in {"0", "false", "no", "n", "off"}:
            return False

    return None


@dataclass
class RiskValidationResult:
    """Resultado da validação de parâmetros de risco."""

    is_valid: bool
    corrected_config: Dict[str, Any]
    warnings: List[str]
    errors: List[str]
    applied_fallbacks: List[str]
    missing_parameters: List[str]


class RiskParameterValidator:
    """
    Validador robusto para parâmetros de risco do sistema QUALIA.

    Características:
    - Validação completa de estrutura e valores
    - Fallbacks inteligentes para parâmetros ausentes
    - Correção automática de valores inválidos
    - Logging detalhado de problemas e correções
    """

    # Parâmetros obrigatórios e opcionais compartilhados
    REQUIRED_PARAMETERS = REQUIRED_PARAMETERS
    OPTIONAL_PARAMETERS = OPTIONAL_PARAMETERS

    # Validadores de tipo e range para cada parâmetro
    PARAMETER_VALIDATORS = {
        "position_sizing_mode": {
            "type": str,
            "allowed_values": [
                "fixed_percentage",
                "volatility_adjusted",
                "kelly_criterion",
            ],
        },
        "max_position_percentage": {
            "type": (int, float),
            "min_value": 0.001,
            "max_value": 1.0,
        },
        "stop_loss_percentage": {
            "type": (int, float),
            "min_value": 0.001,
            "max_value": 0.5,
        },
        "take_profit_percentage": {
            "type": (int, float),
            "min_value": 0.001,
            "max_value": 1.0,
        },
        "max_open_positions": {"type": int, "min_value": 1, "max_value": 10},
        "enable_trailing_stop": {"type": bool},
        "enable_dynamic_position_sizing": {"type": bool},
        "quantum_sensitivity_boost": {
            "type": (int, float),
            "min_value": 0.1,
            "max_value": 3.0,
        },
    }

    def __init__(self):
        """Initialize RiskParameterValidator."""

    def validate_config_file(
        self, config_path: Union[str, Path]
    ) -> RiskValidationResult:
        """
        Valida um arquivo de configuração completo.

        Args:
            config_path: Caminho para o arquivo de configuração

        Returns:
            RiskValidationResult com status e correções aplicadas
        """
        config_path = Path(config_path)

        if not config_path.exists():
            return RiskValidationResult(
                is_valid=False,
                corrected_config=self._get_fallback_config(),
                warnings=[],
                errors=[f"Arquivo de configuração não encontrado: {config_path}"],
                applied_fallbacks=["Usando configuração padrão completa"],
                missing_parameters=[],
            )

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Erro ao decodificar JSON em {config_path}: {e}")
            return RiskValidationResult(
                is_valid=False,
                corrected_config=self._get_fallback_config(),
                warnings=[],
                errors=[f"JSON inválido: {e}"],
                applied_fallbacks=["Usando configuração padrão devido a JSON inválido"],
                missing_parameters=[],
            )

        return self.validate_risk_profiles(config.get("risk_profile_settings", {}))

    def validate_risk_profiles(
        self, profiles_config: Dict[str, Any]
    ) -> RiskValidationResult:
        """
        Valida configurações de perfis de risco.

        Args:
            profiles_config: Dicionário com configurações de perfis

        Returns:
            RiskValidationResult com validação completa
        """
        warnings = []
        errors = []
        applied_fallbacks = []
        missing_parameters = []
        corrected_config = {}

        # Verificar se há pelo menos um perfil
        if not profiles_config:
            warnings.append("Nenhum perfil de risco encontrado")
            applied_fallbacks.append(
                "Adicionando perfis padrão: conservative, moderate, aggressive"
            )
            corrected_config = self._get_fallback_profiles()
        else:
            corrected_config = profiles_config.copy()

        # Validar cada perfil existente e adicionar ausentes
        required_profiles = ["conservative", "moderate", "aggressive"]

        for profile_name in required_profiles:
            if profile_name not in corrected_config:
                warnings.append(f"Perfil '{profile_name}' ausente")
                applied_fallbacks.append(f"Adicionando perfil padrão: {profile_name}")
                corrected_config[profile_name] = asdict(
                    _FALLBACK_RISK_PROFILE_SETTINGS[profile_name]
                )
            else:
                # Validar perfil existente
                profile_result = self._validate_single_profile(
                    profile_name, corrected_config[profile_name]
                )

                corrected_config[profile_name] = profile_result.corrected_config
                warnings.extend(profile_result.warnings)
                errors.extend(profile_result.errors)
                applied_fallbacks.extend(profile_result.applied_fallbacks)
                missing_parameters.extend(profile_result.missing_parameters)

        # Validar perfis customizados adicionais
        for profile_name, profile_config in corrected_config.items():
            if profile_name not in required_profiles:
                profile_result = self._validate_single_profile(
                    profile_name, profile_config
                )
                corrected_config[profile_name] = profile_result.corrected_config
                warnings.extend(profile_result.warnings)
                errors.extend(profile_result.errors)
                applied_fallbacks.extend(profile_result.applied_fallbacks)
                missing_parameters.extend(profile_result.missing_parameters)

        is_valid = len(errors) == 0

        return RiskValidationResult(
            is_valid=is_valid,
            corrected_config={"risk_profile_settings": corrected_config},
            warnings=warnings,
            errors=errors,
            applied_fallbacks=applied_fallbacks,
            missing_parameters=missing_parameters,
        )

    def _validate_single_profile(
        self, profile_name: str, profile_config: Dict[str, Any]
    ) -> RiskValidationResult:
        """Valida um único perfil de risco."""
        warnings = []
        errors = []
        applied_fallbacks = []
        missing_parameters = []
        corrected_config = profile_config.copy()

        # Verificar parâmetros obrigatórios
        for param in self.REQUIRED_PARAMETERS:
            if param not in corrected_config:
                missing_parameters.append(f"{profile_name}.{param}")
                fallback_value = self._get_fallback_value(profile_name, param)
                corrected_config[param] = fallback_value
                applied_fallbacks.append(
                    f"Parâmetro obrigatório '{param}' ausente em '{profile_name}', "
                    f"usando fallback: {fallback_value}"
                )

        # Adicionar parâmetros opcionais ausentes
        for param, default_value in self.OPTIONAL_PARAMETERS.items():
            if param not in corrected_config:
                corrected_config[param] = default_value
                applied_fallbacks.append(
                    f"Parâmetro opcional '{param}' ausente em '{profile_name}', "
                    f"usando padrão: {default_value}"
                )

        # Validar tipos e valores
        for param, value in corrected_config.items():
            if param in self.PARAMETER_VALIDATORS:
                validation_result = self._validate_parameter(profile_name, param, value)
                if not validation_result["is_valid"]:
                    if validation_result["corrected_value"] is not None:
                        corrected_config[param] = validation_result["corrected_value"]
                        warnings.append(validation_result["message"])
                        applied_fallbacks.append(
                            f"Valor corrigido para '{param}' em '{profile_name}': "
                            f"{value} → {validation_result['corrected_value']}"
                        )
                    else:
                        errors.append(validation_result["message"])

        return RiskValidationResult(
            is_valid=len(errors) == 0,
            corrected_config=corrected_config,
            warnings=warnings,
            errors=errors,
            applied_fallbacks=applied_fallbacks,
            missing_parameters=missing_parameters,
        )

    def _validate_parameter(
        self, profile_name: str, param: str, value: Any
    ) -> Dict[str, Any]:
        """Valida um parâmetro específico."""
        validator = self.PARAMETER_VALIDATORS.get(param, {})

        # Validar tipo
        expected_type = validator.get("type")
        if expected_type and not isinstance(value, expected_type):
            try:
                # Tentar conversão automática
                if expected_type is bool:
                    parsed = _parse_bool(value)
                    if parsed is None:
                        raise ValueError("invalid boolean")
                    corrected_value = parsed
                elif expected_type in [(int, float), float]:
                    corrected_value = float(value)
                elif expected_type is int:
                    corrected_value = int(float(value))
                elif expected_type is str:
                    corrected_value = str(value)
                else:
                    corrected_value = None

                return {
                    "is_valid": False,
                    "corrected_value": corrected_value,
                    "message": f"Tipo inválido para '{param}' em '{profile_name}': "
                    f"esperado {expected_type}, recebido {type(value)}",
                }
            except (ValueError, TypeError):
                return {
                    "is_valid": False,
                    "corrected_value": self._get_fallback_value(profile_name, param),
                    "message": f"Impossível converter '{param}' em '{profile_name}': {value}",
                }

        # Validar valores permitidos
        allowed_values = validator.get("allowed_values")
        if allowed_values and value not in allowed_values:
            return {
                "is_valid": False,
                "corrected_value": allowed_values[0],
                "message": f"Valor inválido para '{param}' em '{profile_name}': "
                f"'{value}' não está em {allowed_values}",
            }

        # Validar range numérico
        min_value = validator.get("min_value")
        max_value = validator.get("max_value")

        if min_value is not None and value < min_value:
            return {
                "is_valid": False,
                "corrected_value": min_value,
                "message": f"Valor muito baixo para '{param}' em '{profile_name}': "
                f"{value} < {min_value}",
            }

        if max_value is not None and value > max_value:
            return {
                "is_valid": False,
                "corrected_value": max_value,
                "message": f"Valor muito alto para '{param}' em '{profile_name}': "
                f"{value} > {max_value}",
            }

        return {"is_valid": True, "corrected_value": value, "message": ""}

    def _get_fallback_value(self, profile_name: str, param: str) -> Any:
        """Obtém valor de fallback para um parâmetro."""
        # Primeiro, tentar obter do perfil padrão correspondente
        if profile_name in _FALLBACK_RISK_PROFILE_SETTINGS:
            fallback_profile = _FALLBACK_RISK_PROFILE_SETTINGS[profile_name]
            if hasattr(fallback_profile, param):
                return getattr(fallback_profile, param)

        # Se não encontrar, usar perfil 'moderate' como fallback
        moderate_profile = _FALLBACK_RISK_PROFILE_SETTINGS["moderate"]
        if hasattr(moderate_profile, param):
            return getattr(moderate_profile, param)

        # Último recurso: valores padrão hardcoded
        fallback_values = {
            "position_sizing_mode": "volatility_adjusted",
            "max_position_percentage": 0.05,
            "stop_loss_percentage": 0.025,
            "take_profit_percentage": 0.04,
            "max_open_positions": 2,
            "enable_trailing_stop": True,
            "enable_dynamic_position_sizing": True,
            "quantum_sensitivity_boost": 1.2,
        }

        return fallback_values.get(param, None)

    def _get_fallback_profiles(self) -> Dict[str, Any]:
        """Retorna configuração completa de perfis de fallback."""
        return {
            profile_name: asdict(profile_settings)
            for profile_name, profile_settings in _FALLBACK_RISK_PROFILE_SETTINGS.items()
        }

    def _get_fallback_config(self) -> Dict[str, Any]:
        """Retorna configuração completa de fallback."""
        return {"risk_profile_settings": self._get_fallback_profiles()}

    def fix_config_file(
        self, config_path: Union[str, Path], backup: bool = True
    ) -> RiskValidationResult:
        """
        Corrige um arquivo de configuração aplicando todas as correções necessárias.

        Args:
            config_path: Caminho para o arquivo de configuração
            backup: Se deve criar backup do arquivo original

        Returns:
            RiskValidationResult com o resultado da correção
        """
        config_path = Path(config_path)

        # Validar primeiro
        result = self.validate_config_file(config_path)

        if not result.is_valid or result.applied_fallbacks:
            # Criar backup se solicitado
            if backup and config_path.exists():
                backup_path = config_path.with_suffix(config_path.suffix + ".backup")
                backup_path.write_text(
                    config_path.read_text(encoding="utf-8"), encoding="utf-8"
                )
                logger.info(f"Backup criado: {backup_path}")

            # Carregar configuração existente ou criar nova
            if config_path.exists():
                try:
                    with open(config_path, "r", encoding="utf-8") as f:
                        existing_config = json.load(f)
                except json.JSONDecodeError:
                    existing_config = {}
            else:
                existing_config = {}

            # Merge das correções
            existing_config.update(result.corrected_config)

            # Salvar arquivo corrigido
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(existing_config, f, indent=4, ensure_ascii=False)

            logger.info(f"Arquivo de configuração corrigido: {config_path}")

            # Log das correções aplicadas
            if result.applied_fallbacks:
                logger.info("Correções aplicadas:")
                for fallback in result.applied_fallbacks:
                    logger.info(f"  - {fallback}")

            if result.warnings:
                logger.warning("Avisos durante correção:")
                for warning in result.warnings:
                    logger.warning(f"  - {warning}")

        return result

    def get_validation_summary(self, result: RiskValidationResult) -> str:
        """Gera resumo textual da validação."""
        lines = []
        lines.append("=== VALIDAÇÃO DE PARÂMETROS DE RISCO ===")
        lines.append(f"Status: {'✅ VÁLIDO' if result.is_valid else '❌ INVÁLIDO'}")

        if result.errors:
            lines.append(f"\n❌ Erros ({len(result.errors)}):")
            for error in result.errors:
                lines.append(f"  - {error}")

        if result.warnings:
            lines.append(f"\n⚠️ Avisos ({len(result.warnings)}):")
            for warning in result.warnings:
                lines.append(f"  - {warning}")

        if result.applied_fallbacks:
            lines.append(f"\n🔧 Correções Aplicadas ({len(result.applied_fallbacks)}):")
            for fallback in result.applied_fallbacks:
                lines.append(f"  - {fallback}")

        if result.missing_parameters:
            lines.append(
                f"\n📋 Parâmetros Ausentes ({len(result.missing_parameters)}):"
            )
            for param in result.missing_parameters:
                lines.append(f"  - {param}")

        return "\n".join(lines)


def validate_and_fix_risk_config(
    config_path: Union[str, Path], auto_fix: bool = True
) -> RiskValidationResult:
    """
    Função de conveniência para validar e opcionalmente corrigir configuração de risco.

    Args:
        config_path: Caminho para arquivo de configuração
        auto_fix: Se deve aplicar correções automaticamente

    Returns:
        RiskValidationResult com resultado da validação/correção
    """
    validator = RiskParameterValidator()

    if auto_fix:
        result = validator.fix_config_file(config_path)
        logger.info("🔧 TASK 4: Correção de Parâmetros de Risco Concluída")
        logger.info(validator.get_validation_summary(result))
    else:
        result = validator.validate_config_file(config_path)
        logger.info("🔍 TASK 4: Validação de Parâmetros de Risco")
        logger.info(validator.get_validation_summary(result))

    return result


__all__ = [
    "RiskValidationResult",
    "RiskParameterValidator",
    "validate_and_fix_risk_config",
]
