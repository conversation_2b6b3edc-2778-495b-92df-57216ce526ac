import logging
from typing import Dict
import numpy as np

logger = logging.getLogger(__name__)


def calculate_geometric_metrics(state: np.ndarray, resonance_matrix: np.ndarray) -> Dict[str, float]:

    """Calcula métricas geométricas do estado final.

    Args:
        state: vetor de estado quântico já processado pelo sistema.
        resonance_matrix: matriz de ressonância aplicada ao estado.

    Returns:
        Dicionário com ``spectral_phi_alignment``, ``golden_symmetry`` e
        ``geometric_coherence``.
    """

    logger.info("        ...calculando métricas geométricas do estado final...")

    state = np.asarray(state, dtype=np.complex128).flatten()

    norm = np.linalg.norm(state)
    if norm > 0:
        state = state / norm

    U = np.asarray(resonance_matrix, dtype=np.complex128)
    try:
        eigenvalues, eigenvectors = np.linalg.eigh(U)
    except np.linalg.LinAlgError:
        eigenvalues, eigenvectors = np.linalg.eig(U)

    projections = np.abs(eigenvectors.conj().T @ state) ** 2
    projections /= projections.sum() if projections.sum() else 1.0

    phi = (1 + 5 ** 0.5) / 2
    spectral_phi_alignment = float(np.real(np.dot(projections, np.abs(eigenvalues)))) / phi
    spectral_phi_alignment = max(0.0, min(1.0, spectral_phi_alignment))

    golden_symmetry = 1.0 / (1.0 + abs(spectral_phi_alignment - 1.0))

    ones_state = np.ones_like(state) / np.sqrt(len(state))
    geometric_coherence = float(np.abs(np.vdot(state, ones_state)))


    metrics = {
        'spectral_phi_alignment': spectral_phi_alignment,
        'golden_symmetry': golden_symmetry,

        'geometric_coherence': geometric_coherence,
    }

    logger.info(
        "        ...métricas calculadas: %s",
        {k: f"{v:.3f}" for k, v in metrics.items()},
    )


    return metrics
