#!/usr/bin/env python3
"""
Script para calibrar TODAS as métricas do sistema QUALIA
Executa simulação histórica unificada para determinar thresholds ótimos
Inclui: consciousness, coherence, confidence, volume_surge_min, momentum_min,
        spectral_phi_alignment_min, golden_symmetry_min
"""

import asyncio
import sys
import os
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def main():
    """Função principal de calibração unificada"""

    print("🌌 QUALIA - Calibração Unificada de TODAS as Métricas")
    print("=" * 70)
    print("📊 Métricas: consciousness, coherence, confidence, volume_surge,")
    print("             momentum, spectral_phi_alignment, golden_symmetry")
    print("=" * 70)
    
    try:
        # 1. Inicializar sistema de trading
        logger.info("🔧 Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()
        await trading_system.initialize()
        
        # 2. Criar calibrador unificado
        calibrator = QualiaMetricsCalibrator(trading_system)

        # 3. Executar calibração unificada
        logger.info("📊 Iniciando calibração UNIFICADA baseada em simulação histórica...")
        logger.info("🎯 Objetivo: Otimizar TODOS os thresholds simultaneamente")
        
        # Parâmetros de calibração
        DAYS_BACK = 30              # 30 dias de histórico
        PROFIT_THRESHOLD = 0.02     # 2% de lucro mínimo
        TIME_HORIZON = 4            # 4 horas para verificar lucro
        
        results = await calibrator.calibrate_all_assets(
            days_back=DAYS_BACK,
            profit_threshold=PROFIT_THRESHOLD,
            time_horizon_hours=TIME_HORIZON
        )
        
        # 4. Mostrar resumo
        summary = calibrator.get_calibration_summary(results)
        print(summary)
        
        # 5. Mostrar análise detalhada
        print("\n" + "="*70)
        print("📈 ANÁLISE DETALHADA POR MÉTRICA")
        print("="*70)

        # Mostrar estatísticas por métrica
        await show_detailed_analysis(results)

        # 6. Perguntar se deve aplicar
        print("\n" + "="*70)
        response = input("🤔 Aplicar TODOS os thresholds calibrados ao sistema? (s/N): ").lower()

        if response in ['s', 'sim', 'y', 'yes']:
            # Perguntar sobre fator de aplicação gradual
            gradual_response = input("🔧 Aplicação gradual? (70% novo + 30% atual) [s/N]: ").lower()
            gradual_factor = 0.7 if gradual_response in ['s', 'sim', 'y', 'yes'] else 1.0

            await calibrator.apply_calibrated_thresholds(results, gradual_factor)
            print("✅ TODOS os thresholds aplicados com sucesso!")

            # Mostrar thresholds finais
            await show_final_thresholds(trading_system)

        else:
            print("ℹ️ Thresholds não aplicados. Resultados salvos para análise.")

        # 7. Mostrar próximos passos
        print("\n🎯 PRÓXIMOS PASSOS:")
        print("1. Monitorar performance com novos thresholds")
        print("2. Executar recalibração semanalmente")
        print("3. Ajustar baseado em trades reais")
        print("4. Considerar calibração por ativo individual")
        print("5. Analisar correlação entre métricas")
        print("6. Implementar calibração online adaptativa")
        
    except Exception as e:
        logger.error(f"❌ Erro durante calibração: {e}")
        raise
    
    finally:
        # Cleanup
        if 'trading_system' in locals():
            await trading_system.cleanup()

async def show_detailed_analysis(results):
    """Mostra análise detalhada dos resultados por métrica"""
    if not results:
        print("❌ Nenhum resultado disponível")
        return

    # Calcular estatísticas agregadas por métrica
    all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min',
                   'momentum_min', 'spectral_phi_alignment_min', 'golden_symmetry_min']

    for metric in all_metrics:
        values = []
        for result in results.values():
            if metric in result.recommended_thresholds:
                values.append(result.recommended_thresholds[metric])

        if values:
            print(f"\n📊 {metric.upper()}:")
            print(f"   Valores: {len(values)} ativos")
            print(f"   Mínimo:  {min(values):.3f}")
            print(f"   Máximo:  {max(values):.3f}")
            print(f"   Mediana: {np.median(values):.3f}")
            print(f"   Média:   {np.mean(values):.3f}")
            print(f"   Desvio:  {np.std(values):.3f}")

async def show_final_thresholds(trading_system):
    """Mostra thresholds finais aplicados ao sistema"""
    print("\n🎯 THRESHOLDS FINAIS APLICADOS:")
    print("-" * 50)

    thresholds = trading_system.quantum_thresholds
    for metric, value in thresholds.items():
        print(f"   {metric}: {value:.3f}")

def run_calibration():
    """Wrapper para executar calibração unificada"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Calibração interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_calibration()
