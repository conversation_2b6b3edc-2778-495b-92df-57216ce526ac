"""
Sistema de Calibração Online Adaptativa QUALIA
Aprende com trades reais e ajusta thresholds dinamicamente
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, asdict
import json
from pathlib import Path
import asyncio

from .config_manager import get_config_manager
from .geometric_metrics_calibrator import CalibrationPoint, CalibrationResults
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class TradeOutcome:
    """Resultado de um trade real"""
    trade_id: str
    symbol: str
    timestamp: datetime
    entry_price: float
    exit_price: float
    pnl: float
    pnl_percentage: float
    duration_hours: float
    was_profitable: bool
    
    # Métricas no momento da decisão
    consciousness: float
    coherence: float
    confidence: float
    volume_surge: float
    momentum: float
    spectral_phi_alignment: float
    golden_symmetry: float
    geometric_coherence: float
    
    # Contexto adicional
    market_conditions: Dict
    trade_reason: str

@dataclass
class PerformanceMetrics:
    """Métricas de performance do sistema"""
    total_trades: int
    profitable_trades: int
    win_rate: float
    avg_pnl: float
    avg_duration: float
    sharpe_ratio: float
    max_drawdown: float
    
    # Métricas por threshold
    threshold_effectiveness: Dict[str, float]
    false_positive_rate: float
    false_negative_rate: float

class OnlineCalibrationSystem:
    """Sistema de calibração online que aprende com trades reais"""
    
    def __init__(self, trading_system):
        self.trading_system = trading_system
        self.config_manager = get_config_manager()
        
        # Armazenamento de dados
        self.trade_outcomes: List[TradeOutcome] = []
        self.performance_history: List[PerformanceMetrics] = []
        
        # Configurações de calibração
        self.min_trades_for_calibration = 20
        self.calibration_window_days = 7
        self.learning_rate = 0.1
        self.stability_threshold = 0.05
        
        # Estado do sistema
        self.last_calibration = None
        self.calibration_in_progress = False
        
        # Carregar dados históricos se existirem
        self._load_historical_data()
    
    async def record_trade_outcome(self, trade_data: Dict, quantum_metrics: Dict):
        """
        Registra o resultado de um trade para aprendizado
        
        Args:
            trade_data: Dados do trade (entry, exit, pnl, etc.)
            quantum_metrics: Métricas quânticas no momento da decisão
        """
        try:
            # Criar registro do trade
            outcome = TradeOutcome(
                trade_id=trade_data.get('trade_id', f"trade_{len(self.trade_outcomes)}"),
                symbol=trade_data['symbol'],
                timestamp=datetime.now(timezone.utc),
                entry_price=trade_data['entry_price'],
                exit_price=trade_data['exit_price'],
                pnl=trade_data['pnl'],
                pnl_percentage=trade_data['pnl_percentage'],
                duration_hours=trade_data.get('duration_hours', 0),
                was_profitable=trade_data['pnl'] > 0,
                
                # Métricas quânticas
                consciousness=quantum_metrics['consciousness'],
                coherence=quantum_metrics['coherence'],
                confidence=quantum_metrics['confidence'],
                volume_surge=quantum_metrics['volume_surge'],
                momentum=quantum_metrics['momentum'],
                spectral_phi_alignment=quantum_metrics.get('spectral_phi_alignment', 0),
                golden_symmetry=quantum_metrics.get('golden_symmetry', 0),
                geometric_coherence=quantum_metrics.get('geometric_coherence', 0),
                
                # Contexto
                market_conditions=trade_data.get('market_conditions', {}),
                trade_reason=trade_data.get('reason', 'automated')
            )
            
            self.trade_outcomes.append(outcome)
            
            # Persistir dados
            await self._save_trade_outcome(outcome)
            
            logger.info(f"📊 Trade registrado: {outcome.symbol} | "
                       f"PnL: {outcome.pnl_percentage:.2%} | "
                       f"Duração: {outcome.duration_hours:.1f}h")
            
            # Verificar se deve executar calibração
            await self._check_calibration_trigger()
            
        except Exception as e:
            logger.error(f"❌ Erro registrando trade outcome: {e}")
    
    async def _check_calibration_trigger(self):
        """Verifica se deve executar calibração automática"""
        if self.calibration_in_progress:
            return
        
        # Critérios para calibração
        recent_trades = self._get_recent_trades(days=self.calibration_window_days)
        
        if len(recent_trades) < self.min_trades_for_calibration:
            return
        
        # Verificar se performance mudou significativamente
        current_performance = self._calculate_current_performance(recent_trades)
        
        if self._should_recalibrate(current_performance):
            logger.info("🔄 Trigger de recalibração detectado - iniciando calibração online...")
            await self.execute_online_calibration()
    
    def _should_recalibrate(self, current_performance: PerformanceMetrics) -> bool:
        """Determina se deve recalibrar baseado na performance"""
        if not self.performance_history:
            return True
        
        last_performance = self.performance_history[-1]
        
        # Critérios de recalibração
        win_rate_change = abs(current_performance.win_rate - last_performance.win_rate)
        avg_pnl_change = abs(current_performance.avg_pnl - last_performance.avg_pnl)
        
        # Recalibrar se mudança significativa
        return (win_rate_change > 0.1 or  # 10% mudança na win rate
                avg_pnl_change > 0.005)   # 0.5% mudança no PnL médio
    
    async def execute_online_calibration(self):
        """Executa calibração baseada em trades reais"""
        if self.calibration_in_progress:
            logger.warning("⚠️ Calibração já em progresso")
            return
        
        self.calibration_in_progress = True
        
        try:
            logger.info("🔄 Iniciando calibração online baseada em trades reais...")
            
            # 1. Analisar trades recentes
            recent_trades = self._get_recent_trades(days=self.calibration_window_days)
            profitable_trades = [t for t in recent_trades if t.was_profitable]
            
            if len(profitable_trades) < 5:
                logger.warning("⚠️ Poucos trades lucrativos para calibração online")
                return
            
            # 2. Calcular novos thresholds baseados em trades lucrativos
            new_thresholds = self._calculate_optimal_thresholds(profitable_trades)
            
            # 3. Aplicar ajustes graduais
            await self._apply_online_adjustments(new_thresholds)
            
            # 4. Atualizar métricas de performance
            current_performance = self._calculate_current_performance(recent_trades)
            self.performance_history.append(current_performance)
            
            # 5. Persistir estado
            await self._save_calibration_state()
            
            self.last_calibration = datetime.now(timezone.utc)
            
            logger.info("✅ Calibração online concluída com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na calibração online: {e}")
        
        finally:
            self.calibration_in_progress = False
    
    def _calculate_optimal_thresholds(self, profitable_trades: List[TradeOutcome]) -> Dict[str, float]:
        """Calcula thresholds ótimos baseado em trades lucrativos reais"""
        if not profitable_trades:
            return {}
        
        # Extrair métricas dos trades lucrativos
        metrics = {
            'consciousness': [t.consciousness for t in profitable_trades],
            'coherence': [t.coherence for t in profitable_trades],
            'confidence': [t.confidence for t in profitable_trades],
            'volume_surge_min': [t.volume_surge for t in profitable_trades],
            'momentum_min': [abs(t.momentum) for t in profitable_trades],
            'spectral_phi_alignment_min': [t.spectral_phi_alignment for t in profitable_trades],
            'golden_symmetry_min': [t.golden_symmetry for t in profitable_trades]
        }
        
        # Calcular percentis (mais conservador que simulação)
        optimal_thresholds = {}
        for metric, values in metrics.items():
            if values and any(v > 0 for v in values):
                # Usar percentil 25% para ser mais seletivo com dados reais
                optimal_thresholds[metric] = float(np.percentile(values, 25))
        
        return optimal_thresholds
    
    async def _apply_online_adjustments(self, new_thresholds: Dict[str, float]):
        """Aplica ajustes graduais baseados em aprendizado online"""
        current_thresholds = self.trading_system.quantum_thresholds
        
        for metric, new_value in new_thresholds.items():
            if metric in current_thresholds:
                current_value = current_thresholds[metric]
                
                # Aplicar mudança muito gradual para estabilidade
                adjusted_value = (current_value * (1 - self.learning_rate) + 
                                new_value * self.learning_rate)
                
                # Aplicar apenas se mudança não for muito drástica
                change_ratio = abs(adjusted_value - current_value) / current_value
                if change_ratio < self.stability_threshold:
                    current_thresholds[metric] = adjusted_value
                    
                    logger.info(f"🔧 ONLINE {metric}: {current_value:.3f} → {adjusted_value:.3f}")
                else:
                    logger.warning(f"⚠️ Mudança muito drástica ignorada para {metric}: "
                                 f"{change_ratio:.1%}")
    
    def _get_recent_trades(self, days: int) -> List[TradeOutcome]:
        """Obtém trades recentes dentro do período especificado"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        return [t for t in self.trade_outcomes if t.timestamp >= cutoff_date]
    
    def _calculate_current_performance(self, trades: List[TradeOutcome]) -> PerformanceMetrics:
        """Calcula métricas de performance atual"""
        if not trades:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, {}, 0, 0)
        
        profitable_trades = [t for t in trades if t.was_profitable]
        
        return PerformanceMetrics(
            total_trades=len(trades),
            profitable_trades=len(profitable_trades),
            win_rate=len(profitable_trades) / len(trades),
            avg_pnl=np.mean([t.pnl_percentage for t in trades]),
            avg_duration=np.mean([t.duration_hours for t in trades]),
            sharpe_ratio=self._calculate_sharpe_ratio(trades),
            max_drawdown=self._calculate_max_drawdown(trades),
            threshold_effectiveness={},  # TODO: Implementar
            false_positive_rate=0,       # TODO: Implementar
            false_negative_rate=0        # TODO: Implementar
        )
    
    def _calculate_sharpe_ratio(self, trades: List[TradeOutcome]) -> float:
        """Calcula Sharpe ratio dos trades"""
        if len(trades) < 2:
            return 0
        
        returns = [t.pnl_percentage for t in trades]
        return np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
    
    def _calculate_max_drawdown(self, trades: List[TradeOutcome]) -> float:
        """Calcula maximum drawdown"""
        if not trades:
            return 0
        
        cumulative_pnl = np.cumsum([t.pnl_percentage for t in trades])
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdown = running_max - cumulative_pnl
        
        return float(np.max(drawdown)) if len(drawdown) > 0 else 0
    
    async def _save_trade_outcome(self, outcome: TradeOutcome):
        """Salva resultado do trade em arquivo"""
        try:
            data_dir = Path('data/online_calibration')
            data_dir.mkdir(parents=True, exist_ok=True)
            
            filename = f"trade_outcomes_{datetime.now().strftime('%Y%m')}.jsonl"
            filepath = data_dir / filename
            
            with open(filepath, 'a') as f:
                f.write(json.dumps(asdict(outcome), default=str) + '\n')
                
        except Exception as e:
            logger.error(f"❌ Erro salvando trade outcome: {e}")
    
    async def _save_calibration_state(self):
        """Salva estado da calibração"""
        try:
            state = {
                'last_calibration': self.last_calibration.isoformat() if self.last_calibration else None,
                'total_trades': len(self.trade_outcomes),
                'performance_history': [asdict(p) for p in self.performance_history[-10:]],  # Últimas 10
                'current_thresholds': dict(self.trading_system.quantum_thresholds)
            }
            
            data_dir = Path('data/online_calibration')
            data_dir.mkdir(parents=True, exist_ok=True)
            
            with open(data_dir / 'calibration_state.json', 'w') as f:
                json.dump(state, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"❌ Erro salvando estado da calibração: {e}")
    
    def _load_historical_data(self):
        """Carrega dados históricos se existirem"""
        try:
            # Carregar trade outcomes
            data_dir = Path('data/online_calibration')
            if data_dir.exists():
                for file in data_dir.glob('trade_outcomes_*.jsonl'):
                    with open(file, 'r') as f:
                        for line in f:
                            data = json.loads(line)
                            # Converter de volta para TradeOutcome
                            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
                            self.trade_outcomes.append(TradeOutcome(**data))
            
            logger.info(f"[LOAD] Carregados {len(self.trade_outcomes)} trade outcomes historicos")
            
        except Exception as e:
            logger.error(f"[ERROR] Erro carregando dados historicos: {e}")
    
    def get_performance_summary(self) -> str:
        """Gera resumo de performance do sistema"""
        if not self.trade_outcomes:
            return "📊 Nenhum trade registrado ainda"
        
        recent_trades = self._get_recent_trades(days=7)
        performance = self._calculate_current_performance(recent_trades)
        
        return f"""
📊 PERFORMANCE ONLINE (Últimos 7 dias)
{'='*50}
🔢 Total de trades: {performance.total_trades}
💰 Trades lucrativos: {performance.profitable_trades}
🎯 Win rate: {performance.win_rate:.1%}
📈 PnL médio: {performance.avg_pnl:.2%}
⏱️ Duração média: {performance.avg_duration:.1f}h
📊 Sharpe ratio: {performance.sharpe_ratio:.2f}
📉 Max drawdown: {performance.max_drawdown:.2%}

🔄 Última calibração: {self.last_calibration.strftime('%Y-%m-%d %H:%M') if self.last_calibration else 'Nunca'}
"""
