# coding: utf-8
"""Quantum Pattern Memory implementation.

Exports
-------
QuantumPatternMemory
"""

import copy
import heapq
import threading
import asyncio
import time
import importlib.util
import json
from typing import Any, Dict, List, Optional, Tuple, Union, Deque, DefaultDict
from pathlib import Path
from collections import deque, defaultdict

import numpy as np
from pydantic import ValidationError
from sklearn.decomposition import PCA
from sklearn.neighbors import NearestNeighbors

from ..common_types import QuantumSignaturePacket
from ..memory.base_memory import BaseMemory
from ..memory.locking import LockManager, with_lock
from ..utils.logger import get_logger
from ..utils.tracing import instrument_logger, is_tracing_enabled
from ..config.settings import qpm_metrics_enabled, qpm_memory_file
from ..config.feature_flags import feature_toggle
from ..config.constants import get_pattern_ttl_seconds
try:
    from datadog import DogStatsd
except ModuleNotFoundError:
    # Create a dummy DogStatsd class if datadog is not installed.
    # This allows the system to run without this optional dependency.
    class DogStatsd:
        def increment(self, *args, **kwargs):
            pass

        def timing(self, *args, **kwargs):
            pass
import contextlib

CUPY_AVAILABLE = importlib.util.find_spec("cupy") is not None
GPU_THRESHOLD = 1024

try:
    from opentelemetry import trace
except ImportError:  # pragma: no cover - optional dependency
    trace = None

from .persistence import PatternPersistence
from ..utils.persistence import convert_to_serializable
from .similarity import SimilarityMixin
from .warmstart import WarmStartManager, load_warmstart_scenarios
from .event_bus import SimpleEventBus
from ..event_bus import AsyncEventBus
from typing import TYPE_CHECKING
from .holographic_memory import HolographicMemory
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
from ..utils.hardware_acceleration import gpu_available

if TYPE_CHECKING:  # pragma: no cover - for type hints
    from qualia.market.event_bus import MarketPatternDetected

logger = get_logger(__name__)
if is_tracing_enabled():  # pragma: no cover - runtime conditional
    instrument_logger(logger.logger)

__all__ = ["QuantumPatternMemory"]


def _gpu_dot(matrix: np.ndarray, vector: np.ndarray) -> np.ndarray:
    """Return ``matrix @ vector`` using GPU when available."""

    if CUPY_AVAILABLE and gpu_available() and matrix.shape[1] >= GPU_THRESHOLD:
        import cupy as cp

        mat_gpu = cp.asarray(matrix)
        vec_gpu = cp.asarray(vector)
        return cp.asnumpy(cp.dot(mat_gpu, vec_gpu))
    return matrix.dot(vector)


class QuantumPatternMemory(BaseMemory, SimilarityMixin):
    """Armazenamento de padrões quânticos e metadados de execução.

    A memória é particionada pela dimensão do vetor de assinatura e oferece
    recuperação baseada em similaridade. Operações que alteram o estado são
    protegidas por locks quando ``thread_safe`` ou ``async_safe`` estão ativados.
    """

    def __init__(
        self,
        max_memory_size_per_dimension: int = 1000,  # Nome alterado para clareza
        similarity_threshold: Optional[float] = 0.6,
        low_similarity_threshold: float = 0.6,
        low_threshold_max_patterns: int = 1000,
        persistence_path: Optional[str] = None,
        enable_warmstart: bool = True,  # P-9: Enable warm-start
        warmstart_min_patterns: int = 50,  # P-9: Minimum patterns for effective memory
        auto_persist: Optional[bool] = None,
        warmstart_variations: int = 15,
        warmstart_scenarios: Optional[List[Dict[str, Any]]] = None,
        warmstart_config_path: Optional[str] = None,
        backtest_import_paths: Optional[List[str]] = None,
        pca_target_dim: int = 128,
        similarity_metric: str = "cosine",
        pattern_ttl_seconds: Optional[float] = None,
        use_ann: bool = False,
        ann_threshold: int = 1000,
        memory_event_history_size: int = 200,
        *,
        thread_safe: bool = False,
        async_safe: bool = False,
        event_bus: Optional[SimpleEventBus] = None,
        holographic_memory: Optional[HolographicMemory] = None,
        risk_manager: Optional[QUALIARiskManagerBase] = None,
    ):
        """Inicializa uma nova instância.

        Parameters
        ----------
        max_memory_size_per_dimension : int, default=1000
            Número máximo de padrões armazenados por dimensão.
        similarity_threshold : float, optional
            Limiar fixo de similaridade. ``None`` ativa o modo adaptativo.
        low_similarity_threshold : float, default=0.6
            Limiar mínimo utilizado no modo adaptativo.
        low_threshold_max_patterns : int, default=1000
            Quantidade máxima de padrões considerada para usar o limiar baixo.
        persistence_path : str, optional
            Caminho para arquivo de persistência da memória.
        enable_warmstart : bool, default=True
            Ativa geração de padrões sintéticos quando a memória está vazia.
        warmstart_min_patterns : int, default=50
            Padrões mínimos para considerar a memória populada.
        auto_persist : bool or None, default=None
            Ativa salvamento automático. Quando ``None`` e ``persistence_path``
            é fornecido, a persistência automática será habilitada
            implicitamente.
        warmstart_variations : int, default=15
            Variações criadas para cada cenário sintético de warm-start.
        warmstart_scenarios : list of dict, optional
            Cenários personalizados para warm-start.
        warmstart_config_path : str, optional
            Caminho de configuração alternativo para warm-start.
        backtest_import_paths : list of str, optional
            Arquivos de memória gerados por backtests a serem importados.
        pca_target_dim : int, default=128
            Dimensão alvo ao aplicar PCA na carga de padrões persistidos.
        similarity_metric : {"cosine", "euclidean"}, default="cosine"
            Métrica utilizada para cálculo de similaridade.
        pattern_ttl_seconds : float, optional
            Tempo de vida em segundos de cada padrão armazenado.
        use_ann : bool, default=False
            Utiliza ``NearestNeighbors`` para partições grandes.
        ann_threshold : int, default=1000
            Tamanho mínimo da partição para ativar o backend aproximado.
        memory_event_history_size : int, default=200
            Quantidade máxima de eventos ``memory.update`` armazenados para
            consulta posterior.
        thread_safe : bool, default=False
            Serializa operações de escrita usando ``threading.Lock``.
        async_safe : bool, default=False
            Usa ``asyncio.Lock`` para ambientes assíncronos.
        holographic_memory : HolographicMemory, optional
            Instância utilizada para consulta de padrões históricos.
        risk_manager : QUALIARiskManagerBase, optional
            Gerenciador de risco responsável por validar armazenamento e
            recuperação de padrões. Pode ser definido posteriormente via
            :meth:`warm_start` ou injeção externa.
        """
        if risk_manager is not None and not isinstance(
            risk_manager, QUALIARiskManagerBase
        ):
            raise ValueError(
                "'risk_manager' deve ser uma instância de QUALIARiskManagerBase"
            )

        self.memory: Dict[int, Deque[Dict[str, Any]]] = {}
        self.max_memory_size_per_dimension = max_memory_size_per_dimension
        self.similarity_threshold = similarity_threshold
        self.low_similarity_threshold = low_similarity_threshold
        self.low_threshold_max_patterns = low_threshold_max_patterns
        self.persistence_path = persistence_path or qpm_memory_file
        self.enable_warmstart = enable_warmstart  # P-9
        self.warmstart_min_patterns = warmstart_min_patterns  # P-9
        self.auto_persist = (
            auto_persist if auto_persist is not None else persistence_path is not None
        )
        self.warmstart_variations = warmstart_variations
        self.warmstart_scenarios = warmstart_scenarios or load_warmstart_scenarios(
            warmstart_config_path
        )
        self.backtest_import_paths = backtest_import_paths or []
        self.pca_target_dim = pca_target_dim
        self.similarity_metric = similarity_metric
        self.pattern_ttl_seconds = (
            pattern_ttl_seconds
            if pattern_ttl_seconds is not None
            else get_pattern_ttl_seconds()
        )
        self.use_ann = use_ann
        self.ann_threshold = ann_threshold
        lock_manager = LockManager(async_safe=async_safe)
        self._lock: Optional[Union[threading.Lock, asyncio.Lock]] = (
            lock_manager.create_lock() if thread_safe or async_safe else None
        )
        self._norm_cache: Dict[str, np.ndarray] = {}
        self._pca_cache: Dict[Tuple[str, int], np.ndarray] = {}
        self._pca_models: Dict[Tuple[int, int], PCA] = {}
        self._id_index: Dict[str, Dict[str, Any]] = {}
        self.memory_update_history: Deque[Dict[str, Any]] = deque(
            maxlen=memory_event_history_size
        )

        self.persistence = PatternPersistence(self, event_bus=event_bus)
        self.warmstart_manager = WarmStartManager(self)
        self.statsd = DogStatsd() if qpm_metrics_enabled else None
        self.event_bus = event_bus
        if self.event_bus is not None:
            self.event_bus.subscribe(
                "qpm.threshold_update", self.update_threshold_from_event
            )
            self.event_bus.subscribe("memory.update", self._capture_memory_update_event)
        self.publish_memory_events = feature_toggle("memory_module")
        if self.event_bus and feature_toggle("market_module"):
            self.event_bus.subscribe("market.pattern_detected", self._on_market_pattern)
        self.holographic_memory = holographic_memory or HolographicMemory()
        self.risk_manager = risk_manager

        if self.persistence_path:
            if Path(self.persistence_path).exists():
                loaded_ok = self.load_from_file(self.persistence_path)
                self.reindex_memory()
                if loaded_ok:
                    logger.info(
                        "QPM: %s padrões carregados de '%s'",
                        sum(len(p) for p in self.memory.values()),
                        self.persistence_path,
                    )
                else:
                    logger.warning(
                        "QPM: Falha ao carregar arquivo de cache '%s'",
                        self.persistence_path,
                    )
            else:
                logger.warning(
                    "QPM: arquivo de cache '%s' não encontrado",
                    self.persistence_path,
                )

        loaded_patterns = sum(len(p) for p in self.memory.values())
        if loaded_patterns == 0:
            logger.warning(
                "QPM: cache vazio em '%s'. Nenhum padrão carregado da persistência",
                self.persistence_path,
            )

        if self.enable_warmstart:
            for path in self.backtest_import_paths:
                self.import_patterns_from_backtest(path)

            if self.warmstart_manager._is_memory_insufficient():
                current = sum(len(p) for p in self.memory.values())
                logger.info(
                    "P-9: Memória QPM insuficiente (%s padrões). Iniciando warm-start...",
                    current,
                )
                before = current
                # YAA: Proteção contra loop infinito com limite máximo de tentativas
                max_warmstart_attempts = 10
                warmstart_attempts = 0
                while self.warmstart_manager._is_memory_insufficient() and warmstart_attempts < max_warmstart_attempts:
                    warmstart_attempts += 1
                    logger.debug(f"P-9: Tentativa de warm-start {warmstart_attempts}/{max_warmstart_attempts}")
                    self.warmstart_manager._perform_warmstart()
                    # Verificar se houve progresso
                    current_after_attempt = sum(len(p) for p in self.memory.values())
                    if current_after_attempt == before and warmstart_attempts > 3:
                        logger.warning(f"P-9: Warm-start não está produzindo padrões. Interrompendo após {warmstart_attempts} tentativas.")
                        break
                after = sum(len(p) for p in self.memory.values())
                if warmstart_attempts >= max_warmstart_attempts:
                    logger.warning(f"P-9: Warm-start atingiu limite máximo de tentativas ({max_warmstart_attempts}). Continuando com {after} padrões.")
                logger.info(
                    "P-9: Warm-start finalizado. Memória contém %s padrões (+%s).",
                    after,
                    after - before,
                )
            else:
                logger.info(
                    "P-9: Warm-start não necessário; memória contém %s padrões.",
                    sum(len(p) for p in self.memory.values()),
                )

        loaded_patterns = sum(len(p) for p in self.memory.values())
        if self.similarity_threshold is None:
            self.similarity_threshold = self._determine_similarity_threshold()
            logger.info(
                "QPM: similarity_threshold definido automaticamente para %.3f "
                "com base em %s padrões carregados",
                self.similarity_threshold,
                loaded_patterns,
            )

        logger.info(
            f"QuantumPatternMemory inicializada com max_size_per_dimension={max_memory_size_per_dimension}, "
            f"similarity_threshold={similarity_threshold}. Path de persistência: "
            f"{self.persistence_path if self.persistence_path else 'Nenhum'}. "
            f"Warm-start: {'Habilitado' if self.enable_warmstart else 'Desabilitado'}. "
            f"Métrica: {self.similarity_metric}. "
            f"Memória atual (dimensões carregadas): {list(self.memory.keys()) if self.memory else 'Vazia'}."
        )

    def _require_risk_manager(self) -> None:
        """Ensure a ``QUALIARiskManagerBase`` instance is set."""

        if not isinstance(self.risk_manager, QUALIARiskManagerBase):
            logger.error("RiskManager instance not configured")
            raise RuntimeError(
                "RiskManager necessario. Passe uma instancia de QUALIARiskManagerBase"
            )

    def _purge_expired(self) -> None:
        """Remove padrões expirados com base em ``pattern_ttl_seconds``."""

        if self.pattern_ttl_seconds is None:
            return
        now = time.time()
        ttl = self.pattern_ttl_seconds
        for dim, patterns in list(self.memory.items()):
            original_len = len(patterns)
            kept = []
            for p in patterns:
                if isinstance(p, dict):
                    if p.get("timestamp", 0) + ttl > now:
                        kept.append(p)
                else:
                    logger.warning(
                        "Padrão malformado encontrado na memória durante a purga (tipo: %s). Descartando.",
                        type(p).__name__,
                    )

            if len(kept) != original_len:
                self.memory[dim] = deque(
                    kept, maxlen=self.max_memory_size_per_dimension
                )
                logger.debug(
                    "QPM: Removidos %s padrões expirados da dimensão %sD",
                    original_len - len(kept),
                    dim,
                )

    def set_similarity_threshold(self, new_threshold: Optional[float]) -> None:
        """Atualiza o limiar de similaridade usado para filtragem de padrões.

        Parameters
        ----------
        new_threshold : Optional[float]
            Novo valor do limiar fixo. Se ``None``, passa a utilizar o modo
            adaptativo. Valores fora do intervalo ``[0, 1]`` são ignorados
            para evitar comportamentos inesperados.
        """
        if new_threshold is not None and not 0.0 <= new_threshold <= 1.0:
            logger.warning(
                "QPM.set_similarity_threshold: Valor fora do intervalo [0, 1]; operação ignorada"
            )
            return

        self.similarity_threshold = new_threshold
        logger.info(
            "QPM: similarity_threshold atualizado para %.3f", self.similarity_threshold
        )

    def update_threshold_from_event(self, payload: Any) -> None:
        """Handle ``qpm.threshold_update`` events.

        Parameters
        ----------
        payload : Any
            Data published with the event. It may be a numeric value or a
            mapping containing ``"similarity_threshold"`` or ``"threshold"``.

        Notes
        -----
        Invalid payloads are ignored to preserve the current threshold.
        """

        value: Optional[float]

        if isinstance(payload, dict):
            raw = payload.get("similarity_threshold", payload.get("threshold"))
            value = float(raw) if raw is not None else None
        elif isinstance(payload, (int, float)):
            value = float(payload)
        else:
            value = None

        if value is None:
            logger.warning(
                "QPM.update_threshold_from_event: payload inválido %r", payload
            )
            return

        self.set_similarity_threshold(value)

    def _capture_memory_update_event(self, payload: Any) -> None:
        """Armazena eventos ``memory.update`` recebidos."""

        if not isinstance(payload, dict):
            return
        record = payload.copy()
        record.setdefault("timestamp", time.time())
        self.memory_update_history.append(record)

    @with_lock
    def store_pattern(
        self,
        quantum_signature_packet: QuantumSignaturePacket,
        market_snapshot: Dict[str, Any],
        outcome: Dict[str, Any],
        decision_context: Optional[Dict[str, Any]] = None,
        *,
        timestamp: Optional[float] = None,
        market_scenario: Optional[str] = None,
        extra_metadata: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Armazena um padrão quântico e seus metadados.

        Parameters
        ----------
        quantum_signature_packet : QuantumSignaturePacket
            Assinatura quântica a ser persistida.
        market_snapshot : dict
            Snapshot do mercado no momento da decisão.
        outcome : dict
            Resultado associado ao padrão (p. ex., PnL).
        decision_context : dict, optional
            Informações adicionais sobre a decisão original.
        timestamp : float, optional
            ``timestamp`` a ser gravado; ``time.time()`` quando ``None``.
        market_scenario : str, optional
            Rótulo para o cenário de mercado observado.
        extra_metadata : dict, optional
            Campos adicionais a serem armazenados junto ao padrão.
        metadata : dict, optional
            Metadados incrementais utilizados para importação/exportação.

        Returns
        -------
        bool
            ``True`` se o padrão foi armazenado com sucesso.

        Notes
        -----
        Quando ``async_safe=True`` o retorno é uma *coroutine* que deve ser
        aguardada. Chamar o método sem ``await`` resultará em ``TypeError``.
        """
        start_time = time.perf_counter()
        self._require_risk_manager()
        self._purge_expired()
        if not isinstance(quantum_signature_packet, QuantumSignaturePacket):
            logger.error(
                f"QPM.store_pattern: quantum_signature_packet não é do tipo QuantumSignaturePacket. Tipo recebido: {type(quantum_signature_packet)}"
            )
            if self.statsd:
                self.statsd.increment("qpm.store_pattern.error")
                self.statsd.timing(
                    "qpm.store_pattern_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return False

        packet_copy = (
            quantum_signature_packet.model_copy(deep=True)
            if hasattr(quantum_signature_packet, "model_copy")
            else copy.deepcopy(quantum_signature_packet)
        )

        dimension = self._get_vector_dimension(packet_copy)
        if dimension is None or dimension == 0:
            logger.error(
                f"QPM.store_pattern: Não foi possível determinar dimensão válida para o vetor do QuantumSignaturePacket. Vector: {packet_copy.vector}"
            )
            if self.statsd:
                self.statsd.increment("qpm.store_pattern.error")
                self.statsd.timing(
                    "qpm.store_pattern_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return False

        normalized_vector, new_dim = self._normalize_vector(
            packet_copy.vector, packet_copy.source_details or {}
        )
        if new_dim != dimension:
            logger.warning(
                "QPM.store_pattern: dimensão do vetor %s difere do esperado %s conforme source_details. Ajustando.",
                dimension,
                new_dim,
            )
        packet_copy.vector = normalized_vector
        dimension = new_dim

        signature_vector = packet_copy.vector
        if (
            not isinstance(signature_vector, (list, np.ndarray)) or not signature_vector
        ):  # Redundante se dimension é válido, mas seguro
            logger.error(
                f"QPM.store_pattern: O vetor dentro do QuantumSignaturePacket está vazio ou inválido. Vector: {signature_vector}"
            )
            if self.statsd:
                self.statsd.increment("qpm.store_pattern.error")
                self.statsd.timing(
                    "qpm.store_pattern_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return False

        if market_scenario is not None:
            market_snapshot = {**market_snapshot, "scenario_type": market_scenario}

        # Preserve the QuantumSignaturePacket ID as the pattern identifier so
        # tests and external components can reliably reference stored entries.
        pattern_id = packet_copy.id
        if pattern_id in self._id_index:
            logger.debug(
                "QPM.store_pattern: padrão %s já armazenado. Ignorando inserção.",
                pattern_id,
            )
            if self.statsd:
                self.statsd.increment("qpm.store_pattern.duplicate")
            return False

        # Verifica se o vetor já existe na mesma partição
        for existing in self.memory.get(dimension, []):
            existing_vec = None
            stored_obj = existing.get("qsp_obj")
            if stored_obj is not None:
                existing_vec = stored_obj.vector
            else:
                qsp_dict = existing.get("quantum_signature_packet")
                if isinstance(qsp_dict, dict):
                    existing_vec = qsp_dict.get("vector")
            if existing_vec is not None and np.array_equal(
                np.asarray(existing_vec, dtype=float),
                np.asarray(signature_vector, dtype=float),
            ):
                logger.debug(
                    "QPM.store_pattern: vetor idêntico já presente. Ignorando inserção de %s",
                    pattern_id,
                )
                if self.statsd:
                    self.statsd.increment("qpm.store_pattern.duplicate")
                return False

        pattern_data = {
            "id": pattern_id,
            "timestamp": timestamp or time.time(),
            "quantum_signature_packet": (
                packet_copy.model_dump()
                if hasattr(packet_copy, "model_dump")
                else packet_copy.dict()
            ),
            "qsp_obj": packet_copy,
            "vector_dim": dimension,
            "market_snapshot": convert_to_serializable(market_snapshot),
            "outcome": convert_to_serializable(outcome),
            "decision_context": convert_to_serializable(decision_context or {}),
            "extra_metadata": convert_to_serializable(extra_metadata or {}),
            "metadata": convert_to_serializable(metadata or {}),
            # "dimension": dimension  # Opcional: armazenar dimensão explicitamente se útil para depuração
        }

        if dimension not in self.memory:
            self.memory[dimension] = deque(maxlen=self.max_memory_size_per_dimension)

        span_cm = (
            trace.get_tracer(__name__).start_as_current_span(
                "memory.store_pattern", attributes={"dimension": dimension}
            )
            if trace
            else contextlib.nullcontext()
        )
        with span_cm:
            self.memory[dimension].append(pattern_data)
        self._id_index[pattern_id] = {
            "dimension": dimension,
            "timestamp": pattern_data["timestamp"],
            "market_scenario": pattern_data["market_snapshot"].get("scenario_type"),
            "extra_metadata": pattern_data.get("extra_metadata"),
            "metadata": pattern_data.get("metadata"),
            "pattern": pattern_data,
        }
        logger.debug(
            f"Padrão {pattern_id} (dim={dimension}) armazenado na QuantumPatternMemory. "
            f"Tamanho da partição [{dimension}D]: {len(self.memory[dimension])}. "
            f"Total de partições: {len(self.memory)}."
        )
        if self.event_bus and self.publish_memory_events:
            self.event_bus.publish(
                "memory.update",
                {"action": "store", "pattern_id": pattern_id, "dimension": dimension},
            )
        if self.statsd:
            self.statsd.increment("qpm.memory_update", tags=["action:store"])

        # Log distribution of similarity scores within this dimension after insertion
        try:
            partition_vecs = []
            for p in self.memory[dimension]:
                stored_obj = p.get("qsp_obj")
                if stored_obj is None:
                    qsp_dict = p.get("quantum_signature_packet", {})
                    try:
                        stored_obj = QuantumSignaturePacket(**qsp_dict)
                        p["qsp_obj"] = stored_obj
                    except (ValidationError, TypeError, ValueError) as e:
                        logger.debug(
                            "QPM.store_pattern: falha ao reconstruir QSP para calculo de distribuicao: %s",
                            e,
                            exc_info=True,
                        )
                        stored_obj = None
                    except Exception as exc:
                        logger.exception(
                            "QPM.store_pattern: erro inesperado ao reconstruir QSP para calculo de distribuicao",
                            exc_info=exc,
                        )
                        raise
                if stored_obj is not None:
                    partition_vecs.append(list(stored_obj.vector))
            sims_after_insert = self._vectorized_similarity(
                np.array(signature_vector, dtype=float),
                np.array(partition_vecs, dtype=float),
                metric=self.similarity_metric,
            )
            msg = (
                f"min={float(np.min(sims_after_insert)):.3f}, "
                f"max={float(np.max(sims_after_insert)):.3f}, "
                f"avg={float(np.mean(sims_after_insert)):.3f}, "
                f"std={float(np.std(sims_after_insert)):.3f}"
            )
            logger.debug(
                "QPM.store_pattern: distribuicao de similaridade apos insercao (pattern_id=%s, %sD) - %s",
                pattern_id,
                dimension,
                msg,
            )
        except (ValidationError, TypeError, ValueError) as e:
            logger.debug(
                "QPM.store_pattern: erro ao calcular distribuicao de similaridade para pattern_id=%s: %s",
                pattern_id,
                e,
                exc_info=True,
            )
        except Exception as exc:
            logger.exception(
                "QPM.store_pattern: erro inesperado ao calcular distribuicao de similaridade",
                exc_info=exc,
            )
            raise

        if self.auto_persist and self.persistence_path:
            self.save_to_file()
        if self.statsd:
            self.statsd.increment("qpm.store_pattern.success")
            self.statsd.timing(
                "qpm.store_pattern_ms",
                (time.perf_counter() - start_time) * 1000,
            )
        self.holographic_memory.store(
            packet_copy.vector,
            {"pattern_id": pattern_id},
            timestamp=timestamp or time.time(),
        )
        return True

    def retrieve_similar_patterns(
        self,
        current_signature_packet: QuantumSignaturePacket,
        top_n: int = 5,
        similarity_threshold: Optional[float] = None,
        *,
        adaptive: bool = False,
        min_threshold: Optional[float] = None,
        step: float = 0.05,
        since: Optional[float] = None,
        until: Optional[float] = None,
        market_scenario: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """Recupera padrões similares ao ``current_signature_packet``.

        Parameters
        ----------
        current_signature_packet : QuantumSignaturePacket
            Assinatura quântica de referência.
        top_n : int, default=5
            Número máximo de padrões a retornar.
        similarity_threshold : float, optional
            Limiar de similaridade requerido para inclusão no resultado.
        adaptive : bool, default=False
            Se ``True``, reduz o limiar até que ``top_n`` padrões sejam encontrados.
        min_threshold : float, optional
            Limiar mínimo quando ``adaptive`` está habilitado.
        step : float, default=0.05
            Passo de redução do limiar no modo adaptativo.

        Notes
        -----
        O limiar ativo é ajustado dinamicamente usando ``dynamic_similarity_threshold = max(0.05, 0.7 * top_score)``.
        since : float, optional
            Timestamp mínimo dos padrões considerados.
        until : float, optional
            Timestamp máximo dos padrões considerados.
        market_scenario : str, optional
            Filtra padrões cujo ``market_snapshot.scenario_type`` corresponde ao valor.

        Returns
        -------
        list of dict
            Padrões ordenados por ``similarity_score``. Pode ser vazio.

        Notes
        -----
        No modo ``async_safe=True`` este método retorna uma *coroutine* e deve
        ser chamado com ``await``. Invocar de forma síncrona gera ``TypeError``.
        """
        start_time = time.perf_counter()
        self._require_risk_manager()
        self._purge_expired()
        if not isinstance(current_signature_packet, QuantumSignaturePacket):
            logger.error(
                f"QPM.retrieve_similar_patterns: current_signature_packet não é do tipo QuantumSignaturePacket. Tipo: {type(current_signature_packet)}"
            )
            if self.statsd:
                self.statsd.increment("qpm.retrieve.error")
                self.statsd.timing(
                    "qpm.retrieve_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return []

        normalized_vector, new_dim = self._normalize_vector(
            current_signature_packet.vector,
            current_signature_packet.source_details or {},
        )
        if new_dim != len(current_signature_packet.vector):
            logger.warning(
                "QPM.retrieve_similar_patterns: dimensão do vetor %s difere do esperado %s. Ajustando.",
                len(current_signature_packet.vector),
                new_dim,
            )
            current_signature_packet.vector = normalized_vector

        current_dimension = self._get_vector_dimension(current_signature_packet)
        if current_dimension is None or current_dimension == 0:
            logger.warning(
                "QPM.retrieve_similar_patterns: Não foi possível determinar dimensão válida para o current_signature_packet. Não é possível buscar similares."
            )
            if self.statsd:
                self.statsd.increment("qpm.retrieve.error")
                self.statsd.timing(
                    "qpm.retrieve_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return []

        # Buscar apenas na partição de memória correspondente à dimensão do pacote atual
        relevant_memory_partition = self.memory.get(current_dimension)
        if not relevant_memory_partition:
            logger.debug(
                "QPM: Memória vazia ou sem padrões para a dimensão %sD.",
                current_dimension,
            )
            if self.enable_warmstart:
                logger.debug(
                    "QPM: Executando warm-start automático para a dimensão %sD.",
                    current_dimension,
                )
                self.warmstart_manager._perform_warmstart_for_dimension(
                    current_dimension
                )
                relevant_memory_partition = self.memory.get(current_dimension)
            if not relevant_memory_partition:
                logger.debug(
                    "QPM: Nenhum padrão disponível para a dimensão %sD mesmo após warm-start.",
                    current_dimension,
                )
                # Fallback to patterns from other dimensions
                relevant_memory_partition = [
                    p for patterns in self.memory.values() for p in patterns
                ]

        if since is not None or until is not None or market_scenario is not None:
            allowed_ids = {
                pid
                for pid, meta in self._id_index.items()
                if (
                    (
                        since is None
                        or (
                            meta.get("timestamp") is not None
                            and meta["timestamp"] >= since
                        )
                    )
                    and (
                        until is None
                        or (
                            meta.get("timestamp") is not None
                            and meta["timestamp"] <= until
                        )
                    )
                    and (
                        market_scenario is None
                        or meta.get("market_scenario") == market_scenario
                    )
                )
            }
            relevant_memory_partition = [
                p for p in relevant_memory_partition if p.get("id") in allowed_ids
            ]

        if similarity_threshold is None:
            base_threshold = self.similarity_threshold
        else:
            base_threshold = similarity_threshold
        if base_threshold is None:
            base_threshold = self._determine_similarity_threshold()
        active_similarity_threshold = base_threshold
        min_threshold = (
            self.low_similarity_threshold if min_threshold is None else min_threshold
        )

        candidate_scores: List[Tuple[float, Dict[str, Any]]] = []

        partition_same_dim = all(
            (p.get("vector_dim") or current_dimension) == current_dimension
            for p in relevant_memory_partition
        )

        query_vec = np.array(current_signature_packet.vector, dtype=float)
        query_norm = self._unit_normalize(query_vec).reshape(-1)

        if partition_same_dim:
            vectors: List[np.ndarray] = []
            pattern_refs: List[Dict[str, Any]] = []
            for stored_pattern_dict in relevant_memory_partition:
                stored_qsp_obj = stored_pattern_dict.get("qsp_obj")
                if stored_qsp_obj is None:
                    stored_qsp_dict = stored_pattern_dict.get(
                        "quantum_signature_packet"
                    )
                    if not stored_qsp_dict or not isinstance(stored_qsp_dict, dict):
                        logger.warning(
                            f"QPM: Padrão armazenado ID {stored_pattern_dict.get('id')} não contém um 'quantum_signature_packet' válido em formato dict. Pulando."
                        )
                        continue
                    try:
                        stored_qsp_obj = QuantumSignaturePacket(**stored_qsp_dict)
                        stored_pattern_dict["qsp_obj"] = stored_qsp_obj
                    except (ValidationError, TypeError, ValueError) as exc:
                        logger.warning(
                            "QPM.retrieve_similar_patterns: Erro ao reconstruir QuantumSignaturePacket do dict armazenado: %s. Padrão ID: %s",
                            exc,
                            stored_pattern_dict.get("id"),
                        )
                        continue
                pattern_id = stored_pattern_dict.get("id")
                norm_vec = self._norm_cache.get(pattern_id)
                if norm_vec is None:
                    norm_vec = self._unit_normalize(
                        np.array(stored_qsp_obj.vector, dtype=float)
                    ).reshape(-1)
                    self._norm_cache[pattern_id] = norm_vec
                vectors.append(norm_vec)
                pattern_refs.append(stored_pattern_dict)

            if vectors:
                mat = np.vstack(vectors)
                if self.use_ann and len(mat) >= self.ann_threshold:
                    n_neighbors = min(len(mat), max(top_n, 1) * 5)
                    nn = NearestNeighbors(
                        n_neighbors=n_neighbors, metric=self.similarity_metric
                    )
                    nn.fit(mat)
                    dist, idxs = nn.kneighbors(
                        query_norm.reshape(1, -1), n_neighbors=n_neighbors
                    )
                    if self.similarity_metric == "cosine":
                        sims = 1.0 - dist[0]
                    else:
                        sims = 1.0 / (1.0 + dist[0])
                    candidate_scores.extend(
                        [
                            (float(s), pattern_refs[i])
                            for s, i in zip(sims.tolist(), idxs[0].tolist())
                        ]
                    )
                else:
                    sims = _gpu_dot(mat, query_norm)
                    candidate_scores.extend(list(zip(sims.tolist(), pattern_refs)))
        else:
            groups: DefaultDict[int, List[Dict[str, Any]]] = defaultdict(list)
            for stored_pattern_dict in relevant_memory_partition:
                ts_val = stored_pattern_dict.get("timestamp")
                if since is not None and (ts_val is None or ts_val < since):
                    continue
                if until is not None and (ts_val is None or ts_val > until):
                    continue
                stored_qsp_obj = stored_pattern_dict.get("qsp_obj")
                if stored_qsp_obj is None:
                    stored_qsp_dict = stored_pattern_dict.get(
                        "quantum_signature_packet"
                    )
                    if not stored_qsp_dict or not isinstance(stored_qsp_dict, dict):
                        logger.warning(
                            f"QPM: Padrão armazenado ID {stored_pattern_dict.get('id')} não contém um 'quantum_signature_packet' válido em formato dict. Pulando."
                        )
                        continue
                    try:
                        stored_qsp_obj = QuantumSignaturePacket(**stored_qsp_dict)
                        stored_pattern_dict["qsp_obj"] = stored_qsp_obj
                    except (ValidationError, TypeError, ValueError) as exc:
                        logger.warning(
                            "QPM.retrieve_similar_patterns: Erro ao reconstruir QuantumSignaturePacket do dict armazenado: %s. Padrão ID: %s",
                            exc,
                            stored_pattern_dict.get("id"),
                        )
                        continue
                stored_dim = stored_pattern_dict.get(
                    "vector_dim"
                ) or self._get_vector_dimension(stored_qsp_obj)
                groups[stored_dim].append(stored_pattern_dict)

            for stored_dim, patterns in groups.items():
                if stored_dim == current_dimension:
                    continue
                key_model = (current_dimension, stored_dim)
                max_dim = max(current_dimension, stored_dim)
                padded_current = np.pad(
                    query_vec, (0, max_dim - current_dimension), constant_values=0.0
                ).reshape(1, -1)
                pca_model = self._pca_models.get(key_model)
                if pca_model is None:
                    vecs = np.array(
                        [
                            np.pad(
                                np.array(p["qsp_obj"].vector, dtype=float),
                                (0, max_dim - stored_dim),
                                constant_values=0.0,
                            )[:max_dim]
                            for p in patterns
                        ]
                    )
                    n_components = min(
                        self.pca_target_dim, current_dimension, stored_dim
                    )
                    try:
                        pca_model = PCA(n_components=n_components)
                        pca_model.fit(np.vstack([padded_current, vecs]))
                        self._pca_models[key_model] = pca_model
                    except (ValueError, RuntimeError) as e:
                        logger.warning(
                            "QPM.retrieve_similar_patterns: Erro ao alinhar dimensões via PCA: %s",
                            e,
                        )
                        pca_model = None

                if pca_model is not None:
                    query_proj = self._unit_normalize(
                        pca_model.transform(padded_current)[0]
                    ).reshape(-1)
                else:
                    query_proj = self._unit_normalize(
                        padded_current.reshape(-1)
                    ).reshape(-1)

                for p in patterns:
                    pattern_id = p.get("id")
                    cache_key = (pattern_id, current_dimension)
                    stored_proj = self._pca_cache.get(cache_key)
                    if stored_proj is None or pca_model is None:
                        vec = np.pad(
                            np.array(p["qsp_obj"].vector, dtype=float),
                            (0, max_dim - stored_dim),
                            constant_values=0.0,
                        )[:max_dim]
                        if pca_model is not None:
                            stored_proj = self._unit_normalize(
                                pca_model.transform(vec.reshape(1, -1))[0]
                            ).reshape(-1)
                        else:
                            stored_proj = self._unit_normalize(vec).reshape(-1)
                        self._pca_cache[cache_key] = stored_proj
                    score = float(
                        _gpu_dot(stored_proj.reshape(1, -1), query_proj).item()
                    )
                    candidate_scores.append((score, p))

        if candidate_scores:
            unique_map: Dict[Tuple[float, ...], Tuple[float, Dict[str, Any]]] = {}
            for score, entry in candidate_scores:
                qsp_entry = entry.get("qsp_obj")
                if qsp_entry is None:
                    qsp_dict = entry.get("quantum_signature_packet")
                    if isinstance(qsp_dict, dict):
                        try:
                            qsp_entry = QuantumSignaturePacket(**qsp_dict)
                            entry["qsp_obj"] = qsp_entry
                        except (ValidationError, TypeError, ValueError):
                            continue
                try:
                    vec_key = tuple(round(float(v), 8) for v in qsp_entry.vector)
                except Exception:
                    continue
                existing = unique_map.get(vec_key)
                if existing is None or score > existing[0]:
                    unique_map[vec_key] = (score, entry)
            candidate_scores = list(unique_map.values())

        scores_only = [score for score, _ in candidate_scores]
        auto_adjust = len(relevant_memory_partition) < self.warmstart_min_patterns
        adaptive_mode = adaptive or auto_adjust
        if scores_only:
            top_score = float(np.max(scores_only))
            if (similarity_threshold is None and adaptive_mode) or (
                similarity_threshold is not None and auto_adjust
            ):
                dynamic_similarity_threshold = max(0.05, 0.7 * top_score)
                active_similarity_threshold = max(
                    min(active_similarity_threshold, dynamic_similarity_threshold),
                    min_threshold,
                )
                if similarity_threshold is None:
                    min_threshold = min(min_threshold, dynamic_similarity_threshold)
            distribution_msg = (
                f"min={float(np.min(scores_only)):.3f}, "
                f"max={float(np.max(scores_only)):.3f}, "
                f"avg={float(np.mean(scores_only)):.3f}, "
                f"std={float(np.std(scores_only)):.3f}, "
                f"dyn={max(0.05, 0.7 * top_score):.3f}"
            )
        else:
            distribution_msg = "sem scores"
        logger.debug(
            "QPM.retrieve_similar_patterns: distribuicao de similaridade (query_id=%s, %sD) - %s",
            current_signature_packet.id,
            current_dimension,
            distribution_msg,
        )

        if candidate_scores:
            best_two = heapq.nlargest(2, candidate_scores, key=lambda x: x[0])
            for idx, (score, pattern) in enumerate(best_two, start=1):
                stored_vec = (
                    pattern.get("qsp_obj").vector if pattern.get("qsp_obj") else []
                )
                logger.debug(
                    "QPM: top%s score %.5f vetor_consulta=%s vetor_memoria=%s",
                    idx,
                    score,
                    current_signature_packet.vector[:10],
                    stored_vec[:10],
                )
        similarities_all = [
            {**pattern, "similarity_score": float(score)}
            for score, pattern in candidate_scores
        ]
        similarities = similarities_all

        required_hits = top_n if adaptive else 1

        while True:
            filtered = [
                s
                for s in similarities
                if s["similarity_score"] >= active_similarity_threshold
            ]
            if (
                not adaptive_mode
                or len(filtered) >= required_hits
                or active_similarity_threshold <= min_threshold
            ):
                similarities = filtered
                break
            active_similarity_threshold = max(
                active_similarity_threshold - step, min_threshold
            )

        for rejected in similarities_all:
            if rejected["similarity_score"] < active_similarity_threshold:
                logger.debug(
                    "QPM: padrão %s rejeitado (score %.4f < limiar %.2f)",
                    rejected.get("id"),
                    rejected["similarity_score"],
                    active_similarity_threshold,
                )

        if not similarities and (similarity_threshold is None or adaptive_mode):
            while (
                active_similarity_threshold > self.low_similarity_threshold
                and not similarities
            ):
                active_similarity_threshold = max(
                    active_similarity_threshold - step,
                    self.low_similarity_threshold,
                )
                similarities = [
                    s
                    for s in similarities_all
                    if s["similarity_score"] >= active_similarity_threshold
                ]

        results_count = len(similarities)
        if results_count == 0:
            logger.debug(
                "QPM.retrieve_similar_patterns: nenhum padrao acima do limiar (query_id=%s)",
                current_signature_packet.id,
            )
            logger.info(
                "QPM.retrieve_similar_patterns: query_id=%s dim=%sD encontrados=%s limiar_final=%.2f avaliados=%s",
                current_signature_packet.id,
                current_dimension,
                results_count,
                active_similarity_threshold,
                len(relevant_memory_partition),
            )
            if self.statsd:
                self.statsd.increment("qpm.retrieve.none")
                self.statsd.timing(
                    "qpm.retrieve_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return []

        logger.debug(
            "Recuperados %s padrões com similaridade >= %.2f para a dimensão %sD (query_id=%s)",
            results_count,
            active_similarity_threshold,
            current_dimension,
            current_signature_packet.id,
        )
        logger.info(
            "QPM.retrieve_similar_patterns: query_id=%s dim=%sD encontrados=%s limiar_final=%.2f avaliados=%s",
            current_signature_packet.id,
            current_dimension,
            results_count,
            active_similarity_threshold,
            len(relevant_memory_partition),
        )
        from ..metacognition import service as metacog_service

        focus = metacog_service.get_focus()
        for s in similarities:
            s["priority_score"] = s["similarity_score"] * (1.0 + focus)
        results = heapq.nlargest(
            top_n,
            similarities,
            key=lambda x: x["priority_score"],
        )
        if self.statsd:
            self.statsd.increment("qpm.retrieve.success")
            self.statsd.timing(
                "qpm.retrieve_ms",
                (time.perf_counter() - start_time) * 1000,
            )
        return results

    def get_memory_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas sobre a memória, incluindo o número de padrões por dimensão.
        """
        self._purge_expired()
        stats = {
            "total_patterns": sum(len(part) for part in self.memory.values()),
            "similarity_threshold": self.similarity_threshold,
            "low_similarity_threshold": self.low_similarity_threshold,
            "low_threshold_max_patterns": self.low_threshold_max_patterns,
            "max_memory_size_per_dimension": self.max_memory_size_per_dimension,
            "persistence_path": self.persistence_path,
            "num_dimensions_stored": len(self.memory),
            "patterns_per_dimension": {
                dim: len(patterns) for dim, patterns in self.memory.items()
            },
        }
        return stats

    def get_memory_update_history(self) -> List[Dict[str, Any]]:
        """Retorna o histórico de eventos ``memory.update``."""

        return list(self.memory_update_history)

    def query_patterns(
        self,
        *,
        start_ts: Optional[float] = None,
        end_ts: Optional[float] = None,
        market_scenario: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """Retorna padrões que atendem aos filtros especificados."""
        self._purge_expired()

        results: List[Dict[str, Any]] = []
        for patterns in self.memory.values():
            for pattern in patterns:
                ts_val = pattern.get("timestamp")
                if start_ts is not None and (ts_val is None or ts_val < start_ts):
                    continue
                if end_ts is not None and (ts_val is None or ts_val > end_ts):
                    continue
                if (
                    market_scenario is not None
                    and pattern.get("market_snapshot", {}).get("scenario_type")
                    != market_scenario
                ):
                    continue
                results.append(pattern)
        return results

    def close(self) -> None:
        """Persist memory to disk if a persistence path is configured."""

        if self.persistence_path:
            self.save_to_file()

    # Context manager support
    def __enter__(self) -> "QuantumPatternMemory":
        return self

    def __exit__(
        self, exc_type: Optional[type], exc: Optional[BaseException], tb: Any
    ) -> None:
        self.close()

    @with_lock
    def clear_dimension_partition(self, dimension: int) -> bool:
        """
        Limpa todos os padrões armazenados para uma dimensionalidade específica.

        Args:
            dimension: A dimensionalidade da partição a ser limpa.

        Returns:
            True se a partição foi encontrada e limpa, False caso contrário.

        Notes
        -----
        Com ``async_safe=True`` esta chamada retorna uma *coroutine* e precisa
        ser aguardada.
        """
        if dimension in self.memory:
            num_cleared = len(self.memory[dimension])
            for p in self.memory[dimension]:
                pid = p.get("id")
                if pid is not None:
                    self._id_index.pop(pid, None)
            self.memory[dimension] = deque(maxlen=self.max_memory_size_per_dimension)
        else:
            num_cleared = None

        if num_cleared is not None:
            logger.info(
                f"QPM: Partição de memória para dimensão {dimension}D limpa ({num_cleared} padrões removidos)."
            )
            if self.auto_persist and self.persistence_path:
                self.save_to_file()
            return True
        logger.info(
            f"QPM: Nenhuma partição de memória encontrada para dimensão {dimension}D para limpar."
        )
        return False

    @with_lock
    def clear_all_memory(self) -> None:
        """Limpa toda a memória, todas as partições.

        Notes
        -----
        Em modo ``async_safe=True`` este método precisa ser aguardado com
        ``await``.
        """
        num_partitions_cleared = len(self.memory)
        total_patterns_cleared = sum(len(p_list) for p_list in self.memory.values())
        self.memory = {}
        self._id_index.clear()
        logger.info(
            f"QPM: Toda a memória limpa. {total_patterns_cleared} padrões em {num_partitions_cleared} partições removidos."
        )
        if self.auto_persist and self.persistence_path:
            self.save_to_file()

    # ------------------------------------------------------------------
    # BaseMemory interface compliance

    def store(
        self,
        key: str,
        value: Any,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Store a pattern using the BaseMemory interface."""
        timestamp = None
        market_scenario = None

        if isinstance(value, QuantumSignaturePacket):
            packet = value
            market_snapshot = metadata.get("market_snapshot", {}) if metadata else {}
            outcome = metadata.get("outcome", {}) if metadata else {}
            decision_context = metadata.get("decision_context") if metadata else None
            timestamp = metadata.get("timestamp") if metadata else None
            market_scenario = metadata.get("market_scenario") if metadata else None
            extra_metadata = metadata.get("extra_metadata") if metadata else None
            incremental_metadata = metadata.get("metadata") if metadata else None
        elif isinstance(value, dict) and "quantum_signature_packet" in value:
            packet = value["quantum_signature_packet"]
            market_snapshot = value.get("market_snapshot", {})
            outcome = value.get("outcome", {})
            decision_context = value.get("decision_context")
            timestamp = value.get("timestamp")
            market_scenario = value.get("market_scenario")
            extra_metadata = value.get("extra_metadata")
            incremental_metadata = value.get("metadata")
        else:
            raise ValueError("value must contain a QuantumSignaturePacket")

        self.store_pattern(
            packet,
            market_snapshot=market_snapshot,
            outcome=outcome,
            decision_context=decision_context,
            timestamp=timestamp,
            market_scenario=market_scenario,
            extra_metadata=extra_metadata,
            metadata=incremental_metadata,
        )

    @with_lock
    def retrieve(self, key: str) -> Optional[Dict[str, Any]]:
        """Retrieve a stored pattern by its identifier.

        Notes
        -----
        Quando ``async_safe=True`` este método retorna uma *coroutine* que deve
        ser aguardada.
        """
        self._purge_expired()

        found: Optional[Dict[str, Any]] = None
        dimension = 0
        vector_size = 0

        for dim, patterns in self.memory.items():
            for pattern in patterns:
                if pattern.get("id") == key:
                    found = pattern
                    dimension = dim
                    vector_size = pattern.get("vector_dim", 0)
                    qsp = pattern.get("quantum_signature_packet", {})
                    vec = qsp.get("vector")
                    if vec is not None:
                        vector_size = len(vec)
                    break

        span_cm = (
            trace.get_tracer(__name__).start_as_current_span(
                "memory.retrieve_pattern",
                attributes={
                    "dimension": dimension,
                    "vector_size": vector_size,
                },
            )
            if trace
            else contextlib.nullcontext()
        )
        with span_cm:
            return found

    @with_lock
    def delete(self, key: str) -> bool:
        """Delete a pattern by its identifier.

        Notes
        -----
        Se ``async_safe=True`` o método retorna uma *coroutine* e deve ser
        aguardado com ``await``.
        """

        for dim, patterns in self.memory.items():
            for idx, pattern in enumerate(patterns):
                if pattern.get("id") == key:
                    del patterns[idx]
                    return True
        return False

    @with_lock
    def remove_pattern(self, pattern_id: str) -> bool:
        """Remove a pattern and emit update event.

        Notes
        -----
        Quando ``async_safe=True`` o retorno é uma *coroutine* e deve ser
        utilizado com ``await``.
        """

        start_time = time.perf_counter()
        self._require_risk_manager()
        for dim, patterns in self.memory.items():
            for idx, pattern in enumerate(patterns):
                if pattern.get("id") == pattern_id:
                    del patterns[idx]
                    self._id_index.pop(pattern_id, None)
                    if self.event_bus and self.publish_memory_events:
                        self.event_bus.publish(
                            "memory.update",
                            {
                                "action": "remove",
                                "pattern_id": pattern_id,
                                "dimension": dim,
                            },
                        )
                    if self.statsd:
                        self.statsd.increment("qpm.remove_pattern.success")
                        self.statsd.timing(
                            "qpm.remove_pattern_ms",
                            (time.perf_counter() - start_time) * 1000,
                        )
                    return True
        if self.statsd:
            self.statsd.increment("qpm.remove_pattern.miss")
            self.statsd.timing(
                "qpm.remove_pattern_ms",
                (time.perf_counter() - start_time) * 1000,
            )
        return False

    @with_lock
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """Return pattern identifiers currently stored.

        Notes
        -----
        Retorna uma *coroutine* quando ``async_safe=True`` e precisa ser
        utilizada com ``await``.
        """
        self._purge_expired()

        keys: List[str] = []
        for patterns in self.memory.values():
            keys.extend(p.get("id") for p in patterns if p.get("id"))

        if prefix:
            keys = [k for k in keys if k.startswith(prefix)]
        return keys

    def clear(self) -> None:
        """Remove all stored patterns."""

        self.clear_all_memory()

    def get_info(self) -> Dict[str, Any]:
        """Return summary information about the memory."""

        info = self.get_memory_stats()
        info["type"] = "QuantumPatternMemory"
        return info

    def retrieve_historical_patterns(
        self, vector: List[float] | np.ndarray, top_n: int = 5
    ) -> List[Dict[str, Any]]:
        """Consulta a :class:`HolographicMemory` por vetores similares."""

        return self.holographic_memory.query(
            np.asarray(vector, dtype=float), top_n=top_n
        )

    def _collect_candidate_scores(
        self, query_norm: np.ndarray
    ) -> List[Tuple[float, Dict[str, Any]]]:
        """Return similarity scores for all stored patterns using NumPy."""

        from itertools import chain

        entries: List[Dict[str, Any]] = []
        vectors: List[np.ndarray] = []

        for entry in chain.from_iterable(self.memory.values()):
            qsp_obj = entry.get("qsp_obj")
            if qsp_obj is None:
                qsp_dict = entry.get("quantum_signature_packet")
                if isinstance(qsp_dict, dict):
                    try:
                        qsp_obj = QuantumSignaturePacket(**qsp_dict)
                        entry["qsp_obj"] = qsp_obj
                    except (ValidationError, TypeError, ValueError, KeyError):
                        continue
            if qsp_obj is None:
                continue

            vec = np.asarray(qsp_obj.vector, dtype=float).reshape(-1)
            if vec.size == 0:
                continue
            entries.append(entry)
            vectors.append(vec)

        if not vectors:
            return []

        mat = np.vstack(vectors)
        stored_norms = self._unit_normalize(mat)
        scores = _gpu_dot(stored_norms, query_norm)
        return list(zip(scores.tolist(), entries))

    def recall_similar_patterns(
        self, current_vector: List[float] | np.ndarray, top_n: int = 5
    ) -> List[Dict[str, Any]]:
        """Retorna os padrões mais similares ao ``current_vector``.

        Parameters
        ----------
        current_vector : list or ndarray
            Vetor do estado quântico atual a ser comparado.
        top_n : int, optional
            Número máximo de padrões retornados, por padrão ``5``.

        Returns
        -------
        list of dict
            Cada item contém ``similarity_score`` e o ``pattern`` original.
        """

        if not current_vector:
            return []

        query = np.asarray(current_vector, dtype=float).reshape(-1)
        if query.size == 0:
            return []

        query_norm = self._unit_normalize(query)[0]
        candidates = self._collect_candidate_scores(query_norm)

        candidates.sort(key=lambda x: x[0], reverse=True)
        results = [
            {"similarity_score": score, "pattern": entry}
            for score, entry in candidates[:top_n]
            if np.isfinite(score)
        ]
        return results

    # Delegated helpers -------------------------------------------------

    def warm_start(self, risk_manager: Optional[QUALIARiskManagerBase] = None) -> None:
        """Public wrapper for :class:`WarmStartManager`.

        Parameters
        ----------
        risk_manager : QUALIARiskManagerBase, optional
            Instance required to store synthetic patterns. When provided, it
            becomes the active manager for this memory instance before the
            warm-start workflow executes.
        """

        if risk_manager is not None:
            self.risk_manager = risk_manager

        self._require_risk_manager()
        self.warmstart_manager.warm_start()

    def _create_synthetic_pattern(
        self, config: Dict[str, Any], variation_index: int
    ) -> Optional[float]:
        """Expose synthetic pattern creation for testing purposes.

        This simply delegates to :class:`WarmStartManager` while keeping the
        randomness isolated from the global NumPy RNG.
        """

        return self.warmstart_manager._create_synthetic_pattern(config, variation_index)

    def save_to_file(self, file_path: Optional[str] = None) -> bool:
        """Persiste a memória atual em um arquivo JSON, incluindo metadados de versão."""
        path = Path(file_path or self.persistence_path)
        logger.debug(f"QPM: Salvando memória em '{path}'...")
        try:
            # Estrutura de dados para o arquivo JSON
            memory_dump = {
                "metadata": {
                    "vector_dimension": self.pca_target_dim,
                    "saved_at": time.strftime("%Y-%m-%dT%H:%M:%S%z"),
                    "total_patterns": sum(len(p) for p in self.memory.values()),
                },
                "patterns": self.memory,
            }
            
            # Garante que o diretório exista
            path.parent.mkdir(parents=True, exist_ok=True)

            with open(path, "w", encoding="utf-8") as f:
                # Usa o utilitário para garantir que tudo seja serializável
                serializable_dump = convert_to_serializable(memory_dump)
                json.dump(serializable_dump, f, indent=4)
            
            logger.info(f"QPM: Memória com {memory_dump['metadata']['total_patterns']} padrões salva em '{path}'.")
            return True
        except (IOError, TypeError, ValueError) as e:
            logger.error(f"QPM: Falha ao salvar a memória em '{path}': {e}", exc_info=True)
            return False

    def load_from_file(self, file_path: Optional[str] = None) -> bool:
        """Carrega a memória de um arquivo JSON, com verificação de compatibilidade de dimensão."""
        path = Path(file_path or self.persistence_path)
        if not path.exists():
            logger.warning(f"QPM: Arquivo de persistência '{path}' não encontrado. Iniciando com memória vazia.")
            return False

        logger.debug(f"QPM: Carregando memória de '{path}'...")
        try:
            with open(path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Verifica se é o novo formato com metadados
            if isinstance(data, dict) and "metadata" in data and "patterns" in data:
                metadata = data.get("metadata", {})
                saved_dimension = metadata.get("vector_dimension")
                current_dimension = self.pca_target_dim

                if saved_dimension is not None and saved_dimension != current_dimension:
                    logger.warning(
                        f"QPM: INCOMPATIBILIDADE DE DIMENSÃO DETECTADA! "
                        f"Memória salva tem dimensão {saved_dimension}, mas a configuração atual é {current_dimension}. "
                        f"A memória persistida será invalidada para prevenir corrupção de dados."
                    )
                    self.clear_all_memory()
                    return False

                patterns_data = data["patterns"]
                logger.info(f"QPM: Arquivo de memória compatível (dimensão: {saved_dimension}) carregado.")
            else:
                # Formato antigo (legado)
                logger.warning(
                    "QPM: Carregando arquivo de memória em formato legado (sem metadados de dimensão). "
                    "Se ocorrerem erros de 'shape mismatch', apague o arquivo de memória."
                )
                patterns_data = data

            # Reconstrói a memória com deques
            self.memory.clear()
            for dim_str, patterns_list in patterns_data.items():
                dim = int(dim_str)
                self.memory[dim] = deque(patterns_list, maxlen=self.max_memory_size_per_dimension)
            
            self.reindex_memory()
            return True

        except (IOError, json.JSONDecodeError, TypeError, ValueError) as e:
            logger.error(f"QPM: Falha ao carregar ou processar o arquivo de memória '{path}': {e}", exc_info=True)
            self.clear_all_memory()
            return False

    def reindex_memory(self) -> None:
        """Recria o índice de IDs de padrões a partir da memória carregada."""
        self._id_index.clear()

    def import_patterns_from_backtest(self, file_path: str) -> int:
        """Import patterns from a JSON backtest file."""

        return self.persistence.import_patterns_from_backtest(file_path)

    def get_warmstart_stats(self) -> Dict[str, Any]:
        """Expose warm-start statistics."""

        return self.warmstart_manager.get_warmstart_stats()

    def _on_market_pattern(self, payload: Any) -> None:
        """Handle ``market.pattern_detected`` events."""
        from qualia.market.event_bus import MarketPatternDetected

        if isinstance(payload, MarketPatternDetected):
            vector = payload.vector
            metadata = payload.metadata or {}
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metadata = payload.get("metadata", {})
        else:
            vector = getattr(payload, "vector", None)
            metadata = getattr(payload, "metadata", {}) or {}
        if vector is None:
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics={})
        try:
            self.store_pattern(
                packet,
                metadata.get("market_snapshot", {}),
                metadata.get("outcome", {}),
                metadata.get("decision_context"),
                extra_metadata=metadata.get("extra_metadata"),
            )
        except Exception:  # pragma: no cover - safeguard
            logger.exception("QPM: erro ao registrar padrão recebido")

    def subscribe_to_event_bus(
        self, event_bus: "SimpleEventBus", event_name: str = "retrieve_patterns"
    ) -> None:
        """Subscribe to an event bus to handle external retrieval requests.

        Parameters
        ----------
        event_bus : SimpleEventBus
            Bus instance used for communication.
        event_name : str, optional
            Name of the event that will trigger pattern retrieval.
        """

        def _handler(payload: Dict[str, Any]) -> None:
            packet = payload.get("query_packet")
            if not isinstance(packet, QuantumSignaturePacket):
                return
            top_n = int(payload.get("top_n", 5))
            results = self.retrieve_similar_patterns(packet, top_n=top_n)
            callback = payload.get("callback")
            if callable(callback):
                callback(results)

        event_bus.subscribe(event_name, _handler)

    def subscribe_to_async_event_bus(
        self, event_bus: "AsyncEventBus", event_name: str = "retrieve_patterns"
    ) -> None:
        """Subscribe to an :class:`AsyncEventBus` for async retrieval requests."""

        async def _handler(payload: Dict[str, Any]) -> None:
            packet = payload.get("query_packet")
            if not isinstance(packet, QuantumSignaturePacket):
                return
            top_n = int(payload.get("top_n", 5))
            results = self.retrieve_similar_patterns(packet, top_n=top_n)
            callback = payload.get("callback")
            if callable(callback):
                if asyncio.iscoroutinefunction(callback):
                    await callback(results)
                else:
                    callback(results)

        event_bus.subscribe(event_name, _handler)

    async def query_via_async_event_bus(
        self,
        event_bus: "AsyncEventBus",
        query_packet: QuantumSignaturePacket,
        *,
        top_n: int = 5,
        timeout: float = 1.0,
        event_name: str = "retrieve_patterns",
    ) -> List[Dict[str, Any]]:
        """Send a retrieval request through ``event_bus`` and await results."""

        loop = asyncio.get_event_loop()
        future: asyncio.Future[List[Dict[str, Any]]] = loop.create_future()

        async def _callback(res: List[Dict[str, Any]]) -> None:
            if not future.done():
                future.set_result(res)

        payload = {
            "query_packet": query_packet,
            "top_n": top_n,
            "callback": _callback,
        }
        event_bus.publish(event_name, payload)
        try:
            return await asyncio.wait_for(future, timeout)
        except asyncio.TimeoutError:
            logger.warning(
                "QPM.query_via_async_event_bus: timed out waiting for response"
            )
            return []


# Exemplo de uso futuro ou para testes:
# with QuantumPatternMemory(persistence_path="qpm_memory.json") as qpm:
#     qpm.store_pattern(...)
#     similar = qpm.retrieve_similar_patterns(...)
