"""Shared risk profile key definitions used across configuration modules."""

from __future__ import annotations

from typing import Any, Dict, Sequence, Set

# Keys required when loading a profile via :class:`ConfigManager`.
MANDATORY_RISK_PROFILE_KEYS: Sequence[str] = (
    "max_drawdown_pct",
    "risk_per_trade_pct",
    "max_position_size_pct",
    "max_daily_loss_pct",
    "max_open_positions",
)

# Parameters that every risk profile must provide for validation.
REQUIRED_PARAMETERS: Set[str] = {
    "position_sizing_mode",
    "max_position_percentage",
    "stop_loss_percentage",
    "take_profit_percentage",
    "max_open_positions",
    "enable_trailing_stop",
    "enable_dynamic_position_sizing",
}

# Optional parameters and their default values.
OPTIONAL_PARAMETERS: Dict[str, Any] = {
    "quantum_sensitivity_boost": 1.0,
    "max_drawdown_pct": 10.0,
    "risk_per_trade_pct": 1.0,
    "max_daily_loss_pct": 5.0,
    "cooling_period_minutes": 30,
    "stop_loss_adjustment": 0.8,
    "min_lot_size": 0.0001,
}

__all__ = [
    "MANDATORY_RISK_PROFILE_KEYS",
    "REQUIRED_PARAMETERS",
    "OPTIONAL_PARAMETERS",
]
