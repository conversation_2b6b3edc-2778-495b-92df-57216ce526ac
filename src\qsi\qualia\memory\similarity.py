"""Shared utilities for similarity calculations used across memory modules."""

from __future__ import annotations

import math
import os
import importlib.util
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity  # type: ignore

from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger
from ..utils.signature_utils import expected_signature_dimension

logger = get_logger(__name__)

_CUPY_AVAILABLE = importlib.util.find_spec("cupy") is not None
try:
    _AER_AVAILABLE = importlib.util.find_spec("qiskit_aer") is not None
except ValueError:  # pragma: no cover - defensive for partial installations
    _AER_AVAILABLE = False

# Threshold computation tuning constants
_BASE_COUNT = 300
_DEFAULT_THRESHOLD = 0.25
_LOG_SCALE = 0.3
_UPPER_BOUND = 0.95


class SimilarityMixin:
    """Utility methods for similarity calculations and vector handling."""

    if TYPE_CHECKING:
        memory: Dict[int, Any]

    def _determine_similarity_threshold(self) -> float:
        """Return an adaptive similarity threshold.

        Returns
        -------
        float
            Valor de ``similarity_threshold`` entre ``0`` e ``1``.
        """
        total_patterns = sum(len(p_list) for p_list in self.memory.values())

        if total_patterns <= _BASE_COUNT:
            return _DEFAULT_THRESHOLD

        computed = _DEFAULT_THRESHOLD + _LOG_SCALE * math.log10(
            total_patterns / _BASE_COUNT
        )
        return float(min(max(computed, _DEFAULT_THRESHOLD), _UPPER_BOUND))

    def _get_vector_dimension(
        self, sig_packet: QuantumSignaturePacket
    ) -> Optional[int]:
        """Return the dimension of ``sig_packet``'s vector.

        Parameters
        ----------
        sig_packet : QuantumSignaturePacket
            Pacote a ser inspecionado.

        Returns
        -------
        int or None
            Dimensão detectada ou ``None`` quando indisponível.
        """
        if not sig_packet or getattr(sig_packet, "vector", None) is None:
            return None
        try:
            vec = np.asarray(sig_packet.vector)
            if vec.ndim == 1:
                return int(vec.size)
            if vec.ndim == 2 and vec.shape[0] == 1:
                return int(vec.shape[1])
            logger.warning(
                "QPM._get_vector_dimension: Forma inesperada %s para o pacote.",
                vec.shape,
            )
            return None
        except (TypeError, ValueError, AttributeError) as exc:
            logger.error(
                "QPM._get_vector_dimension: falha ao determinar dimensão: %s",
                exc,
                exc_info=True,
            )
            return None

    def _normalize_vector(
        self, vector: Union[List[float], np.ndarray], source_details: Dict[str, Any]
    ) -> Tuple[List[float], int]:
        """Ajusta o vetor para a dimensão esperada e normaliza.

        Parameters
        ----------
        vector : list or ndarray
            Vetor original de características.
        source_details : dict
            Metadados utilizados para inferir a dimensão esperada.

        Returns
        -------
        tuple(list[float], int)
            Vetor ajustado e sua dimensão final.
        """

        expected_len = expected_signature_dimension(source_details)
        vec_list = list(vector) if isinstance(vector, (list, np.ndarray)) else []

        if expected_len is not None:
            vec_list = vec_list[:expected_len] + [0.0] * max(
                0, expected_len - len(vec_list)
            )
        length = expected_len or len(vec_list)

        if vec_list:
            use_gpu = os.getenv("QUALIA_USE_GPU_MEMORY", "false").lower() in {
                "1",
                "true",
                "yes",
                "on",
            }
            if use_gpu:
                try:
                    if _CUPY_AVAILABLE:
                        import cupy as cp

                        arr = cp.asarray(vec_list, dtype=cp.float32)
                        norm = cp.linalg.norm(arr)
                        if float(norm) > 1e-9:
                            arr = arr / norm
                        vec_list = cp.asnumpy(arr).tolist()
                    elif _AER_AVAILABLE:
                        from qiskit.quantum_info import Statevector

                        arr = np.asarray(vec_list, dtype=float)
                        if arr.size > 0:
                            sv = Statevector(arr)
                            vec_list = sv.data.real.tolist()
                except Exception as exc:  # pragma: no cover - defensive
                    logger.warning(
                        "_normalize_vector: falha no caminho GPU, retornando ao CPU: %s",
                        exc,
                    )
                    arr = np.asarray(vec_list, dtype=float)
                    norm = float(np.linalg.norm(arr))
                    if norm > 1e-9:
                        arr /= norm
                    vec_list = arr.tolist()
            else:
                arr = np.asarray(vec_list, dtype=float)
                norm = float(np.linalg.norm(arr))
                if norm > 1e-9:
                    arr /= norm
                vec_list = arr.tolist()

        return vec_list, length

    def _unit_normalize(self, vectors: np.ndarray) -> np.ndarray:
        """Return L2-normalized ``vectors``.

        Parameters
        ----------
        vectors : ndarray
            Vector or matrix to normalize. A one-dimensional array is treated
            as a single row.

        Returns
        -------
        ndarray
            Normalized array with the same shape as the input. Zero vectors
            remain unchanged to avoid numerical issues.

        Notes
        -----
        Quando ``QUALIA_USE_GPU_MEMORY`` estiver habilitado e CuPy ou
        ``qiskit-aer`` estiverem disponíveis, a normalização será executada no
        dispositivo de GPU.
        """

        if vectors.ndim == 1:
            vectors = vectors.reshape(1, -1)

        use_gpu = os.getenv("QUALIA_USE_GPU_MEMORY", "false").lower() in {
            "1",
            "true",
            "yes",
            "on",
        }
        if use_gpu:
            try:
                if _CUPY_AVAILABLE:
                    import cupy as cp

                    arr = cp.asarray(vectors, dtype=cp.float32)
                    norms = cp.linalg.norm(arr, axis=1, keepdims=True)
                    norms[norms < 1e-12] = 1.0
                    arr = arr / norms
                    return cp.asnumpy(arr)
                elif _AER_AVAILABLE:
                    from qiskit.quantum_info import Statevector

                    arr = np.asarray(vectors, dtype=float)
                    rows = []
                    for row in arr:
                        sv = Statevector(row)
                        rows.append(sv.data.real)
                    return np.vstack(rows)
            except Exception as exc:  # pragma: no cover - defensive
                logger.warning(
                    "_unit_normalize: falha no caminho GPU, usando CPU: %s",
                    exc,
                )

        arr = np.asarray(vectors, dtype=float)
        norms = np.linalg.norm(arr, axis=1, keepdims=True)
        norms[norms < 1e-12] = 1.0
        return arr / norms

    def _calculate_similarity(
        self, sig_packet_1: QuantumSignaturePacket, sig_packet_2: QuantumSignaturePacket
    ) -> float:
        """Return cosine similarity between two packets.

        Parameters
        ----------
        sig_packet_1, sig_packet_2 : QuantumSignaturePacket
            Pacotes de referência a serem comparados.

        Returns
        -------
        float
            Valor de similaridade no intervalo ``[0, 1]``.
        """
        if not isinstance(sig_packet_1, QuantumSignaturePacket) or not isinstance(
            sig_packet_2, QuantumSignaturePacket
        ):
            logger.warning(
                "QPM._calculate_similarity: Um ou ambos os inputs nao sao QuantumSignaturePacket."
            )
            return 0.0

        try:
            vec1 = np.asarray(sig_packet_1.vector, dtype=float)
            vec2 = np.asarray(sig_packet_2.vector, dtype=float)
        except (
            TypeError,
            ValueError,
            AttributeError,
        ) as exc:  # pragma: no cover - defensive
            logger.error(
                "QPM._calculate_similarity: erro ao converter vetores: %s",
                exc,
                exc_info=True,
            )
            return 0.0

        if vec1.size == 0 or vec2.size == 0:
            return 0.0
        if np.allclose(vec1, 0) and np.allclose(vec2, 0):
            return 1.0
        if vec1.shape != vec2.shape:
            logger.warning(
                "QPM._calculate_similarity: Formas incompatíveis %s e %s.",
                vec1.shape,
                vec2.shape,
            )
            return 0.0

        try:
            v1 = self._unit_normalize(vec1.reshape(1, -1))[0]
            v2 = self._unit_normalize(vec2.reshape(1, -1))[0]
            similarity = float(np.dot(v1, v2))
            return 0.0 if not np.isfinite(similarity) else similarity
        except Exception as exc:  # pragma: no cover - defensive
            logger.exception(
                "QPM._calculate_similarity: falha no cálculo", exc_info=exc
            )
            if hasattr(self, "statsd") and self.statsd:
                self.statsd.increment("qpm.calculate_similarity.error")
            raise

    def _vectorized_similarity(
        self,
        current_vector: np.ndarray,
        stored_vectors: np.ndarray,
        *,
        metric: str = "cosine",
    ) -> np.ndarray:
        """Return similarity scores between ``current_vector`` and ``stored_vectors``.

        Parameters
        ----------
        current_vector : ndarray
            Vetor de consulta.
        stored_vectors : ndarray
            Vetores armazenados em memória.
        metric : {"cosine", "euclidean"}, default "cosine"
            Métrica utilizada para calcular a similaridade.

        Returns
        -------
        ndarray
            Array de similaridade onde valores maiores indicam maior proximidade.
        """
        if current_vector.ndim == 1:
            current_vector = current_vector.reshape(1, -1)
        if stored_vectors.ndim == 1:
            stored_vectors = stored_vectors.reshape(1, -1)

        current_norm = self._unit_normalize(np.asarray(current_vector, dtype=float))
        stored_norm = self._unit_normalize(np.asarray(stored_vectors, dtype=float))

        if metric == "cosine":
            sims = cosine_similarity(stored_norm, current_norm).flatten()
            sims[np.isnan(sims)] = 0.0
            return sims

        if metric == "euclidean":
            diff = stored_norm - current_norm
            dist = np.linalg.norm(diff, axis=1)
            sims = 1.0 / (1.0 + dist)
            sims[np.isnan(sims)] = 0.0
            return sims

        logger.warning("QPM._vectorized_similarity: métrica desconhecida %s", metric)
        sims = cosine_similarity(stored_norm, current_norm).flatten()
        sims[np.isnan(sims)] = 0.0
        return sims


__all__ = ["SimilarityMixin"]
