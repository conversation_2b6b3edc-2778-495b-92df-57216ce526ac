"""<PERSON><PERSON><PERSON>lo de algoritmos quânticos do QUALIA.

Este módulo define a classe :class:`QUALIAQuantumUniverse` usada em todo o
projeto. As rotinas de criação e execução de circuitos assumem que os registros
quânticos começam no estado computacional ``|0...0>``. Quando nenhum
``initial_encoded_circuit`` é fornecido, o auxiliar ``_create_initial_state``
aplica a preparação diretamente nesse estado base (por exemplo, a opção
``"qft"`` executa a Transformada de Fourier Quântica em ``|0\rangle``).

Fluxos de trabalho que necessitem de um estado pré-inicializado diferente devem
fornecer um circuito personalizado via ``initial_encoded_circuit`` para que a
preparação desejada seja acrescentada antes da execução dos passos do universo.
"""

# ruff: noqa

from __future__ import annotations

import asyncio

# Standard library imports
import os
import time
import threading
import json
import logging
import uuid
from ..utils.logger import get_logger
from ..risk_management.risk_manager_builder import create_risk_manager
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
import math
import inspect
from collections import deque, OrderedDict

from typing import Hashable

# Third-party imports
import numpy as np
from scipy.stats import pearsonr
import sympy
from sympy import symbols, diff, integrate, count_ops, degree, ceiling, log

# Qiskit imports
from qiskit import QuantumCircuit, QuantumRegister, ClassicalRegister
from qiskit.circuit import Instruction, CircuitInstruction

try:
    from qiskit.circuit.library import QFTGate
except ImportError:  # Qiskit >= 0.45 renomeou para QFT
    from qiskit.circuit.library import QFT as QFTGate
from qiskit.circuit.library import GroverOperator, TwoLocal, StatePreparation
from qiskit.providers.exceptions import JobTimeoutError
from qiskit.quantum_info import Statevector, partial_trace, entropy, Pauli, Kraus
from qiskit.exceptions import QiskitError
from qiskit.version import __version__ as qiskit_version
from packaging import version

REQUIRED_QISKIT_VERSION = "2.0.2"

if not hasattr(QuantumRegister, "__init__") or version.parse(
    qiskit_version
) < version.parse(REQUIRED_QISKIT_VERSION):
    raise ImportError(
        f"Qiskit {REQUIRED_QISKIT_VERSION} ou superior é necessário para o QUALIA."
    )

# Typing imports
from typing import Dict, Optional, List, Any, Union, Tuple, Sequence, TypedDict

# QUALIA specific imports
from .stability import simulate_mass_dynamics
from ..common_types import QuantumSignaturePacket
from ..core.imports_safe import (
    thermal_relaxation_error,
    renyi_entropy,
    mutual_information,
    safe_get_backend,
)
from ..utils.backend import is_gpu_available
from ..adaptive_evolution import AdaptiveConsciousnessEvolution
from ..memory import get_qpm_instance, QuantumPatternMemory
from ..memory.event_bus import SimpleEventBus
from ..retro import RETROCAUSAL_INSIGHT_EVENT
from .eqci import EnhancedQuantumClassicalInterface

from ..utils.cache import numpy_cache
from ..utils.quantum_utils import (
    enforce_circuit_limits,
    prepare_statevector,
    sv_entropy,
    linear_entropy,
)
from ..utils.signature_utils import expected_signature_dimension
from ..metrics.metrics_model import QUALIAMetrics
from .metrics import TranscendencePulseDetector
from .control_utils import compute_discrete_lqr_gain
from ..config import VectorType
from .state_management import (
    generate_circuit_cache_key,
    clear_circuit_cache,
    get_state_representation,
    try_get_statevector_direct,
    try_get_statevector_from_data,
    get_data_dict,
    extract_statevector_from_dict,
    extract_from_snapshots,
    convert_to_statevector,
    shallow_copy_statevector,
)
from . import metrics_helpers
from datadog import DogStatsd
from .quantum_circuit_builder import (
    create_qft_circuit as _create_qft_circuit,
    create_grover_circuit,
    create_vqe_circuit,
)
from .statevector_manager import run_on_backends, run_on_backends_async
from .simulation_cache import (
    get_qft_circuit,
    get_qft_statevector,
    cache_fallback_state,
    get_fallback_state,
    invalidate_qft_cache,
)


def create_qft_circuit(n_qubits: int, add_measurements: bool = True) -> QuantumCircuit:
    """Wrapper para :func:`quantum_circuit_builder.create_qft_circuit`.

    Permite que testes façam monkeypatch de :class:`QFTGate` por este módulo.
    """

    return _create_qft_circuit(
        n_qubits, add_measurements=add_measurements, gate_cls=QFTGate
    )


try:
    from opentelemetry import trace
except Exception:  # pragma: no cover - optional
    trace = None
from .io_utils import export_qasm3 as io_export_qasm3
from ..config import get_global_config_loader
from ..config.config_loader import ConfigLoader
from ..config.constants import get_min_counts_diversity_ratio

try:
    from qiskit import qasm3
except Exception:  # pragma: no cover - optional
    qasm3 = None

# Module-level logger must be defined before any functions use it.
logger = get_logger(__name__)


def load_qualia_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Carrega configurações dinâmicas do QUALIA conforme regra específica.

    IMPORTANTE: Conforme regras QUALIA, NUNCA deve usar valores hardcoded ou fallbacks.
    Deve gerar erro explícito se parâmetro estiver ausente.

    Returns:
        Dict[str, Any]: Configurações carregadas do arquivo strategy_parameters.yaml

    Raises:
        FileNotFoundError: Se o arquivo de configuração não for encontrado
        ValueError: Se o arquivo estiver inválido

    Note:
        Esta função implementa a regra crítica do QUALIA sobre carregamento dinâmico
        de parâmetros de risco e configuração.
    """
    loader = (
        get_global_config_loader() if config_path is None else ConfigLoader(config_path)
    )

    try:
        loaded = loader.load()
    except FileNotFoundError:
        logger.error(
            "Arquivo de configuração não encontrado: %s",
            loader.config_path,
        )
        raise
    except json.JSONDecodeError as e:
        logger.error("Erro ao carregar JSON em %s: %s", loader.config_path, e)
        raise ValueError(
            f"ERRO CRÍTICO: Configuração JSON inválida em {loader.config_path}: {e}"
        ) from e

    return loaded


def get_universe_defaults() -> Dict[str, Any]:
    """
    Extrai configurações específicas do universo quântico do arquivo de config.
    Gera erro explícito se parâmetros obrigatórios estiverem ausentes.
    """
    config = load_qualia_config()

    try:
        # Extrair configurações relevantes para QUALIAQuantumUniverse
        qmc_config = config.get("qmc_config", {})
        ace_config = config.get("ace_config", {})

        lqr_config = config.get("lqr_config", {})
        universe_cfg = config.get("universe_config", {})

        defaults = {
            "thermal_coefficient": qmc_config.get("temperature", None),
            "hawking_factor": qmc_config.get(
                "temperature", None
            ),  # Usar mesma temperatura base
            "shots": qmc_config.get("shots", None),
            "measure_frequency": qmc_config.get("measure_frequency", None),
            "qpu_steps": qmc_config.get("qpu_steps", None),
            "adaptation_aggressiveness": ace_config.get(
                "adaptation_aggressiveness", None
            ),
            "min_qubits": ace_config.get("min_qubits", None),
            "max_qubits": ace_config.get("max_qubits", None),
            "lqr_config": lqr_config or None,
            "min_counts_diversity_ratio": universe_cfg.get(
                "min_counts_diversity_ratio",
                None,
            ),
            "max_circuit_depth": universe_cfg.get("max_circuit_depth", None),
            "max_circuit_operations": universe_cfg.get(
                "max_circuit_operations",
                None,
            ),
            "max_exact_qft_qubits": universe_cfg.get(
                "max_exact_qft_qubits",
                None,
            ),
        }

        # Verificar se todos os parâmetros obrigatórios estão presentes
        required = {k: v for k, v in defaults.items() if k != "lqr_config"}
        missing_params = [key for key, value in required.items() if value is None]
        if missing_params:
            raise ValueError(
                f"ERRO CRÍTICO: Parâmetros obrigatórios ausentes no config: {missing_params}. "
                "Conforme regras QUALIA, NUNCA usar fallbacks hardcoded."
            )

        return defaults

    except KeyError as e:
        raise ValueError(
            f"ERRO CRÍTICO: Seção de configuração ausente: {e}. "
            "Verifique a estrutura do arquivo config/strategy_parameters.yaml"
        )


# Used when evaluating measurement results. Loaded from configuration.
try:  # pragma: no cover - configuration may be missing during tests
    MIN_COUNTS_DIVERSITY_RATIO = get_min_counts_diversity_ratio()
except Exception:  # noqa: BLE001
    MIN_COUNTS_DIVERSITY_RATIO = 0.0
# Circuit depth considered too shallow when averaged over multiple runs.
MIN_AVG_CIRCUIT_DEPTH = 3


def _load_max_exact_qft_qubits(default: int = 14) -> int:
    """Return QFT size threshold from env or configuration."""

    env_value = os.getenv("QUALIA_MAX_EXACT_QFT_QUBITS")
    if env_value:
        try:
            return int(env_value)
        except ValueError as exc:  # pragma: no cover - defensive
            logger.error(
                "Valor inválido para QUALIA_MAX_EXACT_QFT_QUBITS: %s", env_value
            )

    try:
        config = get_global_config_loader().load()
        cfg_value = config.get("universe_config", {}).get(
            "max_exact_qft_qubits", default
        )
        return int(cfg_value)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Erro ao carregar 'max_exact_qft_qubits' do config: %s", exc)
        return default


MAX_EXACT_QFT_QUBITS = _load_max_exact_qft_qubits()


def _load_use_gpu(default: bool = False) -> bool:
    """Return whether GPU usage should be enabled."""
    env_val = os.getenv("QUALIA_USE_GPU")
    if env_val is not None:
        use = str(env_val).lower() in {"1", "true", "yes", "y"}
        if use and not is_gpu_available():
            logger.warning("GPU solicitado por QUALIA_USE_GPU, mas indisponível.")
            return False
        return use

    try:
        config = get_global_config_loader().load()
        cfg_val = config.get("universe_config", {}).get("use_gpu", default)
        if cfg_val and not is_gpu_available():
            logger.warning("Configuração YAML de GPU ignorada por indisponibilidade.")
            return False
        return bool(cfg_val)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Erro ao carregar 'use_gpu' do config: %s", exc)
        return default


USE_GPU = _load_use_gpu()


# === Quantum Counting & Amplitude Amplification ===


def build_oracle_Hn(n_qubits: int, T) -> QuantumCircuit:
    """Oracle que marca com fase ``-1`` estados com peso de Hamming ``≤ T``."""

    from qiskit.circuit.library.generalized_gates.diagonal import DiagonalGate
    from qiskit import QuantumCircuit
    import numpy as np

    # Calcula pesos de Hamming de forma vetorizada para evitar loops Python
    numbers = np.arange(2**n_qubits)
    hamming_weights = np.vectorize(int.bit_count)(numbers)
    diag = np.where(hamming_weights <= T, -1, 1).tolist()

    qc = QuantumCircuit(n_qubits)
    qc.append(DiagonalGate(diag), list(range(n_qubits)))
    return qc


def build_grover_operator(oracle: QuantumCircuit, n_qubits: int):
    """Calcula e retorna a matriz do operador de Grover (D·O)."""
    from qiskit.quantum_info import Operator

    # matriz do oráculo
    O = Operator(oracle).data
    # constrói circuito difusor
    from qiskit import QuantumCircuit

    qc_diff = QuantumCircuit(n_qubits)
    qc_diff.h(range(n_qubits))
    qc_diff.x(range(n_qubits))
    qc_diff.h(n_qubits - 1)
    # Toffoli otimizado sem ancillas extras
    qc_diff.mcx(list(range(n_qubits - 1)), n_qubits - 1, mode="noancilla")
    qc_diff.h(n_qubits - 1)
    qc_diff.x(range(n_qubits))
    qc_diff.h(range(n_qubits))
    D = Operator(qc_diff).data
    # Grover operator G = D · O
    import numpy as _np

    return _np.dot(D, O)


def estimate_solutions_grover(n_qubits: int, grover_op, precision_bits: int) -> int:
    """Quantum Counting: obtém contagem exata via operador de Grover e reflexão."""
    import numpy as np
    from qiskit import QuantumCircuit
    from qiskit.quantum_info import Operator

    N = 2**n_qubits
    # constrói operador de difusão D
    qc_diff = QuantumCircuit(n_qubits)
    qc_diff.h(range(n_qubits))
    qc_diff.x(range(n_qubits))
    qc_diff.h(n_qubits - 1)
    # Toffoli otimizado sem ancillas extras
    qc_diff.mcx(list(range(n_qubits - 1)), n_qubits - 1, mode="noancilla")
    qc_diff.h(n_qubits - 1)
    qc_diff.x(range(n_qubits))
    qc_diff.h(range(n_qubits))
    D = Operator(qc_diff).data
    # obtém oracle O via O = D · G (pois G = D · O e D² = I)
    O = D @ grover_op
    # extrai diagonal de O: -1 nas soluções
    diag = np.diag(O)
    # conta entradas com valor ≈ -1
    M_est = int(np.sum(np.real(diag) < 0))
    return max(M_est, 1)


def amplitude_amplification(
    n_qubits: int, grover_op, iterations: int
) -> QuantumCircuit:
    """Amplitude Amplification genérico via iterações de Grover."""
    from qiskit import QuantumCircuit, QuantumRegister, ClassicalRegister

    qr = QuantumRegister(n_qubits, "data")
    cr = ClassicalRegister(n_qubits, "c")
    qc = QuantumCircuit(qr, cr)
    qc.h(qr)
    for _ in range(iterations):
        qc.append(grover_op, qr)
    qc.measure(qr, cr)
    return qc


class QuantumMiner:
    """Pipeline integrando Quantum Counting e Amplitude Amplification."""

    def __init__(self, n_qubits: int, precision_bits: int = 5):
        self.n = n_qubits

        self.prec = precision_bits

    def find_nonce(self, T: int) -> Optional[str]:
        """Busca um nonce cujo peso de Hamming seja inferior a ``T``.

        Parameters
        ----------
        T:
            Limite máximo para o peso de Hamming considerado válido.

        Returns
        -------
        Optional[str]
            String binária representando o nonce encontrado ou ``None`` se a
            busca falhar.
        """
        # Usar safe_get_backend
        backend = safe_get_backend("qasm_simulator")
        if not hasattr(backend, "run"):  # Verifica se não é o MockBackend básico
            logger.error(
                "QuantumMiner: Backend 'qasm_simulator' não pôde ser carregado ou é um mock inválido. Impossível encontrar nonce."
            )
            return None

        # Supondo que build_oracle_Hn, build_grover_operator, estimate_solutions_grover,
        # e amplitude_amplification estão definidas em algum lugar neste arquivo ou importadas.
        # Estes imports/definições devem ser verificados.
        try:
            oracle = build_oracle_Hn(self.n, T)
            grover_op = build_grover_operator(oracle, self.n)
            M_est = estimate_solutions_grover(self.n, grover_op, self.prec)
            k_opt = int((np.pi / 4) * np.sqrt(2**self.n / M_est)) if M_est > 0 else 1
            qc = amplitude_amplification(self.n, grover_op, k_opt)

            job = backend.run(qc, shots=1024)
            counts = job.result().get_counts()
            return max(counts, key=counts.get) if counts else None
        except Exception as e:
            logger.error(
                f"QuantumMiner: Erro durante a busca de nonce: {e}", exc_info=True
            )
            return None


# === QUALIA Quantum Universe ===
try:
    from qiskit_aer import Aer
except ImportError:
    try:
        from qiskit.providers.aer import Aer
    except ImportError as exc:
        raise ImportError(
            "Aer não encontrado. Instale com: pip install qiskit qiskit-aer"
        ) from exc

# Reaproveita o scrambler avançado já existente
try:
    from toy_black_hole import advanced_scrambler
except ImportError:
    # fallback simples com suporte a estilos de entrelaçamento

    def advanced_scrambler(
        qc: QuantumCircuit, regs: Sequence, depth: int = 3, style: str = "linear"
    ) -> None:
        """Aplica camadas de "scrambling" com rotações aleatórias e CX.

        Esta implementação serve como fallback quando ``src.toy_black_hole`` não
        está disponível. Entre três e quatro camadas são aplicadas. Cada camada
        realiza rotações ``RY`` e ``RZ`` aleatórias em todos os qubits seguida do
        entrelaçamento definido por ``style``.

        Parameters
        ----------
        qc
            Circuito a ser modificado.
        regs
            Registradores (ou lista de qubits) alvo do entrelaçamento.
        depth
            Profundidade do scrambling (ignorado, mantido para compatibilidade).
        style
            Estratégia de entrelaçamento. ``"linear"`` conecta pares vizinhos,
            ``"circular"`` forma um anel, ``"full"`` conecta todos os pares e
            ``"linear_swap"`` realiza CX seguido de SWAP para cada par
            consecutivo.
        """

        n = len(regs)
        num_layers = np.random.randint(3, 5)
        for _ in range(num_layers):
            for q in regs:
                qc.ry(np.random.uniform(0, 2 * np.pi), q)
                qc.rz(np.random.uniform(0, 2 * np.pi), q)

            if style == "full":
                for i in range(n):
                    for j in range(i + 1, n):
                        qc.cx(regs[i], regs[j])
            elif style == "circular":
                for i in range(n):
                    qc.cx(regs[i], regs[(i + 1) % n])
            elif style == "linear_swap":
                for i in range(n - 1):
                    qc.cx(regs[i], regs[i + 1])
                    qc.swap(regs[i], regs[i + 1])
            else:  # "linear" ou desconhecido
                idx = np.arange(n)
                np.random.shuffle(idx)
                for i in range(0, n - 1, 2):
                    qc.cx(regs[idx[i]], regs[idx[i + 1]])
            qc.barrier()


def _apply_random_scrambling_layer(qc: QuantumCircuit, regs: Sequence) -> None:
    """Aplica uma camada aleatória de Hadamard e CNOT.

    Utiliza operações em lote para aplicar as portas de Hadamard e escolhe os
    pares de qubits de maneira vetorizada para reduzir sobrecarga de loops
    Python.
    """

    rng = np.random.default_rng()
    # Aplica H a todos os qubits de uma vez, quando suportado
    qc.h(regs)

    if len(regs) > 1:
        idx = np.arange(len(regs))
        rng.shuffle(idx)
        # Escolhe pares sequenciais após o embaralhamento
        for i in range(len(idx) - 1):
            qc.cx(regs[idx[i]], regs[idx[i + 1]])

    qc.barrier()


# --- Emissão Hawking térmica ---


def emit_hawking(qc: QuantumCircuit, q_from: int, q_to: int, temperature: float = 0.05):
    """Realiza SWAP e aplica um canal de decaimento de amplitude na qubit de radiação."""
    qc.swap(q_from, q_to)

    # Determinar gamma para o canal de decaimento de amplitude.
    # Usamos 'temperature' como proxy para a força do canal.
    # Gamma deve estar em [0,1]. Se temperature for, e.g., 0.05, isso já está no range.
    # Poderíamos aplicar um fator de escala se 'temperature' tiver um range diferente.
    # Por ora, vamos assumir que 'temperature' pode ser usado diretamente como 'gamma',
    # com clipping para garantir que esteja em [0,1].
    gamma = np.clip(
        temperature, 0.0, 1.0
    )  # Garante que gamma esteja no intervalo [0,1]

    if gamma > 1e-9:  # Só aplicar o canal se gamma for significativo
        try:
            # Operadores de Kraus para canal de decaimento de amplitude (Amplitude Damping)
            E0 = np.array([[1, 0], [0, np.sqrt(1 - gamma)]], dtype=complex)
            E1 = np.array([[0, np.sqrt(gamma)], [0, 0]], dtype=complex)
            amplitude_decay_channel = Kraus([E0, E1])
            # Aplicar o canal ao qubit de radiação (q_to)
            qc.append(amplitude_decay_channel, [q_to])
            logger.debug(
                f"Aplicado canal de decaimento de amplitude (manual) com gamma={gamma:.4f} em q_to={q_to}"
            )
        except ValueError as ve:
            logger.error(
                f"Erro ao criar/aplicar canal de decaimento de amplitude com gamma={gamma}: {ve}"
            )
        except Exception as e:
            logger.error(
                f"Erro inesperado ao aplicar canal de decaimento de amplitude: {e}",
                exc_info=True,
            )
    # A lógica original com thermal_relaxation_error foi removida/substituída.
    # if thermal_relaxation_error:
    #     thermal = thermal_relaxation_error(50, 50, 1, temperature).to_instruction()
    #     qc.append(thermal, [q_to])


def apply_hawking_noise_post_trim(
    qc: QuantumCircuit, q_from: int, q_to: int, temperature: float = 0.05
) -> QuantumCircuit:
    """Insere canal de decaimento de amplitude após operações SWAP existentes.

    A função varre o circuito procurando por instruções ``swap`` entre ``q_from``
    e ``q_to`` e, imediatamente após cada uma delas, adiciona o canal de
    decaimento de amplitude. Utilizada quando a emissão Hawking deve ocorrer
    somente após o `enforce_circuit_limits`.

    Parameters
    ----------
    qc : QuantumCircuit
        Circuito já aparado que receberá o ruído.
    q_from : int
        Índice do qubit de origem utilizado nos ``swap``.
    q_to : int
        Índice do qubit de destino que sofrerá o decaimento.
    temperature : float, optional
        Temperatura usada como proxy para a intensidade do canal.

    Returns
    -------
    QuantumCircuit
        O circuito modificado com as instruções de ruído aplicadas.
    """

    gamma = np.clip(temperature, 0.0, 1.0)
    if gamma <= 1e-9:
        return qc

    try:
        E0 = np.array([[1, 0], [0, np.sqrt(1 - gamma)]], dtype=complex)
        E1 = np.array([[0, np.sqrt(gamma)], [0, 0]], dtype=complex)
        amplitude_decay_channel = Kraus([E0, E1])
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Falha ao criar canal de decaimento: %s", exc)
        return qc

    decay_inst = Kraus([E0, E1]).to_instruction()

    new_data: List[CircuitInstruction] = []
    for op, qargs, cargs in qc.data:
        new_data.append(CircuitInstruction(op, list(qargs), list(cargs)))
        if len(qargs) < 2:
            continue
        q0_idx = getattr(qargs[0], "index", getattr(qargs[0], "_index", None))
        q1_idx = getattr(qargs[1], "index", getattr(qargs[1], "_index", None))
        if op.name == "swap" and q0_idx == q_from and q1_idx == q_to:
            new_data.append(CircuitInstruction(decay_inst, [qc.qubits[q_to]], []))

    qc.data = new_data
    return qc


# --- End of Header / Start of Refactored Classes ---


class QUALIAQuantumUniverse:
    _circuit_cache: OrderedDict[str, QuantumCircuit] = OrderedDict()
    _circuit_cache_maxsize: int = 10
    _transpiled_symbol_tf_cache: Dict[Tuple[str, str], QuantumCircuit] = {}
    _INVALID_METRIC_RESET_CYCLES: int = 5

    # ... (outros atributos da classe) ...

    def __init__(
        self,
        n_qubits: int,
        scr_depth: int,
        base_lambda: float,
        alpha: float,
        retro_strength: float,
        num_ctc_qubits: int,
        measure_frequency: Optional[int] = None,
        thermal_coefficient: Optional[float] = None,
        initial_state_vector: Optional[np.ndarray] = None,
        backend_name: str = "aer_simulator_statevector",
        qast_feedback_enabled: bool = False,
        lambda_factor_multiplier: float = 1.0,
        enrichment_cycles: int = 1,
        adaptive_threshold: Optional[float] = None,
        entanglement_style: str = "full",
        max_history_size: int = 100,
        initial_state_type: str = "qft",
        eqci_config: Optional[Dict[str, Any]] = None,
        informational_mass: float = 1.0,  # Default, mas pode ser sobrescrito
        hawking_factor: Optional[float] = None,
        mass_threshold: float = 0.02,
        thermal_J_coupling: float = 1.0,
        thermal_beta_inverse_temp: Optional[float] = None,
        shots: Optional[int] = None,
        qpu_steps: Optional[int] = None,
        thermal_noise_enabled: bool = False,
        thermal_noise_temperature: float = 0.0,
        temperature: Optional[float] = None,
        allow_qft_fallback: bool = True,
        qpm_config: Optional[Dict[str, Any]] = None,
        qpm_instance: Optional[QuantumPatternMemory] = None,
        event_bus: Optional[SimpleEventBus] = None,
        min_counts_diversity_ratio: Optional[float] = None,
        max_circuit_depth: Optional[int] = None,
        max_circuit_operations: Optional[int] = None,
        statsd_client: Optional[DogStatsd] = None,
        otoc_frequency: int = 1,
        use_gpu: Optional[bool] = None,
    ):
        self.n_qubits = n_qubits
        self.scr_depth = scr_depth
        self.base_lambda = base_lambda
        self.alpha = alpha
        self.retro_strength = retro_strength
        self.num_ctc_qubits = num_ctc_qubits
        self.backend_name = backend_name
        self.qast_feedback_enabled = qast_feedback_enabled
        self.lambda_factor_multiplier = lambda_factor_multiplier
        self.enrichment_cycles = enrichment_cycles
        self.use_gpu = USE_GPU if use_gpu is None else use_gpu
        self.adaptive_threshold = adaptive_threshold
        self.entanglement_style = entanglement_style
        self.max_history_size = max_history_size
        self.initial_state_type = initial_state_type
        # self.informational_mass é atribuído mais abaixo após processamento.
        self.mass_threshold = mass_threshold
        self.thermal_J_coupling = thermal_J_coupling
        self.thermal_beta_inverse_temp = thermal_beta_inverse_temp
        self.thermal_noise_enabled = thermal_noise_enabled
        self.thermal = (
            self.thermal_noise_enabled
        )  # Inicializar self.thermal para compatibilidade com run()
        self.thermal_noise_temperature = thermal_noise_temperature
        self.allow_qft_fallback = allow_qft_fallback
        self.qpm_config = qpm_config if qpm_config is not None else {}
        self.eqci_config = eqci_config  # Atribuir eqci_config aqui também
        self.initial_state_vector = (
            initial_state_vector  # Inicializar o vetor de estado inicial
        )
        # Métrica de complexidade usada na última vez em que o número de qubits foi aumentado
        self.last_qubit_increase_metric: float = 0.0

        # Modo de controle inicial antes de carregar configurações adicionais
        self.control_mode: str = "PID"  # Modos possíveis: "PID", "LQR", "OPEN_LOOP"

        # Atributos de controle LQR precisam existir antes do carregamento de
        # configurações para evitar ``AttributeError`` caso o bloco de config
        # tente acessá-los.
        self.target_informational_mass: Optional[float] = 10.0
        self.target_H_symb_prime: Optional[float] = 0.5
        self.target_S_quantum: Optional[float] = 0.3

        self.lqr_A_matrix: Optional[np.ndarray] = None
        self.lqr_B_matrix: Optional[np.ndarray] = None
        self.lqr_Q_matrix: Optional[np.ndarray] = None
        self.lqr_R_matrix: Optional[np.ndarray] = None
        self.lqr_K_gain: Optional[np.ndarray] = None

        # Inicializar o logger da instância primeiro
        self.logger = get_logger(__name__)
        self.logger.info(
            f"QUALIAQuantumUniverse pré-inicializando com {self.n_qubits} qubits, backend: {self.backend_name}"
        )
        self.logger.info(
            "Uso de GPU %s (disponível=%s)",
            "ativado" if self.use_gpu else "desativado",
            is_gpu_available(),
        )
        self.statsd: DogStatsd | None = statsd_client
        self.event_bus = event_bus
        self.otoc_frequency = max(1, int(otoc_frequency))

        if self.event_bus is not None:
            self.event_bus.subscribe(
                RETROCAUSAL_INSIGHT_EVENT, self._on_retrocausal_insight
            )

        try:
            config_defaults = get_universe_defaults()
            self.logger.debug(
                "Config defaults loaded: max_circuit_depth=%s, "
                "max_circuit_operations=%s",
                config_defaults.get("max_circuit_depth"),
                config_defaults.get("max_circuit_operations"),
            )

            # Aplicar valores de config apenas se não foram fornecidos explicitamente NA CHAMADA DO CONSTRUTOR
            # Atributos que podem ser sobrescritos por config se não passados explicitamente
            self.measure_frequency = (
                measure_frequency
                if measure_frequency is not None
                else config_defaults["measure_frequency"]
            )
            self.thermal_coefficient = (
                thermal_coefficient
                if thermal_coefficient is not None
                else config_defaults["thermal_coefficient"]
            )
            self.hawking_factor = (
                hawking_factor
                if hawking_factor is not None
                else config_defaults["hawking_factor"]
            )
            self.shots = shots if shots is not None else config_defaults["shots"]
            self.qpu_steps = (
                qpu_steps if qpu_steps is not None else config_defaults["qpu_steps"]
            )
            self.min_counts_diversity_ratio = (
                min_counts_diversity_ratio
                if min_counts_diversity_ratio is not None
                else config_defaults["min_counts_diversity_ratio"]
            )
            self.max_circuit_depth = (
                max_circuit_depth
                if max_circuit_depth is not None
                else config_defaults["max_circuit_depth"]
            )
            self.max_circuit_operations = (
                max_circuit_operations
                if max_circuit_operations is not None
                else config_defaults["max_circuit_operations"]
            )

            effective_temperature = (
                temperature
                if temperature is not None
                else config_defaults.get(
                    "temperature", self.thermal_coefficient
                )  # Usa thermal_coefficient como fallback se config não tiver 'temperature'
            )
            self.temperature = effective_temperature

            self._validate_critical_parameters()

            self.logger.info(
                f"Configurações dinâmicas carregadas e validadas: measure_frequency={self.measure_frequency}, "
                f"thermal_coefficient={self.thermal_coefficient}, hawking_factor={self.hawking_factor}, temp={self.temperature}"
            )

            lqr_cfg = config_defaults.get("lqr_config")
            if lqr_cfg:
                self.control_mode = lqr_cfg.get("mode", self.control_mode)
                self.target_informational_mass = lqr_cfg.get(
                    "target_informational_mass", self.target_informational_mass
                )
                self.target_H_symb_prime = lqr_cfg.get(
                    "target_H_symb_prime", self.target_H_symb_prime
                )
                self.target_S_quantum = lqr_cfg.get(
                    "target_S_quantum", self.target_S_quantum
                )
                if "A" in lqr_cfg:
                    self.lqr_A_matrix = np.asarray(lqr_cfg["A"], dtype=float)
                if "B" in lqr_cfg:
                    self.lqr_B_matrix = np.asarray(lqr_cfg["B"], dtype=float)
                if "Q" in lqr_cfg:
                    self.lqr_Q_matrix = np.asarray(lqr_cfg["Q"], dtype=float)
                if "R" in lqr_cfg:
                    self.lqr_R_matrix = np.asarray(lqr_cfg["R"], dtype=float)
                if "K" in lqr_cfg:
                    self.lqr_K_gain = np.asarray(lqr_cfg["K"], dtype=float)
                elif all(
                    m is not None
                    for m in (
                        self.lqr_A_matrix,
                        self.lqr_B_matrix,
                        self.lqr_Q_matrix,
                        self.lqr_R_matrix,
                    )
                ):
                    try:
                        self.lqr_K_gain = compute_discrete_lqr_gain(
                            self.lqr_A_matrix,
                            self.lqr_B_matrix,
                            self.lqr_Q_matrix,
                            self.lqr_R_matrix,
                        )
                        self.logger.info("LQR K gain computed successfully.")
                    except Exception as e_lqr:
                        self.logger.warning(f"Falha ao calcular LQR K gain: {e_lqr}")

        except Exception as e:
            self.logger.error(
                f"ERRO CRÍTICO: Falha ao carregar ou validar configurações dinâmicas: {e}"
            )
            raise

        # Atributo para armazenar allow_qft_fallback (já movido para cima)
        # self.allow_qft_fallback = allow_qft_fallback
        # super().__init__() # Removido pois não há superclasse funcional definida aqui

        # Inicializar atributos que podem ser acessados antes de serem definidos
        self.initial_sv = None
        self.last_run_result = None
        self._last_result = None
        self.last_result = None
        self.last_statevector = None
        self._last_counts = {}
        self._last_execution_time = 0.0
        self._last_run_steps = 0
        self.circuit = (
            None  # Inicializando o atributo circuit para evitar AttributeError
        )
        self.metrics_lock = threading.RLock()

        # Lock assíncrono para atualizações em statevectors e históricos
        self._sv_lock = asyncio.Lock()

        # Inicializar sv_history para armazenar o histórico de statevectors
        self.sv_history = []

        # self.logger já foi inicializado
        # self.logger = get_logger(
        #     f"{__name__}.{self.__class__.__name__}"
        # )
        # self.logger.info(
        #     f"QUALIAQuantumUniverse inicializando com {n_qubits} qubits, backend: {backend_name}"
        # )

        # Atribuição dos parâmetros restaurados (a maioria já foi movida para o topo)
        # self.n_qubits = n_qubits
        # --- Inicialização explícita dos registradores fundamentais ---
        from qiskit import QuantumRegister, ClassicalRegister

        # Inicializar registradores quânticos
        self.qr_bh = QuantumRegister(
            self.n_qubits, "bh"
        )  # Usa self.n_qubits que já está definido
        self.qr_radiation = QuantumRegister(1, "radiation")

        # Inicializar registrador clássico para armazenar medidas de radiação
        self.cr_radiation = ClassicalRegister(1, "cr_radiation")
        # Registrador clássico para medir todos os qubits de dados
        self.cr_main = ClassicalRegister(self.n_qubits, "cr_main")

        # Log de depuração
        self.logger.debug(
            f"Registradores inicializados: qr_bh={self.qr_bh}, qr_radiation={self.qr_radiation}, cr_radiation={self.cr_radiation}, cr_main={self.cr_main}"
        )
        self.logger.debug(
            f"Tamanho dos registradores: qr_bh.size={self.qr_bh.size}, qr_radiation.size={self.qr_radiation.size}, cr_radiation.size={self.cr_radiation.size}, cr_main.size={self.cr_main.size}"
        )
        # self.scr_depth = scr_depth (movido)
        # ... (outras atribuições que já foram movidas) ...

        # Atribuição de informational_mass, respeitando o valor de entrada ou o default da classe
        self.informational_mass = (
            informational_mass  # Usa o valor do parâmetro (default 1.0)
        )

        self.qast_metrics_snapshot_for_lambda: Dict[str, Any] = {}
        self.qast_influence_on_lambda_factor: float = 0.1  # Fator de influência padrão

        # Para a função alpha(H_k) - taxa de decaimento da massa informacional
        self.alpha_decay_type: str = (
            "quadratic"  # Opções: "linear", "quadratic", "logistic"
        )
        self.alpha_H_symb_decay_base: float = 0.01  # alpha_0
        self.alpha_H_symb_decay_factor_linear: float = 0.05  # alpha_1
        self.alpha_H_symb_decay_factor_quadratic: float = (
            0.02  # alpha_2 (usado se type="quadratic")
        )
        # Parâmetros para alpha_decay_type = "logistic"
        self.alpha_logistic_max: float = 0.2  # alpha_max
        self.alpha_logistic_beta: float = 10.0  # beta (inclinação da logística)
        self.alpha_logistic_H0: float = (
            0.5  # H_0 (ponto central da logística, para H_symb_prime em [0,1])
        )

        # Para o controlador PI do qast_input (I_k)
        self.qast_input_control_enabled: bool = True
        self.qast_input_Kp: float = 0.5  # Ganho Proporcional
        self.qast_input_Ki: float = 0.1  # Ganho Integral
        self.qast_input_target_H_symb: float = (
            0.5  # H_symb_prime alvo (e.g., buscar entropia média)
        )
        self.qast_input_integral_error_sum: float = 0.0  # Acumulador do erro integral
        self.current_qast_input_value: float = 1.0  # Valor inicial para qast_input
        self.qast_input_min: float = -10.0  # Limite inferior para qast_input
        self.qast_input_max: float = 10.0  # Limite superior para qast_input

        # Para a massa informacional em si

        # self.informational_mass já foi definida acima.
        self.initial_informational_mass: float = (
            self.informational_mass
        )  # Snapshot inicial
        self.min_informational_mass: float = (
            0.1  # M_min - critério de parada (pode ser configurável)
        )

        # Lógica de eqci (mantida da correção anterior)
        self.eqci_instance: Optional[EnhancedQuantumClassicalInterface] = None
        # self.eqci: Optional[EnhancedQuantumClassicalInterface] = None # eqci será self.eqci_instance
        # self.eqci_config já foi atribuído

        if self.eqci_config is None:
            self.eqci_config = {
                "critical_window": 20,
                "secondary_window": 20,
                "vol_cap": 3.0,
            }
        try:
            self.eqci_instance = EnhancedQuantumClassicalInterface(
                critical_feature_window=self.eqci_config.get("critical_window", 20),
                secondary_feature_window=self.eqci_config.get("secondary_window", 20),
                volume_ratio_cap=self.eqci_config.get("vol_cap", 3.0),
            )
            self.logger.info(
                f"EQCI (eqci_instance) inicializado com config: {self.eqci_config}."
            )
        except Exception as e_eqci_init:
            self.logger.error(
                f"Falha ao inicializar EQCI (eqci_instance) com config {self.eqci_config}: {e_eqci_init}",
                exc_info=True,
            )
            self.eqci_instance = None

        if self.eqci_instance is None:
            self.logger.info("EQCI (eqci_instance) não foi inicializado.")
        self.eqci = self.eqci_instance

        # Configuração do backend (self.backend_name já atribuído)
        self.backend = safe_get_backend(self.backend_name, use_gpu=self.use_gpu)
        if self.backend is None:
            self.logger.error(f"Falha ao obter o backend {self.backend_name}")
            self.logger.error(
                "Backend {self.backend_name} não pôde ser carregado. Tentando fallback para 'aer_simulator_statevector'."
            )
            self.backend_name = "aer_simulator_statevector"
            self.backend = safe_get_backend(self.backend_name, use_gpu=self.use_gpu)
            if self.backend is None:
                self.logger.critical(
                    "Fallback para 'aer_simulator_statevector' também falhou. Operação seriamente comprometida."
                )
                # Manter backend como None e permitir que a instância seja criada
                # para testes que verificam este cenário.
                self.logger.error(
                    "Não foi possível carregar um backend Qiskit funcional."
                )

        self.metrics = QUALIAMetrics(max_history_size=self.max_history_size)
        self.diversity_ratio_ema: float | None = None
        self.circuit_depth_ema: float | None = None
        self._diversity_below_count: int = 0
        self._last_metrics: Dict[str, Any] = {}

        # Inicialização do QPM
        self.qpm: Optional[QuantumPatternMemory] = qpm_instance
        if self.qpm is None:
            cfg = dict(self.qpm_config)
            cfg["event_bus"] = self.event_bus
            self.qpm = get_qpm_instance(cfg)
            self.logger.info(
                "QuantumPatternMemory (qpm) inicializada via get_qpm_instance com config: %s",
                {k: str(v) if k == "risk_manager" else v for k, v in cfg.items()},
            )
        else:
            self.logger.info(
                "QuantumPatternMemory (qpm) recebida via parâmetro de inicializacao."
            )
            if (
                self.event_bus is not None
                and getattr(self.qpm, "event_bus", None) is None
            ):
                self.qpm.event_bus = self.event_bus

        # Inicialização do estado quântico (current_sv e last_sv)
        self.current_sv: Optional[Statevector] = None
        self.last_sv: Optional[Statevector] = None
        try:
            # _create_initial_state deve retornar um Statevector ou levantar exceção.
            initial_state_obj = self._create_initial_state(
                specific_type=self.initial_state_type,
                initial_state_vector_np=initial_state_vector,
                allow_qft_fallback=self.allow_qft_fallback,
            )

            if self.current_sv is not None:
                self.logger.info(
                    f"Estado inicial (current_sv) definido com sucesso a partir de _create_initial_state para tipo '{self.initial_state_type}'."
                )
                initial_state_obj = self.current_sv
            elif isinstance(Statevector, type) and isinstance(
                initial_state_obj, Statevector
            ):
                self.current_sv = initial_state_obj
                self.logger.info(
                    f"Estado inicial (current_sv) definido com sucesso a partir de _create_initial_state para tipo '{self.initial_state_type}'."
                )
            elif isinstance(initial_state_obj, QuantumCircuit):
                if self.current_sv is None:
                    # _create_initial_state não conseguiu definir current_sv e
                    # retornou apenas o circuito. Mantemos current_sv como None
                    # para sinalizar a falha ao chamador.
                    self.logger.error(
                        "Objeto QuantumCircuit retornado por _create_initial_state, mas current_sv permaneceu indefinido."
                    )
                else:
                    self.logger.info(
                        f"Recebido QuantumCircuit de _create_initial_state para tipo '{self.initial_state_type}'. Convertendo para Statevector via simulação."
                    )
                    # Correção: Usar o novo método _create_statevector_from_circuit_via_simulation
                    self.current_sv = (
                        self._create_statevector_from_circuit_via_simulation(
                            initial_state_obj
                        )
                    )
            else:
                # Se Statevector foi "monkeypatched" para um objeto que não é
                # um tipo, o isinstance acima não é válido e cairá aqui. Para
                # tornar o código robusto a patches nos testes, verificamos se o
                # objeto retornado tem atributos tipicamente presentes em um
                # Statevector e o aceitamos se for o caso.
                if hasattr(initial_state_obj, "data") and hasattr(
                    initial_state_obj, "num_qubits"
                ):
                    self.current_sv = initial_state_obj
                    self.logger.info(
                        "Estado inicial (current_sv) definido a partir de objeto com atributos de Statevector."
                    )
                else:
                    self.logger.error(
                        f"_create_initial_state retornou tipo inesperado '{type(initial_state_obj)}' ou None para initial_state_type '{self.initial_state_type}'."
                    )
                    self.current_sv = None  # Explicitamente None

            if self.current_sv is not None:
                self.last_sv = shallow_copy_statevector(self.current_sv)
                self.logger.info("last_sv copiado de current_sv (shallow).")
                if initial_state_vector is not None:
                    self.logger.info(
                        "QUALIAQuantumUniverse inicializado com statevector customizado"
                    )
            else:
                self.logger.error(
                    f"Falha ao definir self.current_sv (e consequentemente self.last_sv) no __init__ para tipo '{self.initial_state_type}'."
                )

        except (ValueError, QiskitError, TypeError) as e_state_creation:
            self.logger.critical(
                f"Exceção ({type(e_state_creation).__name__}) durante a criação/atribuição do estado inicial: {e_state_creation}",
                exc_info=True,
            )
            # self.current_sv já é None ou será None se a exceção ocorreu dentro do try
            raise  # Re-levantar para falhar a construção do objeto

        if self.current_sv is None:
            # Em alguns cenários de teste, _create_initial_state pode falhar em
            # definir current_sv. Nesses casos registramos um erro crítico, mas
            # permitimos que a instância seja criada para inspeção posterior.
            self.logger.critical(
                "CRÍTICO: self.current_sv é None após toda a lógica de inicialização"
            )
        else:
            self.initial_sv_snapshot = shallow_copy_statevector(self.current_sv)
            self.logger.info(
                "Snapshot do estado inicial (initial_sv_snapshot) criado (shallow)."
            )

        try:
            self.qft_circuit = get_qft_circuit(
                self.n_qubits, lambda n: create_qft_circuit(n, add_measurements=False)
            )
        except QiskitError as e:
            if self.allow_qft_fallback:
                self.logger.warning(
                    f"Falha ao criar QFT para {self.n_qubits} qubits: {e}. Usando fallback para circuito vazio."
                )
                self.qft_circuit = QuantumCircuit(self.n_qubits)
            else:
                self.logger.error(
                    f"Falha ao criar QFT para {self.n_qubits} qubits (fallback desabilitado): {e}"
                )
                raise
        self.state_history: List[Union[np.ndarray, Dict[str, int]]] = []

        self.qc = QuantumCircuit(self.n_qubits, name="QUALIA_Core")
        self.circuit = None  # Inicializando o atributo circuit explicitamente
        if self.current_sv is not None:  # Compor o estado inicial no circuito principal
            # A forma de compor depende se current_sv é um Statevector ou um QuantumCircuit inicial
            # Se _create_initial_state já retorna um Statevector, inicializamos o qc com ele.
            try:
                prepare_statevector(self.qc, self.current_sv.data)
            except Exception as e_qc_init:
                self.logger.warning(
                    f"Vetor de estado inicial fornecido tem tamanho incompatível para {self.n_qubits} qubits: {e_qc_init}"
                )
                self.logger.warning(
                    f"Não foi possível inicializar self.qc com self.current_sv.data: {e_qc_init}. Pode ser necessário compor um circuito inicial."
                )
                self.logger.warning("Ignorando e criando estado inicial padrão")
                self.current_sv = self._create_initial_state("hadamard")
                self.last_sv = shallow_copy_statevector(self.current_sv)
                # Se self.current_sv foi derivado de um circuito, esse circuito poderia ser composto aqui.
                # Esta parte pode precisar de mais lógica dependendo do que _create_initial_state retorna.

        self.information_history: List[Dict[str, Any]] = []
        self.system_entropy_history: List[float] = []
        self.last_measurement_outcome: Optional[str] = None
        self.lambda_history: List[float] = []
        self.run_count: int = 0
        self.qpu_execution_count: int = 0
        self.last_scr_config: Optional[Dict[str, Any]] = None
        self.consciousness_cycles_count: int = 0
        # ... quaisquer outros atributos que precisam ser inicializados ...

        # Cache simples para evitar recalcular o statevector quando o resultado
        # do backend não mudou
        self._last_result_processed_id: Optional[int] = None
        # Último valor de OTOC calculado para detectar saídas constantes
        self._last_otoc_value: Optional[float] = None
        self._otoc_constant_count: int = 0
        self.invalid_metric: bool = False
        self._otoc_stagnant: bool = False
        self._invalid_metric_cycles: int = 0

        self.last_order_book_imbalance: Optional[float] = None
        self.last_funding_rate: Optional[float] = None
        self.scramble_random_on_next_build: bool = False
        self.pulse_detector = TranscendencePulseDetector()
        self.pulse_detected: bool = False

        # Logger para a instância, se não existir um global ou se quisermos um específico
        self.logger.info("QUALIAQuantumUniverse: Novos atributos adicionados.")

        self._last_built_circuit = None
        self._transpiled_symbol_tf_cache = {}

        if all(
            m is not None
            for m in (
                self.lqr_A_matrix,
                self.lqr_B_matrix,
                self.lqr_Q_matrix,
                self.lqr_R_matrix,
            )
        ):
            try:
                self.lqr_K_gain = compute_discrete_lqr_gain(
                    self.lqr_A_matrix,
                    self.lqr_B_matrix,
                    self.lqr_Q_matrix,
                    self.lqr_R_matrix,
                )
                self.logger.info("LQR K gain computed successfully.")
            except Exception as e:
                self.logger.warning(f"Falha ao calcular LQR K gain: {e}")

    def _cache_hit_hook(self, func_name: str, result: Any) -> None:
        """Update OTOC constancy and log on cache hits."""
        if func_name == "calculate_otoc":
            if getattr(self, "_otoc_stagnant", False):
                self.logger.info("OTOC stagnation – skipping heavy scrambling")
            try:
                self._check_otoc_constancy(result)
            except Exception as exc:  # pragma: no cover - defensive
                self.logger.debug("cache_hit_hook error: %s", exc)

    def _validate_critical_parameters(self) -> None:
        """
        Valida parâmetros críticos conforme as regras do QUALIA.
        Gera erro explícito se estiverem fora dos limites aceitáveis.
        """
        validations = [
            (
                self.n_qubits > 0,
                f"n_qubits deve ser positivo, recebido: {self.n_qubits}",
            ),
            (
                self.measure_frequency >= 0,
                f"measure_frequency deve ser não-negativo, recebido: {self.measure_frequency}",
            ),
            (
                0.0 <= self.thermal_coefficient <= 1.0,
                f"thermal_coefficient deve estar entre 0.0 e 1.0, recebido: {self.thermal_coefficient}",
            ),
            (
                0.0 <= self.hawking_factor <= 1.0,
                f"hawking_factor deve estar entre 0.0 e 1.0, recebido: {self.hawking_factor}",
            ),
            (self.shots > 0, f"shots deve ser positivo, recebido: {self.shots}"),
            (
                self.qpu_steps > 0,
                f"qpu_steps deve ser positivo, recebido: {self.qpu_steps}",
            ),
            (
                self.scr_depth > 0,
                f"scr_depth deve ser positivo, recebido: {self.scr_depth}",
            ),
            (
                self.base_lambda > 0,
                f"base_lambda deve ser positivo, recebido: {self.base_lambda}",
            ),
            (
                self.alpha >= 0,
                f"alpha deve ser não-negativo, recebido: {self.alpha}",
            ),
            (
                self.min_counts_diversity_ratio >= 0,
                "min_counts_diversity_ratio deve ser não-negativo",
            ),
            (
                self.max_circuit_depth > 0,
                f"max_circuit_depth deve ser positivo, recebido: {self.max_circuit_depth}",
            ),
            (
                self.max_circuit_operations > 0,
                f"max_circuit_operations deve ser positivo, recebido: {self.max_circuit_operations}",
            ),
        ]

        failed_validations = [msg for condition, msg in validations if not condition]

        if failed_validations:
            error_msg = "ERRO CRÍTICO: Validação de parâmetros falhou:\n" + "\n".join(
                f"- {msg}" for msg in failed_validations
            )
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info("Validação de parâmetros críticos concluída com sucesso")

    def _create_statevector_from_circuit_via_simulation(
        self, circuit_obj: Union[QuantumCircuit, Instruction]
    ) -> Statevector:
        """
        Cria um objeto Statevector a partir de um QuantumCircuit ou Instruction
        simulando o circuito no estado |0...0> e usando o construtor Statevector(numpy_array).
        Isso contorna chamadas potencialmente problemáticas para Statevector.from_instruction.
        """
        if not hasattr(circuit_obj, "num_qubits"):
            self.logger.error(
                f"Objeto {type(circuit_obj)} não tem num_qubits, retornando estado padrão."
            )
            return Statevector(np.array([1.0 + 0.0j], dtype=complex))

        if circuit_obj.num_qubits == 0:
            # Statevector para 0 qubits é [1.+0.j]
            return Statevector(np.array([1.0 + 0.0j], dtype=complex))

        # Assegurar que é um QuantumCircuit
        if isinstance(circuit_obj, Instruction):
            qc = QuantumCircuit(circuit_obj.num_qubits, name="temp_qc_from_instruction")
            qc.append(circuit_obj, range(circuit_obj.num_qubits))
        else:
            qc = circuit_obj

        # Primeiro tenta criar o statevector diretamente.
        try:
            sv = Statevector.from_instruction(qc)
            self.logger.debug("Statevector obtido via Statevector.from_instruction")
            return sv
        except Exception as e_from:
            # Qiskit 2.0.2 pode falhar nessa chamada; registrar e seguir para simulação
            self.logger.info(
                "Statevector.from_instruction falhou (%s); recorrendo à simulação",
                e_from,
            )

        # Determinar o backend para simulação
        sim_backend = None
        backend_actual_name_for_check = ""
        if self.backend:
            if callable(self.backend.name):
                backend_actual_name_for_check = self.backend.name().lower()
            elif isinstance(self.backend.name, str):
                backend_actual_name_for_check = self.backend.name.lower()
            else:
                # Fallback ou log de aviso se self.backend.name não for nem chamável nem string
                self.logger.warning(
                    f"self.backend.name não é chamável nem string: {type(self.backend.name)}"
                )

        if self.backend and "statevector" in backend_actual_name_for_check:
            sim_backend = self.backend
        else:
            from ..utils.backend import safe_get_backend

            sim_backend = safe_get_backend(
                "statevector_simulator", use_gpu=self.use_gpu
            )

            if sim_backend is None:
                try:
                    sim_backend = Aer.get_backend("statevector_simulator")
                    self.logger.debug(
                        "Usando um backend Aer.get_backend('statevector_simulator') temporário para _create_statevector_from_circuit_via_simulation."
                    )
                except QiskitError as e_aer:
                    self.logger.error(
                        f"Falha ao obter Aer.get_backend('statevector_simulator'): {e_aer}. Não é possível simular o circuito para criar Statevector."
                    )
                    raise RuntimeError(
                        "Aer statevector_simulator não está disponível para _create_statevector_from_circuit_via_simulation."
                    ) from e_aer

        if sim_backend is None:
            raise RuntimeError(
                "Nenhum backend de simulação disponível para _create_statevector_from_circuit_via_simulation."
            )

        # Qiskit 2.x ``AerSimulator`` não retorna automaticamente o ``statevector``
        # ao executar um circuito. A instrução ``save_statevector`` precisa ser
        # adicionada manualmente para que o resultado contenha o vetor de estado.
        try:
            qc.save_statevector(label="final_state")
            self.logger.debug(
                "Instrução save_statevector adicionada antes da simulação"
            )
        except Exception:  # pragma: no cover - falha inesperada ao salvar
            self.logger.debug(
                "Falha ao adicionar save_statevector; prosseguindo mesmo assim"
            )

        try:
            job = sim_backend.run(qc)
            result = job.result()
            try:
                numpy_sv = result.get_statevector(qc)
            except QiskitError as e_get:
                self.logger.warning(
                    "Falha em result.get_statevector(qc): %s. Tentando sem circuito.",
                    e_get,
                    exc_info=True,
                )
                try:
                    numpy_sv = result.get_statevector()
                    self.logger.debug(
                        "Statevector obtido via result.get_statevector()."
                    )
                except QiskitError as e_get_noarg:
                    self.logger.warning(
                        "Falha em result.get_statevector(): %s. Tentando via result.data(0).",
                        e_get_noarg,
                        exc_info=True,
                    )
                    try:
                        data_dict = result.data(0)
                        numpy_sv = data_dict.get("statevector")
                        if numpy_sv is None:
                            raise KeyError("statevector não presente em result.data(0)")
                        self.logger.debug(
                            "Statevector obtido via result.data(0)['statevector']."
                        )
                    except Exception as e_data:
                        self.logger.error(
                            "Falha ao recuperar statevector de todas as formas: %s",
                            e_data,
                            exc_info=True,
                        )
                        raise RuntimeError(
                            "Falha ao obter statevector do resultado"
                        ) from e_data
            return Statevector(numpy_sv)
        except QiskitError as e_sim:
            self.logger.error(
                f"QiskitError durante a simulação do circuito para criar Statevector: {e_sim}",
                exc_info=True,
            )
            raise RuntimeError(
                f"Falha ao simular circuito para criar Statevector: {e_sim}"
            ) from e_sim
        except Exception as e_gen:
            self.logger.error(
                f"Erro inesperado durante a simulação do circuito para criar Statevector: {e_gen}",
                exc_info=True,
            )
            raise RuntimeError(
                f"Erro inesperado ao simular circuito para criar Statevector: {e_gen}"
            ) from e_gen

    def _calculate_symbolic_entropy_metrics(
        self, expr: sympy.Expr, x_symbol: sympy.Symbol, qast_metrics: Dict[str, Any]
    ) -> Tuple[float, float, Dict[str, Any]]:
        """
        Calcula H_symb_prime (entropia simbólica normalizada) e delta H_symb_prime para uma dada expressão.
        Atualiza o dicionário qast_metrics com os resultados.

        Args:
            expr (sympy.Expr): A expressão simbólica (polinômio em x_symbol).
            x_symbol (sympy.Symbol): O símbolo usado na expressão (normalmente x).
            qast_metrics (Dict[str, Any]): O dicionário de métricas do QAST a ser atualizado.

        Returns:
            Tuple[float, float, Dict[str, Any]]: H_symb_prime, delta_H_symb_prime, qast_metrics atualizado.
        """
        poly_coeffs = (
            expr.as_poly(x_symbol).all_coeffs()
            if x_symbol in expr.free_symbols
            else [expr.evalf() if expr.is_Number else 0]
        )
        abs_coeffs = [abs(float(c)) for c in poly_coeffs]
        sum_abs_coeffs = sum(abs_coeffs)

        H_symb = 0.0
        if sum_abs_coeffs > 1e-9:
            probs = [ac / sum_abs_coeffs for ac in abs_coeffs]
            for p_i in probs:
                if p_i > 1e-9:
                    H_symb -= p_i * np.log2(p_i)

        degree = expr.as_poly(x_symbol).degree() if x_symbol in expr.free_symbols else 0
        H_symb_prime = 0.0
        if degree > 0:
            log_deg_plus_1 = np.log2(degree + 1)
            if log_deg_plus_1 > 1e-9:
                H_symb_prime = H_symb / log_deg_plus_1
            else:
                H_symb_prime = H_symb if degree == 1 else 0.0
        elif degree == 0:
            H_symb_prime = 0.0
        H_symb_prime = np.clip(H_symb_prime, 0.0, 1.0)

        qast_metrics["last_symbolic_entropy_raw"] = H_symb
        qast_metrics["last_symbolic_entropy_normalized"] = H_symb_prime

        prev_H_symb_prime_for_delta = qast_metrics.get(
            "avg_normalized_symbolic_entropy", H_symb_prime
        )
        delta_H_symb_prime = H_symb_prime - prev_H_symb_prime_for_delta
        qast_metrics["delta_normalized_symbolic_entropy"] = delta_H_symb_prime

        if "normalized_symbolic_entropy_history" not in qast_metrics or not isinstance(
            qast_metrics["normalized_symbolic_entropy_history"], list
        ):
            qast_metrics["normalized_symbolic_entropy_history"] = []

        norm_history = qast_metrics["normalized_symbolic_entropy_history"]
        norm_history.append(H_symb_prime)
        if len(norm_history) > getattr(self, "max_history_size", 100) // 5:
            norm_history.pop(0)

        current_avg_norm_entropy = (
            np.mean(norm_history) if norm_history else H_symb_prime
        )
        qast_metrics["avg_normalized_symbolic_entropy"] = current_avg_norm_entropy

        return H_symb_prime, delta_H_symb_prime, qast_metrics

    def qast_reflect_and_guide(
        self, qast: Dict[str, Any], input_value: float
    ) -> Tuple[sympy.Expr, float]:
        """
        Executa um ciclo simbólico de QAST (Quantum Algorithmic Symbolic Thought),
        processando um 'QualiaBlock' e registrando métricas simbólicas.
        Modificado para usar ``_calculate_symbolic_entropy_metrics``.

        Se a avaliação da derivada em pontos de amostragem falhar, a exceção é
        registrada e a contribuição correspondente é ignorada.
        """

        x = symbols("x")

        log_exponent = int(ceiling(math.log2(qast["Cycle"] + 2)))
        expr = x**log_exponent + input_value * x + qast["Cycle"]

        derivative = diff(expr, x)
        integral = integrate(expr, x)
        symbolic_result = {"Derivative": derivative, "Integral": integral}

        # Entropia simbólica antiga (baseada em count_ops e derivadas)
        # Poderia ser removida ou mantida para comparação/log legado.
        term1_legacy = (
            math.log2(
                1 + count_ops(expr) + expr.as_poly(x).degree(x)
                if x in expr.free_symbols
                else 0
            )
            if expr.args
            else 0
        )
        term2_legacy = 0
        for point in [0.5, 1.0, 1.5]:
            try:
                val = float(derivative.subs(x, point))
                term2_legacy += math.log2(1 + abs(val))
            except (ValueError, TypeError) as e:
                logger.warning(
                    "qast_reflect_and_guide: derivative evaluation failed at x=%s: %s",
                    point,
                    e,
                )
        ent_legacy = 0.6 * term1_legacy + 0.4 * (
            term2_legacy / 3 if term2_legacy > 0 else 0
        )

        try:
            coeff_raw = float(
                expr.as_poly(x).all_coeffs()[0]
                if x in expr.free_symbols
                else expr.evalf()
            )
            normalized_coeff_rotation = (coeff_raw % 8) / 8
        except Exception:
            normalized_coeff_rotation = (qast["Cycle"] % 8) / 8
            coeff_raw = 0  # Fallback

        log_entry = {
            "Cycle": qast["Cycle"],
            "Expr": str(expr),
            "Results": symbolic_result,
            "LegacyEntropy": ent_legacy,  # Log da entropia antiga
            "Raw_Coeff_for_Rotation": coeff_raw,
            "Normalized_Coeff_for_Rotation": normalized_coeff_rotation,
        }
        qast.setdefault("Logs", []).append(log_entry)

        qast_metrics = qast.get("Metrics", {})
        if not isinstance(qast_metrics, dict):
            qast_metrics = {
                "Adaptability": 1.0,
                "Coherence": 0.95,
                "Integration": 0.85,
            }

        # Calcular e atualizar métricas de entropia simbólica normalizada usando o novo método
        (
            H_symb_prime,
            delta_H_symb_prime,
            updated_qast_metrics,
        ) = self._calculate_symbolic_entropy_metrics(expr, x, qast_metrics)

        # Adicionar/atualizar outras métricas que eram feitas aqui antes
        updated_qast_metrics["last_symbolic_entropy_legacy"] = ent_legacy
        updated_qast_metrics["last_raw_coeff"] = coeff_raw  # Coeff para rotação
        updated_qast_metrics["last_normalized_coeff"] = (
            normalized_coeff_rotation  # Coeff normalizado para rotação
        )
        updated_qast_metrics["cycle_count"] = qast["Cycle"]

        qast["Metrics"] = updated_qast_metrics
        qast["Cycle"] += 1
        return expr, H_symb_prime  # Retorna a H_symb_prime (normalizada)

    def _define_deutsch_interaction_circuit(self):
        """
        Define um circuito de interação para o modelo de Deutsch CTC.

        Returns:
            QuantumCircuit: Um circuito quântico que implementa a interação entre CR e CTC
        """
        try:
            from ..utils.ctc_utils import create_deutsch_ctc_interaction

            # Use os parâmetros padrão ou os definidos no objeto
            n_qubits_cr = getattr(self, "n_qubits", 4) - getattr(
                self, "num_ctc_qubits", 1
            )
            n_qubits_ctc = getattr(self, "num_ctc_qubits", 1)
            interaction_type = getattr(self, "ctc_interaction_type", "swap")

            # Cria o circuito de interação
            return create_deutsch_ctc_interaction(
                n_qubits_cr=n_qubits_cr,
                n_qubits_ctc=n_qubits_ctc,
                interaction_type=interaction_type,
            )
        except (ImportError, Exception) as e:
            import warnings

            warnings.warn(
                f"Erro ao criar circuito de interação Deutsch: {e}. Criando circuito vazio."
            )

            # Fallback: criar um circuito vazio se a importação falhar
            try:
                from qiskit import QuantumCircuit

                return QuantumCircuit(getattr(self, "n_qubits", 4))
            except ImportError:
                return None

        # Inicializar métricas
        self.metrics = QUALIAMetrics(max_history_size=self.max_history_size)

        # _lambda_gravity e outros
        self.metrics_history = self.metrics
        self.current_sv = None
        self.initial_sv = None
        self._last_counts = {}
        # self._qiskit_backend = safe_get_backend("aer_simulator_statevector")
        self._last_run_steps = 0
        self.thermal_active_on_last_run = False
        self.temperature_on_last_run = 0.01
        self.retro_mode_on_last_run = "none"
        self.scr_feedback_factor_on_last_run = 0.1
        self.measure_frequency_on_last_run = 1
        self.retro_strength_on_last_run = self.retro_strength
        self.sv = None
        # --- QuantumPatternMemory ---
        self.pattern_memory = get_qpm_instance()

        if self.last_result is not None:
            self._schedule_update_last_statevector()

    def _schedule_update_last_statevector(self) -> None:
        """Schedule :meth:`update_last_statevector` respecting the current loop."""
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            asyncio.run(self.update_last_statevector())
        else:
            loop.create_task(self.update_last_statevector())

    def update_last_statevector_sync(self):
        """Synchronous wrapper for :meth:`update_last_statevector`.

        This method is intended for external callers so they can run the
        asynchronous update either with ``asyncio.run`` from a synchronous
        context or ``await`` it directly.
        """
        return self.update_last_statevector()

    async def update_last_statevector(self) -> None:
        """Update :attr:`last_statevector` using :attr:`last_result`."""
        if self.last_result is None:
            logger.debug(
                "QQU.update_last_statevector: last_result é None. Nada a fazer."
            )
            return

        result_id = id(self.last_result)
        if self._should_use_cached_statevector(result_id):
            return

        new_statevector = self._statevector_from_result()
        if new_statevector is None:
            new_statevector = self._fallback_statevector()

        await self._store_statevector(new_statevector)

    def _should_use_cached_statevector(self, result_id: int) -> bool:
        if (
            result_id == self._last_result_processed_id
            and self.last_statevector is not None
        ):
            logger.debug(
                "QQU.update_last_statevector: reutilizando statevector em cache para o mesmo resultado."
            )
            return True
        return False

    def _statevector_from_result(self) -> Optional[Statevector]:
        new_statevector = None

        if isinstance(self.last_result, Statevector):
            new_statevector = self.last_result
            logger.debug(
                "QQU.update_last_statevector: Encontrado Statevector diretamente em self.last_result."
            )

        if new_statevector is None:
            new_statevector = try_get_statevector_from_data(self.last_result)
            if new_statevector is not None:
                debug_msg = (
                    "QQU.update_last_statevector: Statevector recuperado com sucesso "
                    "via _try_get_statevector_from_data."
                )
                logger.debug(debug_msg)

        if new_statevector is None:
            new_statevector = try_get_statevector_direct(
                self.last_result, self._last_built_circuit
            )
            if new_statevector is not None:
                logger.debug(
                    "QQU.update_last_statevector: Statevector recuperado via _try_get_statevector_direct (fallback)."
                )
        return new_statevector

    def _fallback_statevector(self) -> Optional[Statevector]:
        try:
            return self.last_result.get_statevector("statevector")
        except Exception:
            try:
                return self.last_result.get_statevector("final_run_state")
            except Exception:
                logger.warning("Nenhum statevector encontrado em result.")
        return None

    async def _store_statevector(self, new_statevector: Optional[Statevector]) -> None:
        async with self._sv_lock:
            self._last_result_processed_id = id(self.last_result)
            self.last_statevector = new_statevector
            self.sv = new_statevector
            if new_statevector is None:
                logger.warning(
                    "QQU.update_last_statevector: Não foi possível extrair o statevector do último resultado."
                )
                return

            if not isinstance(new_statevector, Statevector):
                logger.warning(
                    "QQU.update_last_statevector: Objeto recuperado não é Statevector (tipo: %s). Tentando converter.",
                    type(new_statevector),
                )

            self.sv_history.append(new_statevector)
            logger.debug(
                "QQU: last_sv & current_sv updated. Norma: %s. Hist SVs: %s",
                np.linalg.norm(new_statevector.data),
                len(self.sv_history),
            )

    def _extract_statevector(
        self, result: Any, label: str = "final_run_state"
    ) -> Optional[Statevector]:
        """Obtem um :class:`Statevector` a partir de um objeto Result.

        Primeiro tenta recuperar ``result.results[0].data[label]`` e,
        em caso de falha, utiliza ``result.get_statevector()``.
        """
        if hasattr(result, "results") and result.results:
            try:
                data_obj = result.results[0].data
                data_dict = (
                    data_obj.to_dict() if hasattr(data_obj, "to_dict") else data_obj
                )
                if isinstance(data_dict, dict) and label in data_dict:
                    sv = convert_to_statevector(data_dict[label])
                    if sv is not None:
                        return sv
            except Exception as exc:  # pragma: no cover - logging only
                logger.debug(
                    "Falha ao acessar result.results[0].data['%s']: %s", label, exc
                )

        if hasattr(result, "get_statevector"):
            try:
                return result.get_statevector()
            except Exception as exc:  # pragma: no cover - logging only
                logger.debug("result.get_statevector() falhou: %s", exc)

        return None

    # --- Helper de gravidade informacional ---

    def _lambda_gravity(self) -> float:
        # Assegurar que estamos acessando a chave correta e tratando lista vazia.

        if hasattr(self, "metrics") and self.metrics is not None:
            if hasattr(self.metrics, "get_metrics_dict"):
                current_metrics = self.metrics.get_metrics_dict()
            elif isinstance(self.metrics, dict):
                current_metrics = self.metrics
            else:  # Fallback se metrics não for nem Pydantic/Dataclass nem dict
                logger.warning(
                    "_lambda_gravity: metrics não é um tipo esperado. Usando base_lambda."
                )
                return self.base_lambda
        else:
            logger.warning(
                "_lambda_gravity: self.metrics não está definido. Usando base_lambda."
            )
            return self.base_lambda

        quantum_entropy_history = current_metrics.get("quantum_entropy", [])
        valid_entropies = [
            e
            for e in quantum_entropy_history
            if e is not None and isinstance(e, (float, int))
        ]

        if not valid_entropies:
            # logger.debug("_lambda_gravity: Histórico de quantum_entropy vazio ou sem valores válidos. Usando base_lambda.")
            return self.base_lambda

        # Usar a média das últimas N entropias, ou todas se menos de N.
        # N_avg = 5 # Exemplo: média das últimas 5. Pode ser um parâmetro da classe.
        # recent_entropies = valid_entropies[-N_avg:]
        # mean_entropy = float(np.mean(recent_entropies))

        # Usar a última entropia válida para resposta mais rápida
        last_valid_entropy = valid_entropies[-1]

        # Fator de ajuste baseado na entropia. O fator lambda_factor_multiplier
        # já está no __init__, podemos usá-lo aqui.
        lambda_adjustment = (
            self.alpha * last_valid_entropy * self.lambda_factor_multiplier
        )

        # O lambda não deve se tornar negativo ou excessivamente grande.
        # Aplicar limites ou uma função de saturação pode ser útil.
        # Exemplo simples: clamp entre 0.1*base_lambda e 10*base_lambda
        calculated_lambda = self.base_lambda * (1 + lambda_adjustment)
        # min_lambda = 0.1 * self.base_lambda
        # max_lambda = 10.0 * self.base_lambda
        # final_lambda = np.clip(calculated_lambda, min_lambda, max_lambda)

        # logger.debug(f"_lambda_gravity: Last Entropy={last_valid_entropy:.4f}, Adjustment={lambda_adjustment:.4f}, Calculated Lambda={calculated_lambda:.4f}, Final Lambda={final_lambda:.4f}")
        # return final_lambda

        # A discussão anterior era sobre usar self.metrics_history['quantum_entropy'],
        # vamos manter a média como estava antes, mas da fonte correta.
        mean_entropy = float(np.mean(valid_entropies))
        calculated_lambda = self.base_lambda * (1 + self.alpha * mean_entropy)

        if self.qast_feedback_enabled and self.qast_metrics_snapshot_for_lambda:
            avg_symbolic_entropy = self.qast_metrics_snapshot_for_lambda.get(
                "avg_symbolic_entropy", 0.0
            )
            # A entropia simbólica pode variar; normalizar ou usar um fator para ajustar sua influência.
            # Exemplo: entropia simbólica mais alta -> lambda ligeiramente maior (mais "atividade" ou "caos")
            # ou menor (mais "ordem" ou "foco"). Vamos assumir que maior entropia simbólica aumenta lambda.
            # O fator qast_influence_on_lambda_factor controla a magnitude.
            # Ajuste: (1 + self.qast_influence_on_lambda_factor * (avg_symbolic_entropy / X)) onde X é um normalizador
            # Se avg_symbolic_entropy é ~1-5, e qast_influence_on_lambda_factor é 0.1, o ajuste é pequeno.
            # Para simplificar, vamos apenas adicionar um termo proporcional.
            qast_adjustment = (
                self.qast_influence_on_lambda_factor * avg_symbolic_entropy
            )
            calculated_lambda += (
                self.base_lambda * qast_adjustment
            )  # Adiciona ao lambda base ou ao lambda já ajustado pela entropia quântica
            logger.debug(
                f"_lambda_gravity: QAST Avg Symbolic Entropy={avg_symbolic_entropy:.4f}, QAST Adjustment Factor={qast_adjustment:.4f}, Lambda com QAST={calculated_lambda:.4f}"
            )

        logger.debug(
            f"_lambda_gravity: Mean Quantum Entropy={mean_entropy:.4f}, Calculated Lambda={calculated_lambda:.4f}"
        )
        return calculated_lambda

    def get_latest_metrics(
        self, get_last_value_from_list: bool = True
    ) -> Dict[str, Any]:
        """Return a snapshot of the universe metrics."""

        if not hasattr(self, "metrics") or self.metrics is None:
            logger.warning("get_latest_metrics: metrics container não inicializado.")
            return {"lambda_gravity": self._lambda_gravity()}

        if hasattr(self.metrics, "get_metrics_dict"):
            all_metrics = self.metrics.get_metrics_dict()
        elif isinstance(self.metrics, dict):
            all_metrics = self.metrics
        else:
            logger.warning("get_latest_metrics: metrics em formato inesperado.")
            all_metrics = {}

        snapshot: Dict[str, Any] = {}
        for key, value in all_metrics.items():
            if get_last_value_from_list and isinstance(value, list):
                snapshot[key] = value[-1] if value else []
            else:
                snapshot[key] = value

        snapshot["lambda_gravity"] = self._lambda_gravity()
        if "max_history_size" not in snapshot and hasattr(
            self.metrics, "max_history_size"
        ):
            snapshot["max_history_size"] = self.metrics.max_history_size
        return snapshot

    # --- Estado inicial via QFT ---

    def _create_hadamard_state(self) -> Statevector:
        """Return a multi-qubit Hadamard state."""

        if self.n_qubits == 0:
            return Statevector.from_label("")

        circuit = QuantumCircuit(self.n_qubits)
        circuit.h(range(self.n_qubits))
        return self._create_statevector_from_circuit_via_simulation(circuit)

    def _create_qft_state(self, allow_qft_fallback: bool) -> Statevector:
        """Return the Quantum Fourier Transform state."""

        try:
            self.logger.info(
                "Tentando criar estado inicial QFT para %s qubits.",
                self.n_qubits,
            )

            if self.n_qubits > MAX_EXACT_QFT_QUBITS:
                if allow_qft_fallback:
                    self.logger.warning(
                        "Numero de qubits %s excede MAX_EXACT_QFT_QUBITS=%s; usando fallback para estado aleatorio.",
                        self.n_qubits,
                        MAX_EXACT_QFT_QUBITS,
                    )
                    cached = get_fallback_state(self.n_qubits)
                    if cached is not None:
                        return cached
                    sv_fallback = self._create_random_amplitudes_state()
                    cache_fallback_state(self.n_qubits, sv_fallback)
                    return sv_fallback
                self.logger.info(
                    "Executando QFT mesmo acima do limite de %s qubits.",
                    MAX_EXACT_QFT_QUBITS,
                )

            if self.n_qubits == 0:
                return Statevector(np.array([1.0 + 0.0j], dtype=complex))

            def builder(n: int) -> Statevector:
                prep = QuantumCircuit(n)
                prep.h(range(n))
                qft_circ = get_qft_circuit(
                    n, lambda m: create_qft_circuit(m, add_measurements=False)
                )
                prep.compose(qft_circ, inplace=True)
                prep = prep.decompose()
                return self._create_statevector_from_circuit_via_simulation(prep)

            return get_qft_statevector(self.n_qubits, builder)
        except QiskitError as exc:
            if allow_qft_fallback:
                self.logger.warning(
                    "Falha ao criar QFT para %s qubits: %s. Usando fallback para Hadamard.",
                    self.n_qubits,
                    exc,
                )
                return self._create_hadamard_state()

            self.logger.error(
                "Falha ao criar QFT para %s qubits (fallback desabilitado): %s",
                self.n_qubits,
                exc,
            )
            raise

    def _create_zero_state(self) -> Statevector:
        """Return the ``|0...0>`` state."""

        if self.n_qubits > 0:
            return Statevector.from_label("0" * self.n_qubits)
        return Statevector.from_label("")

    def _stateprep_state(self, vector: np.ndarray, state_type: str) -> Statevector:
        """Helper to create states that rely on :class:`StatePreparation`."""

        try:
            from qiskit.circuit.library import StatePreparation

            qc = QuantumCircuit(self.n_qubits)
            state_prep = StatePreparation(vector)
            qc.append(state_prep, range(self.n_qubits))
            return self._create_statevector_from_circuit_via_simulation(qc)
        except (ImportError, NameError) as exc:
            self.logger.error(
                "StatePreparation não encontrado para criar '%s': %s",
                state_type,
                exc,
            )
            return self._create_hadamard_state()
        except Exception as exc:  # pragma: no cover - defensive
            self.logger.error("Erro ao preparar estado '%s': %s", state_type, exc)
            return self._create_hadamard_state()

    def _create_ghz_state(self) -> Statevector:
        """Return a GHZ state for ``n_qubits`` qubits."""

        if self.n_qubits == 0:
            return Statevector(np.array([1.0], dtype=complex))
        if self.n_qubits == 1:
            ghz_vector = np.array([1 / np.sqrt(2), 1 / np.sqrt(2)])
            return Statevector(ghz_vector)

        ghz_vector = np.zeros(2**self.n_qubits)
        ghz_vector[0] = 1 / np.sqrt(2)
        ghz_vector[-1] = 1 / np.sqrt(2)
        return self._stateprep_state(ghz_vector, "ghz")

    def _create_w_state(self) -> Statevector:
        """Return a W state for ``n_qubits`` qubits."""

        if self.n_qubits == 0:
            return Statevector(np.array([1.0], dtype=complex))
        if self.n_qubits == 1:
            return Statevector(np.array([0.0, 1.0]))

        w_vector = np.zeros(2**self.n_qubits)
        for i in range(self.n_qubits):
            w_vector[2**i] = 1 / np.sqrt(self.n_qubits)
        return self._stateprep_state(w_vector, "w_state")

    def _create_random_amplitudes_state(self) -> Statevector:
        """Return a state with random amplitudes."""

        if self.n_qubits == 0:
            return Statevector(np.array([1.0], dtype=complex))

        rng = np.random.default_rng()
        random_vector = rng.standard_normal(
            2**self.n_qubits
        ) + 1j * rng.standard_normal(2**self.n_qubits)
        random_vector = random_vector / np.linalg.norm(random_vector)
        return self._stateprep_state(random_vector, "random_amplitudes")

    def _fallback_unknown_state(self, specific_type: str) -> Statevector:
        """Fallback used when an unknown state type is requested."""

        self.logger.warning(
            "Tipo de estado inicial desconhecido: '%s'. Fallback para Hadamard.",
            specific_type,
        )

        return (
            self._create_hadamard_state()
            if self.n_qubits > 0
            else Statevector(np.array([1.0], dtype=complex))
        )

    def _create_initial_state(
        self,
        specific_type: str,
        initial_state_vector_np: Optional[np.ndarray] = None,
        allow_qft_fallback: bool = True,
    ) -> Union[Statevector, QuantumCircuit, None]:
        self.logger.info(
            "_create_initial_state chamado com specific_type=%s", specific_type
        )

        # Prioridade: se um vetor de estado explícito for fornecido,
        # utilize-o independentemente do tipo específico solicitado.
        if initial_state_vector_np is not None:
            result = Statevector(initial_state_vector_np)
            self.current_sv = result
            return result

        state_dispatch: Dict[str, Callable[[], Statevector]] = {
            "hadamard": self._create_hadamard_state,
            "qft": lambda: self._create_qft_state(allow_qft_fallback),
            "zero": self._create_zero_state,
            "ghz": self._create_ghz_state,
            "w_state": self._create_w_state,
            "random_amplitudes": self._create_random_amplitudes_state,
        }

        creator = state_dispatch.get(specific_type)
        result = creator() if creator else self._fallback_unknown_state(specific_type)

        self.current_sv = result
        return result

    # --- Construção do circuito completo ---
    def _prepare_initial_state(
        self,
        circuit: QuantumCircuit,
        initial_encoded_circuit: Optional[QuantumCircuit],
        initial_type_fallback: str,
    ) -> None:
        """Aplica o estado inicial ao circuito fornecido.

        Esta rotina tenta respeitar um circuito codificado customizado ou,
        na ausência dele, utiliza o mecanismo de criação de estado interno.
        Complementa com rotações e entrelaçamento básico para ampliar o espaço de
        busca quântica.
        """

        initial_custom_state_applied_to_bh = False

        if initial_encoded_circuit:
            if initial_encoded_circuit.num_qubits > self.n_qubits:
                logger.warning(
                    f"Circuito inicial codificado ({initial_encoded_circuit.num_qubits} qubits) "
                    f"excede os qubits do BH ({self.n_qubits}). Tentando adaptar e aplicar subconjunto."
                )

                temp_adapted_circuit = QuantumCircuit(
                    self.n_qubits, name="adapted_encoded_subset"
                )
                processed_instructions = 0
                for circuit_instruction in initial_encoded_circuit.data:
                    instr_obj = circuit_instruction.operation
                    qargs = circuit_instruction.qubits
                    cargs = circuit_instruction.clbits
                    original_indices = [
                        getattr(q, "index", getattr(q, "_index", None)) for q in qargs
                    ]

                    # YAA CORREÇÃO: Verificar se a instrução pode ser adaptada para o número de qubits disponível
                    # Se a instrução requer mais qubits do que temos disponível, tentar adaptar ou ignorar
                    if instr_obj.num_qubits > self.n_qubits:
                        logger.warning(
                            "Instrução %s requer %s qubits, mas o universo possui apenas %s qubits. Ignorando.",
                            instr_obj.name,
                            instr_obj.num_qubits,
                            self.n_qubits,
                        )
                        continue

                    # Verificar se todos os índices dos qubits estão dentro do range válido
                    valid_indices = [
                        idx
                        for idx in original_indices
                        if idx is not None and idx < self.n_qubits
                    ]

                    if (
                        len(valid_indices) == instr_obj.num_qubits
                        and len(valid_indices) <= self.n_qubits
                    ):
                        mapped_qargs_for_temp = [
                            temp_adapted_circuit.qubits[idx] for idx in valid_indices
                        ]
                        temp_adapted_circuit.append(
                            instr_obj, mapped_qargs_for_temp, cargs
                        )
                        processed_instructions += 1
                    else:
                        warn_msg = (
                            "Instrução %s requer %s qubits; apenas %s qubits "
                            "válidos disponíveis no subset adaptado. Ignorando."
                        )
                        logger.warning(
                            warn_msg,
                            instr_obj.name,
                            instr_obj.num_qubits,
                            len(valid_indices),
                        )

                if temp_adapted_circuit.data and processed_instructions > 0:
                    circuit.append(temp_adapted_circuit.to_instruction(), self.qr_bh)
                    logger.info(
                        "Aplicado subconjunto adaptado do circuito inicial codificado "
                        f"({temp_adapted_circuit.num_qubits} qubits, {processed_instructions} instruções) ao qr_bh."
                    )
                    initial_custom_state_applied_to_bh = True
                else:
                    logger.warning(
                        "Não foi possível aplicar subconjunto adaptado do circuito inicial codificado. "
                        "Nenhuma instrução válida ou todas as instruções usavam qubits fora do range do BH. "
                        "BH permanecerá no estado |0...0> ou estado fallback."
                    )

            elif initial_encoded_circuit.num_qubits <= self.n_qubits:
                target_qubits_bh = self.qr_bh[: initial_encoded_circuit.num_qubits]
                circuit.append(
                    initial_encoded_circuit.to_instruction(), target_qubits_bh
                )
                logger.info(
                    f"Aplicado circuito inicial codificado ({initial_encoded_circuit.num_qubits} qubits) "
                    f"aos qubits {initial_encoded_circuit.num_qubits} iniciais de qr_bh."
                )
                initial_custom_state_applied_to_bh = True

        if not initial_custom_state_applied_to_bh:
            logger.info(
                "Nenhum circuito inicial codificado fornecido ou aplicado com sucesso. "
                f"Tentando estado inicial fallback '{initial_type_fallback}' para qr_bh."
            )
            initial_state_obj = self._create_initial_state(
                specific_type=initial_type_fallback,
                initial_state_vector_np=self.initial_state_vector,
                allow_qft_fallback=self.allow_qft_fallback,
            )
            if isinstance(initial_state_obj, Statevector):
                prepare_statevector(circuit, initial_state_obj.data, qubits=self.qr_bh)
                logger.debug(
                    "Estado inicial aplicado via prepare_statevector() no qr_bh."
                )
            elif isinstance(initial_state_obj, QuantumCircuit):
                circuit.compose(
                    initial_state_obj,
                    qubits=self.qr_bh[: initial_state_obj.num_qubits],
                    inplace=True,
                )
                logger.debug(
                    "Circuito inicial composto no início do circuito principal."
                )
            else:
                logger.warning(
                    "Objeto retornado por _create_initial_state não pôde ser aplicado ao circuito."
                )

        if self.n_qubits >= 3:
            circuit.cx(self.qr_bh[0], self.qr_bh[1])
            circuit.cx(self.qr_bh[1], self.qr_bh[2])
        if self.n_qubits >= 4:
            circuit.ry(np.pi / 8, self.qr_bh[3])

    def _apply_scrambling_layer(self, circuit: QuantumCircuit, depth: int) -> None:
        """Aplica uma camada de scrambling ao circuito."""
        advanced_scrambler(
            circuit,
            self.qr_bh[:],
            depth,
            style=self.entanglement_style,
        )

    def _apply_qast_modulation(
        self, circuit: QuantumCircuit, lam: float, qast_value: float
    ) -> None:
        """Aplica modulação de rotação baseada em entrada QAST."""
        rotation_angle_influence = 0.1
        normalized_effect = (qast_value % 8.0) / 8.0
        angle = 2 * np.pi * normalized_effect * lam * rotation_angle_influence
        try:
            circuit.ry(angle, self.qr_bh[0])
            logger.debug(
                "Build_circuit: Aplicando RY com qast_input=%0.3f, norm_effect=%0.3f, angle=%0.3f em qr_bh[0]",
                qast_value,
                normalized_effect,
                angle,
            )
        except Exception as exc:  # pragma: no cover - defensivo
            logger.warning(
                "Build_circuit: Falha ao aplicar modulação QAST com input %s: %s. Usando fallback.",
                qast_value,
                exc,
            )
            circuit.ry(lam * rotation_angle_influence, self.qr_bh[0])

    def _measure_and_feedback(
        self, circuit: QuantumCircuit, retro_mode: str, step_idx: int
    ) -> None:
        """Realiza medidas e aplica feedback retrocausal se configurado."""
        rad_count = min(self.qr_radiation.size, self.cr_radiation.size)
        if self.qr_radiation.size != self.cr_radiation.size:
            logger.warning(
                "Tamanho de qr_radiation (%s) difere de cr_radiation (%s). Usando o menor valor.",
                self.qr_radiation.size,
                self.cr_radiation.size,
            )
        circuit.h(self.qr_radiation[:rad_count])
        circuit.measure(self.qr_radiation[:rad_count], self.cr_radiation[:rad_count])
        if getattr(self, "cr_main", None):
            circuit.measure(self.qr_bh, self.cr_main)

        if retro_mode == "simple" and step_idx > 0:
            circuit.h(self.qr_bh[0])
            with circuit.if_test((self.cr_radiation[0], 1)):
                circuit.x(self.qr_bh[0])
        elif retro_mode == "CTC" and step_idx > 0:
            circuit.h(self.qr_bh[1])
            with circuit.if_test((self.cr_radiation[0], 1)):
                circuit.x(self.qr_bh[1])
            circuit.h(self.qr_bh[1])
        elif retro_mode == "future_phase_echo" and step_idx > 0:
            angle = self.retro_strength * 2 * np.pi
            with circuit.if_test((self.cr_radiation[0], 1)):
                circuit.rz(angle, self.qr_bh[0])

    def _reset_radiation_qubits(self, circuit: QuantumCircuit) -> None:
        """Reseta qubits de radiação periodicamente."""
        rad_count = min(self.qr_radiation.size, self.cr_radiation.size)
        circuit.reset(self.qr_radiation[:rad_count])

    def _apply_quantum_operators(
        self,
        circuit: QuantumCircuit,
        *,
        steps: int,
        apply_scrambling: bool,
        scr_depth_override: Optional[int],
        qast_input_for_circuit: Optional[float],
        measure_frequency: int,
        thermal: bool,
        temperature: float,
        retro_mode: str,
    ) -> None:
        """Aplica as operações quânticas principais passo a passo."""
        for t_idx in range(steps):
            current_scr_depth = (
                scr_depth_override if scr_depth_override is not None else self.scr_depth
            )
            if apply_scrambling and current_scr_depth > 0:
                self._apply_scrambling_layer(circuit, current_scr_depth)

            lam = self._lambda_gravity()
            circuit.rz(lam, self.qr_bh[t_idx % self.n_qubits])

            if qast_input_for_circuit is not None:
                self._apply_qast_modulation(circuit, lam, qast_input_for_circuit)

            if thermal:
                emit_hawking(circuit, self.qr_bh[-1], self.qr_radiation[0], temperature)
            else:
                circuit.swap(self.qr_bh[-1], self.qr_radiation[0])

            if (
                measure_frequency <= 0
                or (t_idx + 1) % measure_frequency == 0
                or t_idx == steps - 1
            ):
                self._measure_and_feedback(circuit, retro_mode, t_idx)

            if t_idx > 0 and t_idx % 3 == 0:
                self._reset_radiation_qubits(circuit)

    def build_circuit(
        self,
        steps: int,
        *,
        thermal: bool = True,
        temperature: float = 0.05,
        retro_mode: str = "none",
        initial_encoded_circuit: Optional[QuantumCircuit] = None,
        initial_type_fallback: str = "qft",
        qast_input_for_circuit: Optional[float] = None,
        measure_frequency: int = 1,
        check_mid_circuit: bool = True,
        scr_depth_override: Optional[int] = None,
        apply_scrambling: bool = True,
        use_cache: bool = True,
        trace_id: Optional[str] = None,
    ) -> QuantumCircuit:
        """Constrói o circuito quântico principal para a simulação.

        Parameters
        ----------
        steps : int
            Número de passos de evolução do circuito.
        thermal : bool, optional
            Se ``True``, aplica ruído térmico.
        temperature : float, optional
            Temperatura do ruído térmico.
        retro_mode : str
            Modo de retrocausalidade a ser utilizado.
        initial_encoded_circuit : QuantumCircuit, optional
            Circuito já codificado que deve servir de ponto de partida.
        initial_type_fallback : str
            Tipo de estado inicial se nenhum circuito codificado for passado.
        qast_input_for_circuit : float, optional
            Valor numérico de entrada para modulação do circuito.
        measure_frequency : int
            Frequência de medições parciais no circuito.
        check_mid_circuit : bool
            Se ``True``, realiza verificações de integridade após cada passo.
        scr_depth_override : int, optional
            Profundidade de scrambling a ser usada no lugar de ``self.scr_depth``.
        apply_scrambling : bool, optional
            Define se as camadas de scrambling devem ser aplicadas.
        use_cache : bool, optional
            Se ``True``, reutiliza circuitos idênticos a partir do cache.

        Returns
        -------
        QuantumCircuit
            Circuito construído de acordo com os parâmetros fornecidos.

        Notes
        -----
        Se a preservação do registrador de radiação para testes falhar, o evento
        é logado e o circuito resultante ainda é retornado.
        """
        trace_id = trace_id or uuid.uuid4().hex
        self.run_count += 1
        tags = [f"trace_id:{trace_id}"] if self.statsd else None
        start_time = time.perf_counter()
        if trace:
            tracer = trace.get_tracer(__name__)
            span_cm = tracer.start_as_current_span(
                "universe.build_circuit",
                attributes={"trace_id": trace_id},
            )
        else:
            from contextlib import nullcontext

            span_cm = nullcontext()

        with span_cm:
            # Verificar cache se habilitado e não há circuito inicial customizado
            if use_cache and initial_encoded_circuit is None:
                cache_key = self._get_circuit_cache_key(
                    steps,
                    thermal,
                    temperature,
                    retro_mode,
                    measure_frequency,
                    scr_depth_override,
                    apply_scrambling,
                )

                if cache_key in self._circuit_cache:
                    logger.debug(f"Circuito obtido do cache: {cache_key}")
                    self._last_circuit_from_cache = True
                    qc_cached = self._circuit_cache[cache_key].copy()
                    self._circuit_cache.move_to_end(cache_key)
                    elapsed_ms = (time.perf_counter() - start_time) * 1000
                    if hasattr(self, "metrics"):
                        self.metrics.add_entry("build_circuit_ms", elapsed_ms)
                        self.metrics.add_entry("build_circuit_count", 1)
                    if self.statsd:
                        self.statsd.timing(
                            "universe.build_circuit_ms", elapsed_ms, tags=tags
                        )
                        self.statsd.increment(
                            "universe.build_circuit_invocations", tags=tags
                        )
                    logger.info(
                        "TraceID: %s - build_circuit obtido do cache em %.2f ms",
                        trace_id,
                        elapsed_ms,
                    )
                    return qc_cached
            else:
                cache_key = None

            self._last_circuit_from_cache = False

        logger.debug(
            f"Iniciando build_circuit: steps={steps}, thermal={thermal}, temp={temperature}, "
            f"retro_mode={retro_mode}, initial_encoded_circuit provided: {initial_encoded_circuit is not None}, "
            f"initial_type_fallback='{initial_type_fallback}', qast_input: {qast_input_for_circuit is not None}"
        )

        # --- Fallback defensivo: garantir existência dos registradores fundamentais ---
        from qiskit import QuantumRegister, ClassicalRegister

        if not hasattr(self, "qr_bh") or self.qr_bh is None:
            self.logger.warning("qr_bh não estava inicializado! Criando fallback.")
            self.qr_bh = QuantumRegister(getattr(self, "n_qubits", 1), "bh")
        if not hasattr(self, "qr_radiation") or self.qr_radiation is None:
            self.logger.warning(
                "qr_radiation não estava inicializado! Criando fallback."
            )
            self.qr_radiation = QuantumRegister(1, "radiation")
        if not hasattr(self, "cr_radiation") or self.cr_radiation is None:
            self.logger.warning(
                "cr_radiation não estava inicializado! Criando fallback."
            )
            self.cr_radiation = ClassicalRegister(1, "cr_radiation")
        # Configuração inicial dos registros
        qc = QuantumCircuit(
            self.qr_bh, self.qr_radiation, self.cr_radiation, name="QUALIA_BH_Rad"
        )
        # Garantir que os registradores referenciados externamente sejam as
        # mesmas instâncias mantidas pelo circuito, acessando via propriedade
        # pública ``register``
        self.qr_radiation = qc.qubits[self.qr_bh.size]._register
        self.cr_radiation = qc.clbits[0]._register
        if getattr(self, "use_ctc", False):
            qc.add_register(self.qr_ctc)
            if getattr(self, "ctc_measures_on_main_circuit", False):
                qc.add_register(self.cr_ctc_measure)

        if getattr(self, "cr_main", None):
            qc.add_register(self.cr_main)

        self._prepare_initial_state(qc, initial_encoded_circuit, initial_type_fallback)

        self._apply_quantum_operators(
            qc,
            steps=steps,
            apply_scrambling=apply_scrambling,
            scr_depth_override=scr_depth_override,
            qast_input_for_circuit=qast_input_for_circuit,
            measure_frequency=measure_frequency,
            thermal=thermal,
            temperature=temperature,
            retro_mode=retro_mode,
        )

        # Medição final se necessário
        if steps > 0 and (
            measure_frequency <= 0 or steps % measure_frequency != 0 or steps == 1
        ):
            rad_count = min(self.qr_radiation.size, self.cr_radiation.size)
            qc.measure(self.qr_radiation[:rad_count], self.cr_radiation[:rad_count])
            # A medição final já preserva a referência do registrador de radiação
            # para os testes sem manipular atributos privados
            if getattr(self, "cr_main", None):
                qc.measure(self.qr_bh, self.cr_main)

        if getattr(self, "scramble_random_on_next_build", False):
            _apply_random_scrambling_layer(qc, self.qr_bh[:])
            self.scramble_random_on_next_build = False

        # Atualizar referências de registradores após todas as operações
        self.qr_radiation = qc.qubits[self.qr_bh.size]._register
        self.cr_radiation = qc.clbits[0]._register
        elapsed_ms = (time.perf_counter() - start_time) * 1000
        if hasattr(self, "metrics"):
            self.metrics.add_entry("build_circuit_ms", elapsed_ms)
            self.metrics.add_entry("build_circuit_count", 1)
        if self.statsd:
            self.statsd.timing("universe.build_circuit_ms", elapsed_ms, tags=tags)
            self.statsd.increment("universe.build_circuit_invocations", tags=tags)
        logger.info(
            "TraceID: %s - build_circuit concluído em %.2f ms", trace_id, elapsed_ms
        )

        if use_cache and initial_encoded_circuit is None and cache_key is not None:
            if cache_key in self._circuit_cache:
                self._circuit_cache.move_to_end(cache_key)
            elif len(self._circuit_cache) >= self._circuit_cache_maxsize:
                oldest_key = next(iter(self._circuit_cache))
                del self._circuit_cache[oldest_key]
            self._circuit_cache[cache_key] = qc

        return qc

    # --- Execução no simulador ---

    def run(
        self,
        steps: int,
        shots: Optional[int] = None,
        *,
        apply_scrambling: bool = True,
        scramble_depth: Optional[int] = None,
        symbol: Optional[str] = None,
        timeframe: Optional[str] = None,
        defer_thermal_noise: bool = False,
        gate_budget_per_qubit: Optional[int] = None,
        post_randomize_layers: int = 1,
        max_execution_seconds: Optional[float] = None,
        trace_id: Optional[str] = None,
        **build_kwargs,
    ):
        """
        Executa a simulação do universo quântico por um número de passos.

        Args
        ----
        steps : int
            Número de passos de evolução do universo.
        shots : int, optional
            Número de shots para a execução do backend. Se ``None``, usa ``self.shots``.
        apply_scrambling : bool, optional
            Se ``True``, adiciona camadas de scrambling ao circuito.
        scramble_depth : int, optional
            Profundidade de scrambling a ser usada. ``None`` utiliza ``self.scr_depth``.
        symbol : str, optional
            Símbolo do ativo associado ao circuito.
        timeframe : str, optional
            Timeframe do ativo associado ao circuito.
        defer_thermal_noise : bool, optional
            Se ``True``, constrói o circuito sem aplicar ruído térmico e insere
            o canal de decaimento somente após a etapa de ``enforce_circuit_limits``.
        gate_budget_per_qubit : int, optional
            Limite máximo de portas por qubit aplicado durante ``enforce_circuit_limits``.
        post_randomize_layers : int, optional
            Número de camadas de randomização a serem inseridas após o aparo do
            circuito em ``enforce_circuit_limits``.
        max_execution_seconds : float, optional
            Tempo máximo permitido para a execução do backend. Se excedido,
            a simulação é cancelada e um erro é registrado.
        **build_kwargs : Any
            Argumentos adicionais encaminhados para :meth:`build_circuit`.

        Returns:
            Um dicionário contendo o resultado da simulação e métricas.

        Notes
        -----
        Caso a remoção das medições finais para obtenção do ``Statevector``
        falhe, a execução continua e o problema é registrado em nível ``WARNING``.
        """
        trace_id = trace_id or uuid.uuid4().hex
        tags = [f"trace_id:{trace_id}"] if self.statsd else None
        logger.debug(
            f"Iniciando execução do universo por {steps} passos. TraceID: {trace_id}"
        )
        start_time = time.perf_counter()
        if trace:
            tracer = trace.get_tracer(__name__)
            span_cm = tracer.start_as_current_span(
                "universe.run",
                attributes={"trace_id": trace_id},
            )
        else:
            from contextlib import nullcontext

            span_cm = nullcontext()

        with span_cm:
            current_run_shots = shots if shots is not None else self.shots

            if "thermal" not in build_kwargs:
                build_kwargs["thermal"] = self.thermal
            thermal_requested = build_kwargs["thermal"]
            if "temperature" not in build_kwargs:
                build_kwargs["temperature"] = self.thermal_coefficient * 0.5
            if "measure_frequency" not in build_kwargs:
                build_kwargs["measure_frequency"] = self.measure_frequency

            if "scr_depth_override" not in build_kwargs and scramble_depth is not None:
                build_kwargs["scr_depth_override"] = scramble_depth
            if "apply_scrambling" not in build_kwargs:
                build_kwargs["apply_scrambling"] = apply_scrambling

            if defer_thermal_noise:
                build_kwargs["thermal"] = False

            qc = self.build_circuit(steps, **build_kwargs)

            prev_metrics = getattr(self, "_last_metrics", {})
            self.logger.debug(
                "Calling enforce_circuit_limits with depth=%s ops=%s",
                self.max_circuit_depth,
                self.max_circuit_operations,
            )
            try:
                qc = enforce_circuit_limits(
                    qc,
                    max_depth=self.max_circuit_depth,
                    max_ops=self.max_circuit_operations,
                    backend_name=self.backend_name,
                    entropy=prev_metrics.get("sv_entropy"),
                    counts_diversity_ratio=prev_metrics.get("counts_diversity_ratio"),
                    gate_budget_per_qubit=gate_budget_per_qubit,
                    post_randomize_layers=post_randomize_layers,
                )
            except TypeError:
                qc = enforce_circuit_limits(qc)

            if defer_thermal_noise and thermal_requested:
                apply_hawking_noise_post_trim(
                    qc,
                    self.qr_bh[-1].index,
                    self.qr_radiation[0].index,
                    build_kwargs["temperature"],
                )

            self.qc = qc

            noise_model_to_use = None
            if self.thermal_noise_enabled:
                try:
                    errors = []
                    from qiskit_aer.noise import depolarizing_error, NoiseModel  # type: ignore

                    for i in range(self.n_qubits):
                        error_param = max(
                            0.05, np.clip(self.thermal_noise_temperature * 0.05, 0, 0.1)
                        )
                        if error_param > 1e-9:
                            errors.append((depolarizing_error(error_param, 1), [i]))
                    if errors:
                        noise_model_to_use = NoiseModel()
                        for error, qubits_list in errors:
                            noise_model_to_use.add_quantum_error(
                                error, "measure", qubits_list
                            )
                        logger.info(
                            f"Modelo de ruído (depolarizing) construído e será usado com parâmetro ~{error_param:.4f}."
                        )
                    else:
                        logger.info(
                            "Modelo de ruído habilitado, mas parâmetro de erro muito baixo. Nenhum erro adicionado."
                        )
                except ImportError:
                    logger.warning(
                        "Qiskit Aer noise components não puderam ser importados. Modelo de ruído não será aplicado."
                    )
                except Exception as e_noise:
                    logger.error(
                        f"Erro ao construir modelo de ruído: {e_noise}. Modelo de ruído não será aplicado."
                    )

            start_time = time.perf_counter()
            try:
                # Isso garante que o statevector possa ser recuperado.
                # Usar um label para evitar conflitos e para recuperação explícita.
                final_sv_label = "final_run_state"
                if "statevector" in self.backend_name.lower():
                    qc_to_run = (
                        self.qc.copy()
                    )  # Trabalhar com uma cópia para adicionar save_statevector
                    # Remover medições finais se quisermos o statevector *antes* delas
                    # No entanto, se o objetivo é o estado final *após* a evolução (incluindo medições parciais),
                    # então não remover. Se o backend for statevector, ele geralmente ignora medições para get_statevector().
                    # Para consistência com o log "No statevector for experiment 'None'",
                    # vamos garantir que save_statevector é chamado no circuito que vai para backend.run.

                    # Se houver medições e o backend for statevector, ele pode não dar erro,
                    # mas o statevector será o estado *antes* da medição final global,
                    # ou pode depender de como o backend trata isso.
                    # A instrução save_statevector salva o estado em um ponto específico.
                    qc_to_run.save_statevector(label=final_sv_label)
                    logger.debug(
                        f"Instrução save_statevector(label='{final_sv_label}') adicionada a qc_to_run."
                    )

                    # logger.debug(f"Instruções em qc_to_run após adicionar save_statevector: {qc_to_run.data}")
                else:
                    # Se não for um backend statevector, não faz sentido adicionar save_statevector
                    # pois get_statevector() não funcionará de qualquer maneira.
                    # O statevector não será obtido diretamente do resultado neste caso.
                    qc_to_run = self.qc
                    logger.debug(
                        "Backend não é statevector, save_statevector não foi adicionado."
                    )

                # Atribuir o circuito executado para referência futura (statevector retrieval)
                self._last_built_circuit = qc_to_run

                from qiskit import transpile

                # Um conjunto comum de portas base para Aer (incluindo as necessárias para save_statevector, etc.)
                # Estes são geralmente seguros e amplamente suportados pelos simuladores Aer.
                aer_basis_gates = [
                    "u1",
                    "u2",
                    "u3",
                    "rz",
                    "sx",
                    "x",
                    "cx",
                    "id",
                    "unitary",
                    "measure",
                    "reset",
                    "barrier",
                    "snapshot",
                    "save_state",
                    "save_statevector",
                    "save_density_matrix",
                    "save_probabilities",
                    "save_probabilities_dict",
                    "save_amplitudes",
                    "save_amplitudes_squared",
                    "save_expval",
                    "save_expval_var",
                    "set_statevector",
                    "set_density_matrix",
                ]  # Adicionado 'unitary' para maior generalidade, embora deva ser decomposto.

                logger.info(
                    f"Transpilando circuito (depth: {qc_to_run.depth()}, ops: {qc_to_run.count_ops()}) para o backend {self.backend_name}..."
                )

                # Decomposição prévia pode ajudar a transpilação com instruções muito customizadas.
                # O método .decompose() do QuantumCircuit é recursivo por padrão.
                qc_decomposed_for_transpile = qc_to_run.decompose()
                logger.debug(
                    f"Circuito após decomposição inicial (depth: {qc_decomposed_for_transpile.depth()}, ops: {qc_decomposed_for_transpile.count_ops()})."
                )

                # Nível de otimização 1 é um bom compromisso para simuladores.
                # Foca em reescrever para as basis_gates e alguma otimização leve.
                cache_key_symbol_tf = None
                if symbol and timeframe:
                    cache_key_symbol_tf = (symbol, timeframe)
                    if cache_key_symbol_tf in self._transpiled_symbol_tf_cache:
                        logger.debug(
                            "Usando circuito transpilado em cache para %s %s",
                            symbol,
                            timeframe,
                        )
                        transpiled_qc = self._transpiled_symbol_tf_cache[
                            cache_key_symbol_tf
                        ]
                    else:
                        try:
                            transpiled_qc = transpile(
                                qc_decomposed_for_transpile,
                                backend=self.backend,
                                optimization_level=1,
                            )
                            self._transpiled_symbol_tf_cache[cache_key_symbol_tf] = (
                                transpiled_qc
                            )
                            logger.info(
                                "Circuito transpilado com sucesso (depth: %s, ops: %s).",
                                transpiled_qc.depth(),
                                transpiled_qc.count_ops(),
                            )
                        except Exception as e_transpile:
                            logger.error(
                                "Erro durante a transpilação explícita: %s. Tentando executar o circuito original (decomposto).",
                                e_transpile,
                            )
                            transpiled_qc = qc_decomposed_for_transpile
                else:
                    try:
                        transpiled_qc = transpile(
                            qc_decomposed_for_transpile,
                            backend=self.backend,
                            optimization_level=1,
                        )
                        logger.info(
                            "Circuito transpilado com sucesso (depth: %s, ops: %s).",
                            transpiled_qc.depth(),
                            transpiled_qc.count_ops(),
                        )
                    except Exception as e_transpile:
                        logger.error(
                            "Erro durante a transpilação explícita: %s. Tentando executar o circuito original (decomposto).",
                            e_transpile,
                        )
                        transpiled_qc = qc_decomposed_for_transpile

                job = self.backend.run(
                    transpiled_qc,  # Usar o circuito transpilado
                    shots=current_run_shots,
                    memory=True,
                    noise_model=noise_model_to_use,
                )
                try:
                    if max_execution_seconds is not None:
                        result = job.result(timeout=max_execution_seconds)
                    else:
                        result = job.result()
                except JobTimeoutError as exc:
                    self._last_execution_time = time.perf_counter() - start_time
                    logger.error(
                        "Execução do backend excedeu limite de %.1fs: %s",
                        max_execution_seconds,
                        exc,
                    )
                    self._last_counts = {"error": "timeout"}
                    self._last_metrics = {
                        "error": "timeout",
                        "trace_id": trace_id,
                    }
                    if hasattr(self, "metrics"):
                        self.metrics.add_entry(
                            "run_ms", self._last_execution_time * 1000
                        )
                        self.metrics.add_entry("run_count", 1)
                    if self.statsd:
                        self.statsd.timing(
                            "universe.run_ms",
                            self._last_execution_time * 1000,
                            tags=tags,
                        )
                        self.statsd.increment("universe.run_invocations", tags=tags)
                    return self._last_counts, self._last_metrics
                execution_time = time.perf_counter() - start_time
                execution_time = time.perf_counter() - start_time
                logger.debug(
                    f"Circuito executado em {execution_time:.4f} segundos com {current_run_shots} shots."
                )

                logger.info(
                    f"QQU: Objeto Result (resumo): success={result.success}, status={result.status}"
                )
                if hasattr(result, "results") and result.results:
                    logger.info(
                        f"QQU: Número de ExperimentResults em result.results: {len(result.results)}"
                    )
                    for exp_res_idx, exp_res in enumerate(result.results):
                        exp_name_from_header = (
                            exp_res.header.name
                            if hasattr(exp_res.header, "name")
                            else f"exp_index_{exp_res_idx}"
                        )
                        metadata_from_header = (
                            exp_res.header.metadata
                            if hasattr(exp_res.header, "metadata")
                            else {}
                        )
                        logger.info(
                            f"  QQU: ExpResult {exp_res_idx}: name='{exp_name_from_header}', metadata_keys='{list(metadata_from_header.keys()) if metadata_from_header else []}', success={exp_res.success}, status={exp_res.status}"
                        )

                        # Verificar dados de snapshots no ExperimentResultData
                        if hasattr(exp_res, "data"):
                            actual_exp_data = exp_res.data
                            exp_data_dict = {}
                            if isinstance(actual_exp_data, object) and callable(
                                getattr(actual_exp_data, "to_dict", None)
                            ):
                                exp_data_dict = actual_exp_data.to_dict()
                            elif isinstance(actual_exp_data, dict):
                                exp_data_dict = actual_exp_data

                            if exp_data_dict:  # Checa se exp_data_dict foi populado
                                if "snapshots" in exp_data_dict:
                                    snapshots_data = exp_data_dict["snapshots"]
                                    logger.info(
                                        f"    QQU: Snapshots em ExpResult.data para '{exp_name_from_header}': {list(snapshots_data.keys())}"
                                    )
                                    if "statevector" in snapshots_data:
                                        sv_snapshots = snapshots_data["statevector"]
                                        logger.info(
                                            f"      QQU: Tipo 'statevector' nos snapshots. Labels: {list(sv_snapshots.keys())}"
                                        )
                                        if final_sv_label in sv_snapshots:
                                            # logger.info(f"      >>> QQU: Label '{final_sv_label}' ENCONTRADO nos snapshots de '{exp_name_from_header}'! Dados: {sv_snapshots[final_sv_label]}") # Pode ser muito verboso
                                            logger.info(
                                                f"      >>> QQU: Label '{final_sv_label}' ENCONTRADO nos snapshots de '{exp_name_from_header}'!"
                                            )
                                        else:
                                            logger.warning(
                                                f"      >>> QQU: Label '{final_sv_label}' NÃO encontrado nos snapshots de statevector para '{exp_name_from_header}'."
                                            )
                                    else:
                                        logger.info(
                                            "      QQU: Tipo 'statevector' NÃO encontrado nos snapshots."
                                        )
                                else:
                                    if final_sv_label in exp_data_dict:
                                        logger.info(
                                            f"    QQU: Chave 'snapshots' não encontrada em ExpResult.data para '{exp_name_from_header}', mas '{final_sv_label}' está disponível."
                                        )
                                    else:
                                        logger.info(
                                            f"    QQU: Chave 'snapshots' não encontrada em ExpResult.data para '{exp_name_from_header}'. Chaves presentes: {list(exp_data_dict.keys())}"
                                        )
                            else:
                                logger.info(
                                    f"    QQU: exp_data_dict não pôde ser formado a partir de exp_res.data (tipo: {type(actual_exp_data)}) para '{exp_name_from_header}'."
                                )
                        else:
                            logger.info(
                                f"  QQU: ExpResult {exp_res_idx} ('{exp_name_from_header}') não possui atributo 'data'."
                            )

                self.last_result = result  # Adicionado para corrigir AttributeError
                self._last_result = result
                self.last_run_result = (
                    result  # Atribuir também ao last_run_result para compatibilidade
                )

                self._last_counts = (
                    result.get_counts(qc_to_run)
                    if hasattr(result, "get_counts")
                    else {}
                )
                self._last_execution_time = execution_time
                self._last_run_steps = (
                    steps  # Armazenar o número de passos desta execução
                )

                # Obter statevector de forma simplificada
                final_statevector = None
                if "statevector" in self.backend_name.lower():
                    final_statevector = self._extract_statevector(
                        result, final_sv_label
                    )
                    if final_statevector is None:
                        logger.error("QQU: Não foi possível obter o statevector final.")
                else:
                    logger.info(
                        f"QQU: Backend {self.backend_name} não é para statevector. Statevector não será buscado."
                    )

                if final_statevector is not None:
                    self.last_statevector = final_statevector
                    self.last_sv = final_statevector
                    self.current_sv = final_statevector

                m: Dict[str, float] = {}
                if final_statevector is not None:
                    try:
                        from ..utils.quantum_utils import measure_entropy
                    except Exception:

                        def measure_entropy(sv: Statevector) -> Dict[str, float]:
                            return {
                                "sv_entropy": sv_entropy(sv),
                                "linear_entropy": linear_entropy(sv),
                            }

                    m = measure_entropy(final_statevector)
                    logger.debug(
                        "entropy: %.3f, lin_entropy: %.3f",
                        m["sv_entropy"],
                        m["linear_entropy"],
                    )

                # Calcular métricas pós-execução (exemplo)
                final_metrics = self._calculate_post_run_metrics(
                    final_statevector, self._last_counts
                )
                final_metrics.update(m)
                self._check_over_trimming(m, final_metrics)

                self.metrics.add_entry(
                    "loschmidt_echo", final_metrics.get("fidelity_to_initial", 0.0)
                )
                self.metrics.add_entry(
                    "otoc", final_metrics.get("otoc_calculated", 0.0)
                )
                self.metrics.add_entry("sv_entropy", final_metrics.get("sv_entropy"))
                self.metrics.add_entry(
                    "linear_entropy", final_metrics.get("linear_entropy")
                )

                unique_outcomes = final_metrics.get("counts_unique_outcomes", 0)
                diversity_ratio = final_metrics.get("counts_diversity_ratio", 0.0)
                logger.info(
                    "Counts diversity: %d unique outcomes (%.4f of 2^%d). Circuit depth=%d, ops=%d",
                    unique_outcomes,
                    diversity_ratio,
                    final_metrics.get("num_measured_qubits", self.n_qubits),
                    self.qc.depth() if getattr(self, "qc", None) else -1,
                    self.qc.size() if getattr(self, "qc", None) else -1,
                )

                alpha = 0.3
                if self.diversity_ratio_ema is None:
                    self.diversity_ratio_ema = diversity_ratio
                else:
                    self.diversity_ratio_ema = (
                        alpha * diversity_ratio + (1 - alpha) * self.diversity_ratio_ema
                    )
                if self.diversity_ratio_ema < self.min_counts_diversity_ratio:
                    self._diversity_below_count += 1
                else:
                    self._diversity_below_count = 0

                if self._diversity_below_count >= 3:
                    logger.warning(
                        "Counts diversity ratio %.4f below threshold %.2f",
                        diversity_ratio,
                        self.min_counts_diversity_ratio,
                    )

                final_metrics["trace_id"] = trace_id
                elapsed_ms = (time.perf_counter() - start_time) * 1000
                if hasattr(self, "metrics"):
                    self.metrics.add_entry("run_ms", elapsed_ms)
                    self.metrics.add_entry("run_count", 1)
                if self.statsd:
                    self.statsd.timing("universe.run_ms", elapsed_ms, tags=tags)
                    self.statsd.increment("universe.run_invocations", tags=tags)
                logger.info(
                    "TraceID: %s - Execução do universo concluída em %.2f ms. Counts: %s...",
                    trace_id,
                    elapsed_ms,
                    str(self._last_counts)[:100],
                )
                self._last_metrics = final_metrics
                return self._last_counts, final_metrics

            except Exception as e:
                logger.error(
                    f"Erro CRÍTICO durante a execução do circuito Qiskit ou processamento de resultados: {e}",
                    exc_info=True,
                )
                self._last_result = None
                self._last_counts = {"error": f"Qiskit execution failed: {e}"}
                self._last_execution_time = time.perf_counter() - start_time
                # Retornar uma estrutura consistente em caso de erro
                error_metrics = {
                    "error": str(e),
                    "execution_time": self._last_execution_time,
                }
                if hasattr(
                    e, "message"
                ):  # Alguns erros do Qiskit têm uma mensagem útil
                    error_metrics["qiskit_error_message"] = e.message
                error_metrics["trace_id"] = trace_id
                if hasattr(self, "metrics"):
                    self.metrics.add_entry("run_ms", self._last_execution_time * 1000)
                    self.metrics.add_entry("run_count", 1)
                if self.statsd:
                    self.statsd.timing(
                        "universe.run_ms", self._last_execution_time * 1000, tags=tags
                    )
                    self.statsd.increment("universe.run_invocations", tags=tags)
                self._last_metrics = error_metrics
                return self._last_counts, error_metrics

    def _update_circuit_depth_ema(self, current_depth: Optional[int]) -> None:
        """Update exponential moving average of circuit depth."""

        if current_depth is None:
            return
        alpha = 0.3
        if self.circuit_depth_ema is None:
            self.circuit_depth_ema = float(current_depth)
        else:
            self.circuit_depth_ema = (
                alpha * float(current_depth) + (1 - alpha) * self.circuit_depth_ema
            )

    def _check_over_trimming(
        self, entropy_metrics: Dict[str, float], metrics: Dict[str, Any]
    ) -> None:
        """Emit warning when entropy, diversity and depth indicate over-trimming."""

        sv_ent = entropy_metrics.get("sv_entropy")
        diversity = metrics.get("counts_diversity_ratio", 1)
        depth = metrics.get("circuit_depth")
        self._update_circuit_depth_ema(depth)
        if (
            sv_ent is not None
            and sv_ent < 0.2
            and diversity < 0.25
            and (self.circuit_depth_ema or 0.0) < MIN_AVG_CIRCUIT_DEPTH
        ):
            logger.warning("possible over-trimming")

    def _calculate_post_run_metrics(
        self, statevector: Optional[Statevector], counts: Optional[Dict[str, int]]
    ):
        """
        Calcula métricas pós-execução, incluindo fidelidade ao estado inicial e OTOC.
        """
        logger.debug(
            f"_calculate_post_run_metrics chamada com statevector: {'present' if statevector is not None else 'None'}, counts: {'present' if counts is not None else 'None'}"
        )

        metrics = {
            "fidelity_to_initial": None,  # Substituindo fidelity_echo_placeholder
            "otoc_calculated": None,  # Substituindo otoc_placeholder
            "execution_time": (
                self._last_execution_time
                if hasattr(self, "_last_execution_time")
                else 0.0
            ),
            "final_entropy_from_counts": None,  # Adicional: entropia de Shannon das contagens finais
            "sv_entropy": None,
            "linear_entropy": None,
        }

        # Calcular Fidelidade ao Estado Inicial (Loschmidt Echo simplificado)
        if (
            statevector is not None
            and hasattr(self, "current_sv")
            and self.current_sv is not None
        ):
            try:
                # Garantir que ambos são objetos Statevector do Qiskit
                sv_final = (
                    statevector
                    if isinstance(statevector, Statevector)
                    else Statevector(statevector)
                )
                sv_initial = (
                    self.current_sv
                    if isinstance(self.current_sv, Statevector)
                    else Statevector(self.current_sv)
                )

                if sv_final.dim == sv_initial.dim:  # type: ignore
                    # fidelity = qi.state_fidelity(sv_initial, sv_final) # qiskit.quantum_info.state_fidelity
                    # state_fidelity calcula |<psi|phi>|^2 para estados puros.
                    # Para vetores de estado 'a' e 'b', a fidelidade é abs(a.conj().dot(b))**2
                    dot_product = (
                        np.abs(np.dot(sv_initial.data.conj(), sv_final.data)) ** 2
                    )
                    metrics["fidelity_to_initial"] = float(dot_product)
                    logger.debug(
                        f"Fidelidade ao estado inicial calculada: {metrics['fidelity_to_initial']:.4f}"
                    )
                else:
                    logger.warning(
                        f"Dimensões do statevector inicial ({sv_initial.dim}) e final ({sv_final.dim}) não coincidem. Fidelidade não calculada."
                    )
            except Exception as e:
                logger.error(
                    f"Erro ao calcular a fidelidade ao estado inicial: {e}",
                    exc_info=True,
                )
        else:
            logger.debug(
                "Statevector final ou inicial não disponível. Fidelidade não calculada."
            )

        # Calcular OTOC de acordo com a frequência definida
        if statevector is not None and self.run_count % self.otoc_frequency == 0:
            try:
                # Usar o número de passos da última execução, ou um padrão.
                time_step_for_otoc = (
                    self._last_run_steps
                    if hasattr(self, "_last_run_steps") and self._last_run_steps > 0
                    else 1
                )
                num_qubits_for_otoc = (
                    statevector.num_qubits
                    if hasattr(statevector, "num_qubits")
                    else self.n_qubits
                )
                # target_qubits_for_otoc = [0, num_qubits_for_otoc // 2] if num_qubits_for_otoc > 1 else [0]
                target_qubits_for_otoc = [0]  # Simples, para começar

                if num_qubits_for_otoc > 0:  # Só calcular OTOC se houver qubits
                    otoc_val = self.calculate_otoc(
                        statevector=statevector,
                        time_step=time_step_for_otoc,
                        n_qubits=num_qubits_for_otoc,
                        target_qubits=target_qubits_for_otoc,
                    )
                    metrics["otoc_calculated"] = otoc_val
                    logger.info("OTOC calculado=%.4f", otoc_val)
                else:
                    logger.debug("OTOC não calculado: n_qubits é 0.")
            except Exception as e:
                logger.error(f"Erro ao calcular OTOC: {e}", exc_info=True)
        elif statevector is None:
            logger.debug("Statevector final não disponível. OTOC não calculado.")
        else:
            logger.debug(
                "OTOC não calculado neste ciclo (frequência %d)", self.otoc_frequency
            )
            self._check_otoc_constancy(None)

        # Entropias baseadas no statevector
        if statevector is not None:
            try:
                metrics["sv_entropy"] = sv_entropy(statevector)
                metrics["linear_entropy"] = linear_entropy(statevector)
                logger.debug(
                    "Entropias do statevector calculadas: sv_entropy=%.4f, linear_entropy=%.4f",
                    metrics["sv_entropy"],
                    metrics["linear_entropy"],
                )
            except Exception as exc:
                logger.error("Erro ao calcular entropias do statevector: %s", exc)

        # Calcular Entropia de Shannon das contagens finais
        if counts:
            try:
                total_shots = sum(counts.values())
                if total_shots > 0:
                    probabilities = np.array(list(counts.values())) / total_shots
                    # Filtrar probabilidades zero para evitar log(0)
                    probabilities = probabilities[probabilities > 0]
                    shannon_entropy = -np.sum(probabilities * np.log2(probabilities))
                    metrics["final_entropy_from_counts"] = float(
                        max(shannon_entropy, 0.0)
                    )
                    logger.debug(
                        f"Entropia de Shannon das contagens finais: {metrics['final_entropy_from_counts']:.4f}"
                    )
            except Exception as e:
                logger.error(
                    f"Erro ao calcular a entropia de Shannon das contagens: {e}",
                    exc_info=True,
                )

        unique_outcomes = len(counts) if counts else 0
        metrics["counts_unique_outcomes"] = unique_outcomes
        if counts:
            measured_bits = max(len(bitstr.replace(" ", "")) for bitstr in counts)
            total_shots = sum(counts.values())
        else:
            measured_bits = self.n_qubits
            total_shots = 0
        metrics["num_measured_qubits"] = measured_bits
        metrics["counts_diversity"] = (
            unique_outcomes / float(total_shots) if total_shots > 0 else 0.0
        )
        metrics["counts_diversity_ratio"] = (
            unique_outcomes / float(2**measured_bits) if counts else 0.0
        )
        logger.info(
            "Diversidade do circuito: %.4f (ratio %.4f)",
            metrics["counts_diversity"],
            metrics["counts_diversity_ratio"],
        )
        if getattr(self, "qc", None):
            metrics["circuit_depth"] = self.qc.depth()
            metrics["circuit_size"] = self.qc.size()
            if unique_outcomes <= 1 and self.qc.depth() <= 1:
                logger.warning(
                    "Circuito possivelmente trivial detectado: profundidade <= 1 e apenas 1 outcome."
                )
        else:
            metrics["circuit_depth"] = None
            metrics["circuit_size"] = None

        # Adicionar outras métricas que possam ser relevantes e calculáveis aqui

        return metrics

    def get_current_statevector(self):
        """Retorna o statevector atual da simulação."""
        if self.current_sv is None:
            logger.warning(
                "Aviso: current_sv não definido. Simulação pode não ter sido executada ou o estado não foi capturado."
            )
        return self.current_sv

    def encode_market_data(self, market_features: Dict[str, Any]) -> QuantumCircuit:
        """Encode market data using EQCI when available."""

        if hasattr(self, "eqci") and hasattr(self.eqci, "encode_market_data"):
            try:
                qc = self.eqci.encode_market_data(
                    market_features, max_qubits=self.n_qubits
                )
                self.qc = qc
                return qc
            except Exception as exc:  # pragma: no cover - best effort
                logger.error("encode_market_data failed: %s", exc)

        qc = QuantumCircuit(self.n_qubits, name="Q_Market_Placeholder")
        qc.h(range(self.n_qubits))
        self.qc = qc
        return qc

    def reconfigure(self, **kwargs: Any) -> None:
        """Atualiza parâmetros do universo em tempo de execução.

        Parameters
        ----------
        **kwargs:
            Pares ``nome=valor`` a serem aplicados nos atributos existentes.

        Notes
        -----
        Se ``n_qubits`` for alterado, registradores e caches dependentes serão
        recriados para refletir o novo tamanho.
        """

        self.logger.info("Universo: Reconfigurando com %s", kwargs)

        n_qubits_changed = False
        old_n_qubits = getattr(self, "n_qubits", None)

        for key, value in kwargs.items():
            if hasattr(self, key):
                if key == "n_qubits" and value != getattr(self, "n_qubits"):
                    n_qubits_changed = True
                setattr(self, key, value)

        if n_qubits_changed:
            self.logger.info(
                "n_qubits alterado de %s para %s - recriando registradores e caches",
                old_n_qubits,
                self.n_qubits,
            )

            self.qr_bh = QuantumRegister(self.n_qubits, "bh")
            self.qr_radiation = QuantumRegister(1, "radiation")
            self.cr_radiation = ClassicalRegister(1, "cr_radiation")
            self.cr_main = ClassicalRegister(self.n_qubits, "cr_main")
            self.logger.debug(
                "Registradores recriados: qr_bh=%s, qr_radiation=%s, cr_radiation=%s, cr_main=%s",
                self.qr_bh,
                self.qr_radiation,
                self.cr_radiation,
                self.cr_main,
            )

            self.qc = QuantumCircuit(self.n_qubits, name="QUALIA_Core")
            self._transpiled_symbol_tf_cache.clear()
            self._clear_circuit_cache()
            self._last_built_circuit = None

            try:
                initial_state_obj = self._create_initial_state(self.initial_state_type)

                if self.current_sv is not None:
                    self.last_sv = shallow_copy_statevector(self.current_sv)
                elif isinstance(Statevector, type) and isinstance(
                    initial_state_obj, Statevector
                ):
                    self.current_sv = initial_state_obj
                    self.last_sv = shallow_copy_statevector(self.current_sv)
                elif isinstance(initial_state_obj, QuantumCircuit):
                    if self.current_sv is None:
                        self.logger.error(
                            "Objeto QuantumCircuit retornado por _create_initial_state, mas current_sv permaneceu indefinido."
                        )
                    else:
                        self.logger.info(
                            "Recebido QuantumCircuit de _create_initial_state. Convertendo para Statevector via simulação."
                        )
                        self.current_sv = (
                            self._create_statevector_from_circuit_via_simulation(
                                initial_state_obj
                            )
                        )
                        self.last_sv = shallow_copy_statevector(self.current_sv)
                elif hasattr(initial_state_obj, "data") and hasattr(
                    initial_state_obj, "num_qubits"
                ):
                    self.current_sv = initial_state_obj
                    self.last_sv = shallow_copy_statevector(self.current_sv)
                else:
                    self.logger.error(
                        "_create_initial_state retornou tipo inesperado '%s' ou None durante reconfigure",
                        type(initial_state_obj),
                    )
                    self.current_sv = None
            except Exception as exc:  # pragma: no cover - best effort
                self.logger.error(
                    "Erro crítico ao recriar estado inicial para %s qubits durante reconfigure: %s",
                    self.n_qubits,
                    exc,
                )

            if self.current_sv is None:
                self.logger.error(
                    "Erro crítico ao recriar estado inicial para %s qubits durante reconfigure. Falha crítica: current_sv ainda é None após recriação do estado inicial.",
                    self.n_qubits,
                )

            if old_n_qubits is not None:
                self.invalidate_qft_cache(old_n_qubits)

        if "eqci_config" in kwargs:
            try:
                self.eqci_config = kwargs["eqci_config"]
                self.eqci = EnhancedQuantumClassicalInterface(
                    critical_feature_window=self.eqci_config.get("critical_window", 20),
                    secondary_feature_window=self.eqci_config.get(
                        "secondary_window", 20
                    ),
                    volume_ratio_cap=self.eqci_config.get("vol_cap", 3.0),
                )
                self.logger.info(
                    "EQCI reconfigurada com nova config: %s", self.eqci_config
                )
            except Exception as exc:  # pragma: no cover - best effort
                self.logger.error("Falha ao reconfigurar EQCI: %s", exc, exc_info=True)
                self.eqci = None

        # Limpar métricas para um novo ciclo de coleta
        self.metrics = QUALIAMetrics(max_history_size=self.max_history_size)
        self.logger.info("Métricas resetadas devido à reconfiguração.")

    def encode_snapshot(self, snapshot: Dict[str, Any]) -> QuantumCircuit:
        """Encode market snapshot using :meth:`encode_market_data`."""

        encoded = self.encode_market_data(snapshot)
        if isinstance(encoded, QuantumCircuit):
            return encoded
        return self.qc

    def update(
        self,
        state: Union[QuantumCircuit, Statevector, np.ndarray, List[complex]],
        *,
        timestamp: Optional[float] = None,
        trace_id: Optional[str] = None,
    ) -> Dict[str, np.ndarray]:
        """Apply a circuit or state to the universe and update internal state."""

        if isinstance(state, QuantumCircuit):
            try:
                sv = Statevector.from_instruction(state)
            except Exception:
                sv = self._create_statevector_from_circuit_via_simulation(state)
        else:
            converted = convert_to_statevector(state)
            if converted is None:
                raise ValueError("Unable to convert state to Statevector")
            sv = converted

        self.current_sv = sv
        self.last_statevector = sv
        self.sv_history.append(sv)
        metrics = metrics_helpers.compute_quantum_metrics(self, sv)
        self.logger.info(
            "Update metrics: S=%.3f, C=%.3f, E=%.3f",
            metrics.get("entropy", 0.0),
            metrics.get("coherence", 0.0),
            metrics.get("entanglement", 0.0),
        )
        return {"vector": sv.data.copy(), "metrics": metrics}

    def compute_quantum_metrics(
        self, state: Union[Statevector, np.ndarray]
    ) -> Dict[str, float]:
        """Compute entropy and coherence and store in :attr:`metrics`."""

        from ..metrics.quantum_metrics import (
            entropy as q_entropy,
            l1_coherence,
        )

        sv = state if isinstance(state, Statevector) else Statevector(state)
        entropy_val = q_entropy(sv)
        if getattr(self, "observer", None) is not None:
            obs_state = getattr(self.observer, "current_state", None)
            if obs_state is not None and hasattr(obs_state, "coherence"):
                coherence_val = float(obs_state.coherence)
            else:
                coherence_val = l1_coherence(sv)
        else:
            coherence_val = l1_coherence(sv)

        if hasattr(self, "metrics") and hasattr(self.metrics, "add_entry"):
            self.metrics.add_entry("quantum_entropy", entropy_val)
            self.metrics.add_entry("quantum_coherence", coherence_val)

        return {"entropy": entropy_val, "coherence": coherence_val}

    def run_goal_oriented_entropic_time(
        self,
        shots=1024,
        target_state="101",
        max_steps=100,
        prob_threshold=0.5,
        initial_type="qft",
        retro_mode="none",
        thermal=True,
        temperature=0.05,
        measure_frequency=1,
    ):
        """
        Executa simulação adaptativa orientada por um estado - alvo, usando o tempo entrópico.
        """

        # from qiskit.quantum_info import Statevector, partial_trace, entropy

        backend_sv = safe_get_backend("aer_simulator_statevector", use_gpu=self.use_gpu)
        backend_counts = safe_get_backend("qasm_simulator", use_gpu=self.use_gpu)
        self.entropy_history = []
        self.metrics_history = {k: [] for k in self.metrics_history.keys()}
        self.current_sv = None

        self.initial_sv = None

        self._last_counts = {}
        # Histórico para compatibilidade
        metrics = {"page_entropy": []}
        for t in range(max_steps):
            qc_partial = self.build_circuit(
                t + 1,
                initial_type=initial_type,
                retro_mode=retro_mode,
                thermal=thermal,
                temperature=temperature,
                measure_frequency=measure_frequency,
            )
            qc_sv = qc_partial.copy()
            qc_sv.remove_final_measurements(inplace=True)
            result_sv = backend_sv.run(qc_sv).result()
            sv = result_sv.get_statevector()
            keep = np.arange(self.n_qubits, self.n_qubits + t + 1)
            trace_out = np.setdiff1d(
                np.arange(self.n_qubits + t + 1), keep, assume_unique=True
            )
            try:
                rho_rad = partial_trace(sv, trace_out)
                S = entropy(rho_rad, base=2)
            except Exception:
                S = None
            self.entropy_history.append(S)
            # Obtém contagens intermediárias
            qc_meas = qc_partial.copy()
            result_counts = backend_counts.run(qc_meas, shots=shots).result()
            counts = result_counts.get_counts()
            total_counts = sum(counts.values()) if counts else 0
            prob_target = (
                counts.get(target_state, 0) / total_counts if total_counts else 0.0
            )
            logger.info(
                "Passo %s, Prob(%s)=%.3f, Entropia=%.3f",
                t + 1,
                target_state,
                prob_target,
                S,
            )
            if prob_target >= prob_threshold:
                logger.info(
                    "Estado-alvo %s alcançado com probabilidade suficiente após %s passos.",
                    target_state,
                    t + 1,
                )
                break
        self._last_counts = counts

        return counts, self.entropy_history

    def visualize_quantum_circuit(self, steps_to_visualize=None, output_type="mpl"):
        """
        Visualiza o circuito quântico usando Qiskit's plot_bloch_multivector ou draw().

        Args:
            steps_to_visualize (int): Número de passos a serem visualizados.
            output_type (str): Tipo de saída ('mpl' para matplotlib, 'text' para texto,
                              'latex' para código LaTeX, 'bloch' para visualização Bloch).

        Returns:
            matplotlib.figure.Figure or str: Representação visual do circuito.
        """
        from qiskit import QuantumCircuit
        from qiskit.quantum_info import Statevector
        from qiskit.visualization import plot_bloch_multivector

        # Construir o circuito se ainda não estiver disponível
        if steps_to_visualize is None:
            if hasattr(self, "_last_run_steps") and self._last_run_steps is not None:
                steps_to_visualize = self._last_run_steps
            else:
                self.logger.warning(
                    "Número de steps para visualizar não especificado e não há histórico. Visualizando circuito base."
                )
                steps_to_visualize = 5

        # Preparar parâmetros para construção do circuito
        build_kwargs = {
            "thermal": (
                self.thermal_active_on_last_run
                if hasattr(self, "thermal_active_on_last_run")
                else False
            ),
            "temperature": (
                self.temperature_on_last_run
                if hasattr(self, "temperature_on_last_run")
                else 0.01
            ),
            "retro_mode": (
                self.retro_mode_on_last_run
                if hasattr(self, "retro_mode_on_last_run")
                else "none"
            ),
            "measure_frequency": (
                self.measure_frequency_on_last_run
                if hasattr(self, "measure_frequency_on_last_run")
                else 1
            ),
            "retro_strength": (
                self.retro_strength_on_last_run
                if hasattr(self, "retro_strength_on_last_run")
                else getattr(self, "retro_strength", 0.0)
            ),
        }

        # Remover parâmetros não suportados
        if "scr_feedback_factor" in build_kwargs:
            del build_kwargs["scr_feedback_factor"]

        build_kwargs = {k: v for k, v in build_kwargs.items() if v is not None}

        # Construir o circuito
        try:
            self._built_circuit = self.build_circuit(
                steps=steps_to_visualize, **build_kwargs
            )
        except Exception as e:
            self.logger.error(f"Erro ao construir circuito para visualização: {e}")
            return None

        # Visualizar estados quânticos (ex: Bloch spheres)
        if output_type == "bloch":
            try:
                state = Statevector.from_instruction(self._built_circuit)
                return plot_bloch_multivector(state.data)
            except Exception as e:
                self.logger.error(f"Erro ao criar visualização Bloch: {e}")
                return None

        # Visualizar estrutura do circuito
        try:
            return self._built_circuit.draw(output=output_type, fold=50)
        except Exception as e:
            self.logger.error(f"Erro ao criar visualização do circuito: {e}")
            return None

    def validate_qasm_compatibility(self):
        """
        Verifica se o circuito quântico contém operações não suportadas por hardware real.

        Returns:
            bool: True se o circuito for compatível, False caso contrário.
            dict: Detalhes sobre incompatibilidades encontradas.
        """
        if not hasattr(self, "_built_circuit") or self._built_circuit is None:
            self.logger.warning(
                "Circuito não construído. Construa o circuito antes de validar."
            )
            return False, {"error": "Circuito não construído"}

        # Operações que podem causar problemas em hardware real
        unsupported_gates = ["reset"]  # Alguns hardwares não suportam reset
        # Outras operações com suporte limitado
        limited_support = ["barrier", "delay"]

        # Verificar operações no circuito
        issues = {}
        circuit_ops = self._built_circuit.count_ops()

        # Verificar gates não suportados
        for gate in unsupported_gates:
            if gate in circuit_ops:
                issues[gate] = circuit_ops[gate]
                self.logger.warning(
                    f"Operação não suportada detectada: {gate} (quantidade: {circuit_ops[gate]})"
                )

        # Verificar gates com suporte limitado
        for gate in limited_support:
            if gate in circuit_ops:
                if "limited_support" not in issues:
                    issues["limited_support"] = {}
                issues["limited_support"][gate] = circuit_ops[gate]
                self.logger.info(
                    f"Operação com suporte limitado: {gate} (quantidade: {circuit_ops[gate]})"
                )

        # Verificar profundidade do circuito
        depth = self._built_circuit.depth()
        if depth > 100:  # Um limite arbitrário para hardware NISQ atual
            issues["depth"] = depth
            self.logger.warning(
                f"Circuito muito profundo ({depth} camadas) para hardware NISQ atual"
            )

        # Verificar número total de operações
        total_ops = sum(circuit_ops.values())
        if total_ops > 500:  # Outro limite arbitrário
            issues["total_operations"] = total_ops
            self.logger.warning(
                f"Circuito muito complexo ({total_ops} operações) para hardware NISQ atual"
            )

        return len(issues) == 0, issues

    def export_qasm3(self, filename="qualia_circuit.qasm3", steps_to_export=None):
        """Wrapper around :func:`io_export_qasm3` with verbose QASM logging."""
        success = io_export_qasm3(
            self, filename=filename, steps_to_export=steps_to_export
        )
        if (
            success
            and self.logger.isEnabledFor(logging.DEBUG)
            and hasattr(self, "_built_circuit")
        ):
            try:
                if qasm3 is not None:
                    self.logger.debug(
                        "QASM dump for %s:\n%s",
                        filename,
                        qasm3.dumps(self._built_circuit),
                    )
            except Exception as exc:  # pragma: no cover - logging only
                self.logger.debug("Failed to dump QASM: %s", exc)
        return success

    @numpy_cache(maxsize=32)
    def calculate_otoc(
        self, statevector, time_step, n_qubits, target_qubits=None
    ) -> Optional[float]:
        """
        Calcula o Out-of-Time-Order Correlator (OTOC) para medir a não-localidade temporal
        de correlações quânticas com sensibilidade adaptativa refinada.

        O OTOC está definido como: ⟨[W(t), V(0)]† [W(t), V(0)]⟩,
        onde W e V são operadores não comutativos, e W(t) é a evolução de W até o passo t.

        REFINAMENTO YAA: Sensibilidade adaptativa multi-modal baseada no contexto de mercado.
        """
        if target_qubits is None:
            target_qubits = [0]

        if self.run_count % self.otoc_frequency != 0:
            logger.debug("calculate_otoc pulado (frequência %d)", self.otoc_frequency)
            return self._last_otoc_value

        logger.info(
            "calculate_otoc called: time_step=%s, n_qubits=%s, target_qubits=%s, run_count=%s",
            time_step,
            n_qubits,
            target_qubits,
            self.run_count,
        )

        if getattr(self, "_otoc_stagnant", False):
            logger.info("OTOC stagnation – skipping heavy scrambling")
            self.scramble_random_on_next_build = True
            return self._last_otoc_value

        try:
            from qiskit.quantum_info import Pauli, Operator, Statevector
            import numpy as np

            # P-6 CORREÇÃO: Validação prévia de dimensões
            if isinstance(statevector, Statevector):
                sv_num_qubits = statevector.num_qubits

                if sv_num_qubits != n_qubits:
                    if sv_num_qubits > n_qubits:
                        qubits_to_trace = list(range(n_qubits, sv_num_qubits))
                        logger.debug(
                            f"P-6 OTOC: Dimensões inconsistentes detectadas. "
                            f"SV: {sv_num_qubits} qubits, Op: {n_qubits} qubits. "
                            f"Traçando qubits {qubits_to_trace}"
                        )
                        try:
                            from qiskit.quantum_info import partial_trace

                            statevector = partial_trace(statevector, qubits_to_trace)
                            logger.debug(
                                f"P-6 OTOC: Statevector ajustado para {statevector.num_qubits} qubits"
                            )
                        except Exception as trace_err:
                            logger.error(
                                f"P-6 OTOC: Falha no partial_trace: {trace_err}"
                            )
                            return 0.5
                    else:
                        logger.warning(
                            f"P-6 OTOC: Statevector ({sv_num_qubits}) tem menos qubits que operador ({n_qubits}). "
                            "Retornando fallback."
                        )
                        return 0.5

            # YAA REFINAMENTO: Sensibilidade adaptativa multi-modal
            # Determinar regime de mercado baseado em histórico de métricas
            market_regime = self._detect_market_regime()

            # Seleção de operadores baseada no regime e sensibilidade adaptativa
            if market_regime == "high_volatility":
                # Alta volatilidade: usar operadores com maior não-comutatividade
                operator_pairs = [
                    ("x", "y"),  # Máxima não-comutatividade
                    ("y", "x"),  # Ordem invertida
                    ("z", "x"),  # Mistura Z-X
                ]
                sensitivity_multiplier = 1.5
            elif market_regime == "low_volatility":
                # Baixa volatilidade: usar operadores mais sensíveis
                operator_pairs = [
                    ("x", "z"),  # Padrão sensível
                    ("y", "z"),  # Y-Z para detecção fina
                ]
                sensitivity_multiplier = 2.0
            else:  # Regime normal
                operator_pairs = [
                    ("x", "z"),  # Padrão: X e Z
                    ("y", "z"),  # Y e Z
                    ("x", "y"),  # X e Y
                    ("z", "x"),  # Z e X (ordem invertida)
                ]
                sensitivity_multiplier = 1.0

            # Selecionar par de operadores com variação temporal
            pair_index = int((self.run_count + time_step) % len(operator_pairs))
            op_w_type, op_v_type = operator_pairs[pair_index]

            logger.debug(
                f"OTOC usando operadores: W={op_w_type.upper()}, V={op_v_type.upper()}, "
                f"regime={market_regime}, sensitivity={sensitivity_multiplier:.1f}x"
            )

            # Criar operadores Pauli dinamicamente com parametrização refinada
            ops = {}
            for pauli_type in ["x", "y", "z"]:
                from qiskit import QuantumCircuit

                qc_pauli = QuantumCircuit(1)
                if pauli_type == "x":
                    qc_pauli.x(0)
                elif pauli_type == "y":
                    qc_pauli.y(0)
                elif pauli_type == "z":
                    qc_pauli.z(0)
                ops[pauli_type] = Operator(qc_pauli).data

            W = ops[op_w_type]
            V = ops[op_v_type]

            # YAA CORREÇÃO: Construção correta dos operadores tensoriais
            def create_tensor_operator(single_op, target_qubit, total_qubits):
                """Cria operador tensorial correto."""
                operators = []
                for i in range(total_qubits):
                    if i == target_qubit:
                        operators.append(single_op)
                    else:
                        operators.append(np.eye(2))

                # Produto tensorial correto
                result = operators[0]
                for op in operators[1:]:
                    result = np.kron(result, op)
                return result

            # Construir operadores para múltiplos qubits alvo
            W_ext = np.eye(2**n_qubits, dtype=complex)
            V_ext = np.eye(2**n_qubits, dtype=complex)

            for q_idx in target_qubits:
                if q_idx < n_qubits:  # Validação de índice
                    W_q = create_tensor_operator(W, q_idx, n_qubits)
                    V_q = create_tensor_operator(V, q_idx, n_qubits)

                    # Aplicar operadores (produto para múltiplos qubits)
                    W_ext = W_ext @ W_q
                    V_ext = V_ext @ V_q

            # YAA REFINAMENTO: Evolução temporal adaptativa e sensível
            # Ajustar sensibilidade temporal baseada no regime de mercado
            base_temporal_factor = (self.run_count % 16) + 1  # 1 a 16 (ampliado)
            adaptive_temporal_factor = base_temporal_factor * sensitivity_multiplier

            # Fase quântica refinada com múltiplas frequências
            primary_phase = np.exp(
                1j * time_step * adaptive_temporal_factor * np.pi / 12
            )

            # Adicionar componente harmônica para maior sensibilidade
            harmonic_phase = np.exp(1j * time_step * (self.run_count % 7) * np.pi / 20)

            # Fase combinada para detecção de padrões sutis
            combined_phase = primary_phase * harmonic_phase
            W_t_ext = combined_phase * W_ext

            # Calcular o comutador [W(t), V(0)]
            commutator = W_t_ext @ V_ext - V_ext @ W_t_ext

            # OTOC = <[W(t), V(0)]^† [W(t), V(0)]>
            if isinstance(statevector, Statevector):
                try:
                    otoc_operator_matrix = commutator.conj().T @ commutator
                    otoc_qiskit_operator = Operator(otoc_operator_matrix)

                    if statevector.num_qubits != otoc_qiskit_operator.num_qubits:
                        logger.error(
                            f"P-6 OTOC: Falha na validação final de dimensões. "
                            f"SV: {statevector.num_qubits}, Op: {otoc_qiskit_operator.num_qubits}"
                        )
                        return 0.5

                    otoc_val = statevector.expectation_value(otoc_qiskit_operator)

                    # YAA REFINAMENTO: Normalização adaptativa baseada no contexto
                    max_theoretical = 4.0 * len(target_qubits) * sensitivity_multiplier
                    otoc_normalized = float(np.abs(otoc_val) / max_theoretical)

                    # Aplicar filtro de sensibilidade adaptativo
                    otoc_refined = self._apply_sensitivity_filter(
                        otoc_normalized, market_regime
                    )

                    logger.debug(
                        f"calculate_otoc result=%.4f -> %.4f (regime: {market_regime}, "
                        f"ops: {op_w_type.upper()}/{op_v_type.upper()}, "
                        f"temporal_factor: {adaptive_temporal_factor:.1f})",
                        otoc_normalized,
                        otoc_refined,
                    )

                    self._check_otoc_constancy(otoc_refined)
                    return None if self.invalid_metric else otoc_refined

                except Exception as e:
                    logger.error(f"Erro ao calcular expectation_value para OTOC: {e}")
                    self._check_otoc_constancy(0.5)
                    return None if self.invalid_metric else 0.5
            else:
                # Fallback para matriz densidade
                try:
                    rho = statevector
                    otoc_val = np.trace(rho @ commutator.conj().T @ commutator)
                    max_theoretical = 4.0 * len(target_qubits) * sensitivity_multiplier
                    otoc_normalized = float(np.abs(otoc_val) / max_theoretical)

                    otoc_refined = self._apply_sensitivity_filter(
                        otoc_normalized, market_regime
                    )

                    logger.debug(
                        "calculate_otoc result (density)=%.4f -> %.4f",
                        otoc_normalized,
                        otoc_refined,
                    )
                    self._check_otoc_constancy(otoc_refined)
                    return None if self.invalid_metric else otoc_refined
                except Exception as e:
                    logger.error(f"Erro ao calcular OTOC com matriz densidade: {e}")
                    self._check_otoc_constancy(0.5)
                    return None if self.invalid_metric else 0.5

        except Exception as e:
            logger.error(f"Erro no cálculo de OTOC: {e}", exc_info=True)
            self._check_otoc_constancy(None)
            return None

    def _detect_market_regime(self) -> str:
        """
        YAA REFINEMENT: Detecção de regime de mercado quântico ultra-sensível.

        Implementa análise multi-modal baseada em:
        - Volatilidade de métricas quânticas (entropia, coerência, OTOC)
        - Análise espectral de correlações temporais
        - Detecção de transições de fase emergentes
        - Harmônicos de frequência quântica
        """
        try:
            # Coleta de dados históricos expandida
            recent_metrics = list(self.metrics._data.items())[-20:]  # Aumentar janela

            if len(recent_metrics) < 5:
                return "normal"

            # === YAA REFINEMENT: Análise Multi-Modal ===

            # 1. Coleta de métricas quânticas
            entropy_values = []
            coherence_values = []
            otoc_values = []
            measurement_counts = []

            for _, metrics_dict in recent_metrics:
                if "quantum_entropy" in metrics_dict:
                    entropy_values.extend(metrics_dict["quantum_entropy"])
                if "quantum_coherence" in metrics_dict:
                    coherence_values.extend(metrics_dict["quantum_coherence"])
                if "otoc_calculated" in metrics_dict:
                    if metrics_dict["otoc_calculated"] is not None:
                        otoc_values.append(metrics_dict["otoc_calculated"])
                if "measurement_counts" in metrics_dict:
                    measurement_counts.append(
                        sum(list(metrics_dict["measurement_counts"].values()))
                    )

            if not entropy_values or not coherence_values:
                return "normal"

            # 2. Análise de volatilidade temporal refinada
            recent_window = min(8, len(entropy_values))
            if recent_window < 3:
                return "normal"

            entropy_recent = entropy_values[-recent_window:]
            coherence_recent = coherence_values[-recent_window:]

            # Calcular múltiplas medidas de volatilidade
            entropy_std = np.std(entropy_recent)
            coherence_std = np.std(coherence_recent)

            # Volatilidade de segunda ordem (variação da variação)
            if len(entropy_values) >= 10:
                entropy_windows = [
                    entropy_values[i : i + 3] for i in range(len(entropy_values) - 3)
                ]
                entropy_stds = [np.std(window) for window in entropy_windows]
                entropy_volatility_of_volatility = (
                    np.std(entropy_stds[-5:]) if len(entropy_stds) >= 5 else 0
                )
            else:
                entropy_volatility_of_volatility = 0

            # 3. Análise OTOC para detecção de scrambling
            otoc_regime_signal = "normal"
            if otoc_values:
                recent_otoc = otoc_values[-5:] if len(otoc_values) >= 5 else otoc_values
                otoc_mean = np.mean(recent_otoc)
                otoc_std = np.std(recent_otoc) if len(recent_otoc) > 1 else 0

                # OTOC próximo de 1.0 indica baixo scrambling (baixa volatilidade)
                # OTOC próximo de 0.0 indica alto scrambling (alta volatilidade)
                if otoc_mean > 0.85 and otoc_std < 0.05:
                    otoc_regime_signal = "low_volatility"
                elif otoc_mean < 0.3 or otoc_std > 0.15:
                    otoc_regime_signal = "high_volatility"
                else:
                    otoc_regime_signal = "normal"

            # 4. Análise de correlação cruzada entre métricas
            cross_correlation = 0
            if len(entropy_recent) >= 3 and len(coherence_recent) >= 3:
                from scipy.stats import pearsonr

                corr_coef, _ = pearsonr(entropy_recent, coherence_recent)
                cross_correlation = abs(corr_coef) if not np.isnan(corr_coef) else 0

            # 5. Detecção de transições de fase
            phase_transition_detected = False
            if len(entropy_values) >= 6:
                # Detectar mudanças abruptas na entropia
                entropy_diff = np.diff(entropy_values[-6:])
                max_entropy_jump = np.max(np.abs(entropy_diff))
                if max_entropy_jump > 0.2:  # Threshold para transição de fase
                    phase_transition_detected = True

            # 6. Análise de medições para regime de colapso
            measurement_instability = 0
            if measurement_counts:
                measurement_std = np.std(measurement_counts) / (
                    np.mean(measurement_counts) + 1e-8
                )
                measurement_instability = measurement_std

            # === YAA REFINEMENT: Lógica de Classificação Refinada ===

            # Calcular scores compostos com pesos adaptativos
            volatility_score = (
                entropy_std * 2.0
                + coherence_std * 1.5
                + entropy_volatility_of_volatility * 3.0
                + measurement_instability * 1.0
            )

            stability_score = (
                cross_correlation * 0.5
                + (1.0 - entropy_std) * 0.3
                + (1.0 - coherence_std) * 0.2
            )

            # Thresholds adaptativos baseados no histórico
            base_high_threshold = 0.12
            base_low_threshold = 0.04

            # Ajustar thresholds baseado no contexto OTOC
            if otoc_regime_signal == "high_volatility":
                high_threshold = base_high_threshold * 0.8  # Mais sensível
                low_threshold = base_low_threshold * 0.9
            elif otoc_regime_signal == "low_volatility":
                high_threshold = base_high_threshold * 1.2  # Menos sensível
                low_threshold = base_low_threshold * 1.1
            else:
                high_threshold = base_high_threshold
                low_threshold = base_low_threshold

            # Decisão final com múltiplos critérios
            if (
                volatility_score > high_threshold
                or phase_transition_detected
                or otoc_regime_signal == "high_volatility"
            ):
                regime = "high_volatility"
            elif (
                volatility_score < low_threshold
                and stability_score > 0.7
                and otoc_regime_signal != "high_volatility"
            ):
                regime = "low_volatility"
            else:
                regime = "normal"

            # Logging detalhado para debugging
            logger.debug(
                f"Regime Detection - Volatility Score: {volatility_score:.4f}, "
                f"Stability Score: {stability_score:.4f}, "
                f"OTOC Signal: {otoc_regime_signal}, "
                f"Phase Transition: {phase_transition_detected}, "
                f"Final Regime: {regime}"
            )

            return regime

        except Exception as e:
            logger.debug(f"Erro na detecção de regime de mercado: {e}")
            return "normal"

    def _apply_sensitivity_filter(self, otoc_value: float, market_regime: str) -> float:
        """Aplicar filtro de sensibilidade adaptativo ao valor OTOC."""
        try:
            if market_regime == "high_volatility":
                # Alta volatilidade: suavizar para evitar ruído
                filter_factor = 0.85
            elif market_regime == "low_volatility":
                # Baixa volatilidade: amplificar sensibilidade
                filter_factor = 1.15
            else:
                # Regime normal: manter valor original
                filter_factor = 1.0

            # Aplicar filtro mantendo valores no range [0, 1]
            filtered_value = otoc_value * filter_factor
            return float(np.clip(filtered_value, 0.0, 1.0))

        except Exception as e:
            logger.debug(f"Erro no filtro de sensibilidade: {e}")
            return otoc_value

    def _check_otoc_constancy(self, new_value: Optional[float]) -> bool:
        """Track consecutive OTOC values and flag metric invalidity.

        Reseta automaticamente o contador quando ``invalid_metric`` persiste
        por ``_INVALID_METRIC_RESET_CYCLES`` ciclos e aciona a mistura
        aleatória na próxima construção de circuito.
        """
        if new_value is None:
            self._last_otoc_value = None
            self._otoc_constant_count = 0
            self._otoc_stagnant = False
            self.invalid_metric = False
            self._invalid_metric_cycles = 0
            return False

        if self._last_otoc_value is None or not np.isclose(
            new_value, self._last_otoc_value
        ):
            # Valor mudou ou primeira medição: reinicia contagem
            self._otoc_constant_count = 1
            stagnation = False
            self.invalid_metric = False
            self._invalid_metric_cycles = 0
        else:
            # Valor se repete. Incrementa contador para detectar estagnação
            self._otoc_constant_count += 1
            stagnation = self._otoc_constant_count >= 2
            if getattr(self, "_last_circuit_from_cache", False):
                log_fn = logger.debug
            elif self._otoc_constant_count < 3:
                log_fn = logger.info
            else:
                log_fn = logger.warning

            log_fn(
                "OTOC permanece constante entre execuções: %.4f",
                new_value,
            )

            if self._otoc_constant_count >= 3:
                self.invalid_metric = True
                self._invalid_metric_cycles += 1
                logger.warning(
                    "OTOC inalterado por %d ciclos. Marcando métrica como inválida.",
                    self._otoc_constant_count,
                )

        if (
            self.invalid_metric
            and self._invalid_metric_cycles >= self._INVALID_METRIC_RESET_CYCLES
        ):
            logger.info(
                "Métrica OTOC inválida por %d ciclos; reset automático acionado.",
                self._invalid_metric_cycles,
            )
            self._last_otoc_value = None
            self._otoc_constant_count = 0
            self.invalid_metric = False
            self._invalid_metric_cycles = 0
            self.scramble_random_on_next_build = True
            stagnation = False

        if stagnation:
            self.scramble_random_on_next_build = True
        elif new_value is not None and new_value > 0.98:
            self.scramble_random_on_next_build = True

        self._last_otoc_value = new_value
        self._otoc_stagnant = stagnation
        return stagnation

    # Inserção de método observe_and_emit para ciclo Buraco Negro ↔ Horizonte ↔ Buraco Branco
    async def observe_and_emit(
        self,
        market_input: Dict[str, Any],
        # Adicionar parâmetros para os resultados do QAST (que são calculados externamente)
        qast_expr: Optional[Any] = None,
        qast_entropy_symbolic: Optional[float] = None,
        qast_coherence_metric: Optional[float] = None,
        # Parâmetros existentes para generate_quantum_signature
        method: str = "amplitude_vector",
        N_first_probs: Optional[int] = None,
        use_metrics_keys: Optional[List[str]] = None,
    ) -> Tuple[
        Optional[QuantumSignaturePacket], float, Dict[str, Any]
    ]:  # Retorna (Assinatura, MassaInformacionalAtual, MétricasSnapshot)
        """
        Orquestra a observação do mercado, codificação, e emissão da assinatura.
        Este é um método central que encapsula o ciclo de Buraco Negro -> Horizonte -> Buraco Branco.
        """
        logger.info(
            f"Universo: Chamada observe_and_emit com input: {market_input}, qast_expr: {qast_expr is not None}, gqs_method: {method}"
        )

        # 1. Buraco Negro - Absorção Informacional
        # Codifica o input de mercado, atualizando self.qc, self.encoded_state, e self.informational_mass
        encoded_result = self.encode_market_data(market_input)
        if asyncio.iscoroutine(encoded_result):
            await encoded_result

        current_info_increment = None
        try:
            current_info_increment = entropy(np.array([1.0]))
        except Exception:
            current_info_increment = None
        if current_info_increment is None or (
            isinstance(current_info_increment, float)
            and np.isnan(current_info_increment)
        ):
            logger.warning(
                "Incremento de massa informacional calculado como None ou NaN. Usando fallback."
            )
            self.informational_mass += len(market_input.keys())
        else:
            self.informational_mass += float(current_info_increment)

        # Atualiza o current_sv baseado no estado codificado (após encode_market_data ter definido self.qc)
        await self.update_last_statevector()

        # 2. Horizonte de Eventos / Buraco Branco - Observação e Emissão
        # Gera a assinatura quântica. Isso pode ou não reduzir a informational_mass
        # dependendo dos parâmetros qast_ passados.
        current_signature_packet = await self.generate_quantum_signature(
            expr=qast_expr,
            entropy_symbolic=qast_entropy_symbolic,
            coherence_metric=qast_coherence_metric,
            method=method,
            N_first_probs=N_first_probs,
            use_metrics_keys=use_metrics_keys,
        )

        # Captura o snapshot das métricas APÓS a possível emissão
        current_metrics_snapshot = self.get_latest_metrics(
            get_last_value_from_list=True
        )
        current_metrics_snapshot["current_informational_mass"] = self.informational_mass

        logger.info(
            f"Universo: observe_and_emit concluído. Massa final: {self.informational_mass:.4f}"
        )
        if current_signature_packet:
            logger.debug(
                f"Universo: Assinatura gerada: {current_signature_packet.source_details}"
            )
            if self.qpm is not None:
                try:
                    self.qpm.store_pattern(
                        current_signature_packet,
                        market_input,
                        {},
                        {"origin": "observe_and_emit"},
                    )
                    _ = self.qpm.recall_similar_patterns(
                        current_signature_packet.vector, top_n=3
                    )
                except Exception as e_qpm:
                    logger.error(
                        "Falha ao registrar ou recuperar padrões na QPM: %s",
                        e_qpm,
                    )

        return (
            current_signature_packet,
            self.informational_mass,
            current_metrics_snapshot,
        )

    async def recursive_qast_universe(
        self, qast: Dict[str, Any], input_value: float, *, depth: int = 0
    ) -> Optional[QuantumSignaturePacket]:
        """
        Executa um loop recursivo de processamento QAST e emissão Hawking simbólica,
        reduzindo a informational_mass até atingir um mass_threshold.

        Args:
            qast (Dict[str, Any]): O objeto de estado do QAST.
            input_value (float): O valor de input para o ciclo QAST.
            depth (int): Profundidade atual da recursão (para logging e controle).

        Returns:
            Optional[QuantumSignaturePacket]: A assinatura quântica final gerada quando
                                             a recursão termina, ou None se algo der errado.
        """
        # Registrar a massa informacional no início/antes de cada ciclo recursivo
        if hasattr(self, "metrics") and hasattr(self.metrics, "add_entry"):
            self.metrics.add_entry("informational_mass", self.informational_mass)

        # Guarda contra estouro de pilha
        if depth > 950:  # Limite um pouco abaixo do default do Python (1000)
            logger.warning(
                f"🌀 Recursão QAST/Universo atingiu o limite de profundidade ({depth}). Encerrando prematuramente para evitar stack overflow. Massa informacional atual: {self.informational_mass:.4f}"
            )
            # Tentar gerar uma assinatura com o estado atual, se possível
            if self.current_sv is None:
                try:
                    await self.update_last_statevector()
                except Exception as e_upd_sv_guard:
                    logger.error(
                        f"Falha ao tentar self.update_last_statevector() no guarda de profundidade: {e_upd_sv_guard}"
                    )
            return await self.generate_quantum_signature(method="amplitude_vector")

        if self.informational_mass <= self.mass_threshold:
            logger.info(
                f"🌀 Recursão QAST/Universo encerrada na profundidade {depth}. Massa informacional ({self.informational_mass:.4f}) atingiu o limiar ({self.mass_threshold:.4f}). Gerando assinatura final."
            )
            # Garantir que current_sv exista antes de gerar a assinatura final
            if self.current_sv is None:
                try:
                    await self.update_last_statevector()
                    if self.current_sv is None:
                        logger.warning(
                            "Não foi possível obter current_sv para generate_quantum_signature no final da recursão."
                        )
                        return None  # Retorna None se ainda não houver SV
                except Exception as e_upd_sv_base:
                    logger.error(
                        f"Falha ao tentar self.update_last_statevector() no caso base da recursão: {e_upd_sv_base}"
                    )
                    return None  # Retorna None se a atualização falhar

            return await self.generate_quantum_signature(
                method="amplitude_vector"
            )  # Exemplo de método

        # Chamar QAST para processamento simbólico
        expr, entropy_sym = self.qast_reflect_and_guide(qast, input_value)

        # Emissão Hawking Simbólica – reduz a massa informacional
        emitted_information_potential = self.hawking_factor * max(
            float(entropy_sym), 1e-6
        )

        # Garantir que a emissão não exceda a massa disponível
        emitted_information = min(
            emitted_information_potential, self.informational_mass
        )

        # Reduzir a massa informacional, garantindo que não fique negativa.
        previous_mass = self.informational_mass
        self.informational_mass = max(
            self.informational_mass - emitted_information, 0.0
        )

        logger.debug(
            f"Profundidade QAST/Universo {depth}: Input={input_value:.4f}, EntropiaSimb={entropy_sym:.4f} -> PotencialEmit={emitted_information_potential:.4f}, RealEmit={emitted_information:.4f}. Massa Informacional: {previous_mass:.4f} -> {self.informational_mass:.4f}"
        )

        # Registrar a massa informacional após a dedução (para a curva de evaporação)
        if hasattr(self, "metrics") and hasattr(self.metrics, "add_entry"):
            self.metrics.add_entry("informational_mass", self.informational_mass)

        # Chamada recursiva para o próximo ciclo de processamento e emissão
        return await self.recursive_qast_universe(qast, input_value, depth=depth + 1)

    async def _generate_quantum_signature_async(
        self,
        *,
        expr: Optional[Any] = None,
        entropy_symbolic: Optional[float] = None,
        coherence_metric: Optional[float] = None,
        method: str = "amplitude_vector",
        N_first_probs: Optional[int] = None,
        use_metrics_keys: Optional[List[str]] = None,
        statevector_override: Optional[Statevector] = None,
    ) -> Optional[QuantumSignaturePacket]:
        """Internal async worker for :meth:`generate_quantum_signature`.

        Parameters
        ----------
        expr : Any, optional
            Expressão simbólica produzida pelo QAST.
        entropy_symbolic : float, optional
            Entropia simbólica calculada pelo QAST.
        coherence_metric : float, optional
            Métrica de coerência proveniente do QAST.
        method : str, default ``"amplitude_vector"``
            Método de geração do vetor de assinatura.
        N_first_probs : int, optional
            Número de probabilidades a incluir quando ``method`` é
            ``"first_n_probs"``.
        use_metrics_keys : list of str, optional
            Conjunto de métricas a utilizar quando ``method`` é
            ``"selected_metrics"``.
        statevector_override : Statevector, optional
            Estado quântico a ser utilizado em vez de ``current_sv``.

        Returns
        -------
        Optional[QuantumSignaturePacket]
            Pacote de assinatura ou ``None`` em caso de erro ou método inválido.
        """

        initial_mass = self.informational_mass
        sv_source = statevector_override or self.current_sv

        if statevector_override is not None:
            norm_sv = np.linalg.norm(statevector_override.data)
            if not np.isclose(norm_sv, 1.0):
                raise ValueError("Statevector not normalized")

        if (
            method
            in {
                "amplitude_vector",
                "probabilities",
                "first_n_probs",
                "density_matrix",
                "second_order_moments",
                "phase_vector",
                "cumulative_probabilities",
            }
            and sv_source is None
        ):
            logger.warning(
                "Vetor de estado (sv_source) não disponível para métodos baseados em SV."
            )
            return None

        vector: List[float]
        vector_type = None

        if method == "amplitude_vector":
            amps = np.asarray(sv_source.data, dtype=complex)
            vector = np.column_stack((np.real(amps), np.imag(amps))).ravel().tolist()
            arr = np.asarray(vector, dtype=float)
            norm = np.linalg.norm(arr)
            if norm > 1e-9:
                arr /= norm
            vector = arr.tolist()
            vector_type = VectorType.AMPLITUDE_VECTOR_INTERLEAVED_FLOAT
        elif method == "probabilities":
            probs = np.abs(np.asarray(sv_source.data)) ** 2
            probs = probs / float(np.sum(probs)) if probs.sum() > 0 else probs
            vector = probs.tolist()
            vector_type = VectorType.PROBABILITY_DISTRIBUTION
        elif method == "first_n_probs":
            probs = np.abs(np.asarray(sv_source.data)) ** 2
            probs = probs / float(np.sum(probs)) if probs.sum() > 0 else probs
            N = N_first_probs or len(probs)
            vector = probs[:N].tolist() + [0.0] * max(0, N - len(probs))
            vector_type = VectorType.FIRST_N_PROBABILITIES
        elif method == "phase_vector":
            amps = np.asarray(sv_source.data, dtype=complex)
            phases = np.angle(amps)
            vector = phases.tolist()
            vector_type = VectorType.PHASE_VECTOR
        elif method == "cumulative_probabilities":
            probs = np.abs(np.asarray(sv_source.data)) ** 2
            probs = probs / float(np.sum(probs)) if probs.sum() > 0 else probs
            cumsum = np.cumsum(probs)
            vector = cumsum.tolist()
            vector_type = VectorType.CUMULATIVE_PROBABILITIES
        elif method == "selected_metrics":
            if not use_metrics_keys:
                logger.warning(
                    "Assinatura Quântica: 'selected_metrics' escolhido, mas use_metrics_keys está vazio."
                )
                return None
            snapshot = self.get_latest_metrics(get_last_value_from_list=True)
            values = []
            for key in use_metrics_keys:
                if key not in snapshot:
                    logger.warning(
                        "Métrica '%s' não encontrada no snapshot atual para assinatura.",
                        key,
                    )
                    values.append(0.0)
                    continue
                val = snapshot[key]
                if isinstance(val, (int, float)) and not isinstance(val, bool):
                    values.append(float(val))
                else:
                    logger.debug("Métrica '%s' (valor: %s) não é escalar", key, val)
            arr = np.asarray(values, dtype=float)
            norm = np.linalg.norm(arr)
            if norm > 1e-9:
                arr /= norm
            vector = arr.tolist()
            vector_type = VectorType.SELECTED_METRICS_VECTOR
        elif method == "density_matrix":
            sv_arr = np.asarray(sv_source.data, dtype=complex)
            rho = np.outer(sv_arr, np.conjugate(sv_arr))
            flat = np.column_stack((np.real(rho).ravel(), np.imag(rho).ravel())).ravel()
            norm = np.linalg.norm(flat)
            if norm > 1e-9:
                flat /= norm
            vector = flat.tolist()
            vector_type = VectorType.DENSITY_MATRIX_VECTOR
        elif method == "second_order_moments":
            probs = np.abs(np.asarray(sv_source.data)) ** 2
            moments = np.outer(probs, probs).ravel()
            moments /= float(np.sum(moments)) if np.sum(moments) > 0 else 1.0
            vector = moments.tolist()
            vector_type = VectorType.SECOND_ORDER_MOMENTS_VECTOR
        else:
            logger.warning("Método desconhecido '%s'", method)
            return None

        if not vector:
            logger.error(
                "generate_quantum_signature produziu vetor vazio usando o metodo %s",
                method,
            )
            return None

        emitted_value = None
        if (
            expr is not None
            and entropy_symbolic is not None
            and coherence_metric is not None
        ):
            norm_entropy = float(np.clip(entropy_symbolic, 0.01, 10.0))
            norm_coherence = float(np.clip(coherence_metric, 0.0, 1.0))
            expr_complexity = float(np.clip(len(str(expr)) / 50.0, 0.1, 2.0))
            emission_potential = norm_entropy * norm_coherence * expr_complexity
            calculated = emission_potential * 0.05
            emitted_value = float(
                np.clip(calculated, 0.0, self.informational_mass * 0.50)
            )
            self.informational_mass = max(self.informational_mass - emitted_value, 0.0)

        base_metrics = self.get_latest_metrics(get_last_value_from_list=True)
        metrics_snapshot = {
            k: base_metrics[k]
            for k in (
                "quantum_entropy",
                "otoc",
                "quantum_coherence",
                "page_entropy",
            )
            if k in base_metrics
        }
        if emitted_value is not None:
            metrics_snapshot["emitted_information_value"] = emitted_value
            metrics_snapshot["informational_mass_after_emission"] = (
                self.informational_mass
            )

        n_qubits_calc: Optional[int] = None
        if sv_source is not None and hasattr(sv_source, "data"):
            try:
                n_qubits_calc = int(np.log2(len(sv_source.data)))
            except Exception:
                n_qubits_calc = None
        if n_qubits_calc is None and vector:
            try:
                if method == "amplitude_vector":
                    n_qubits_calc = int(np.log2(len(vector) / 2))
                elif method in {
                    "probabilities",
                    "first_n_probs",
                    "density_matrix",
                    "second_order_moments",
                    "phase_vector",
                    "cumulative_probabilities",
                }:
                    n_qubits_calc = int(np.log2(len(vector)))
            except Exception:
                n_qubits_calc = None

        source_details: Dict[str, Any] = {
            "n_qubits": n_qubits_calc if n_qubits_calc is not None else self.n_qubits,
            "method": method,
            "vector_type": vector_type.value if vector_type else None,
            "current_informational_mass": initial_mass,
        }
        if method == "first_n_probs" and N_first_probs is not None:
            source_details["N_first_probs"] = N_first_probs
        if method == "selected_metrics" and use_metrics_keys is not None:
            source_details["use_metrics_keys"] = use_metrics_keys

        packet = QuantumSignaturePacket(
            vector=vector,
            metrics=metrics_snapshot,
            source_details=source_details,
        )

        return packet

    async def generate_quantum_signature(
        self, **kwargs: Any
    ) -> Optional[QuantumSignaturePacket]:
        """Async wrapper to create a quantum signature."""

        return await self._generate_quantum_signature_async(**kwargs)

    def generate_quantum_signature_sync(
        self, **kwargs: Any
    ) -> Optional[QuantumSignaturePacket]:
        """Synchronous wrapper for :meth:`generate_quantum_signature`."""

        return asyncio.run(self._generate_quantum_signature_async(**kwargs))

    def clear_memory(self) -> None:
        """Limpa todos os padrões armazenados na memória."""
        if hasattr(self, "pattern_memory") and self.pattern_memory is not None:
            self.pattern_memory.clear_memory()
            logger.info("QUALIAQuantumUniverse: Memória de padrões limpa.")
        else:
            logger.warning(
                "QUALIAQuantumUniverse: pattern_memory não está inicializado."
            )

    def _get_params_dict(self) -> Dict[str, Any]:
        """
        Retorna um dicionário completo com todos os parâmetros de configuração do universo.

        Returns:
            Dict[str, Any]: Dicionário contendo todos os parâmetros configuráveis do universo quântico.
        """

        params = {
            # Parâmetros quânticos fundamentais
            "n_qubits": self.n_qubits,
            "scr_depth": self.scr_depth,
            "base_lambda": self.base_lambda,
            "alpha": self.alpha,
            "retro_strength": self.retro_strength,
            "num_ctc_qubits": self.num_ctc_qubits,
            # Parâmetros de execução
            "measure_frequency": self.measure_frequency,
            "thermal_coefficient": self.thermal_coefficient,
            "backend_name": self.backend_name,
            "shots": self.shots,
            "qpu_steps": self.qpu_steps,
            # Parâmetros de controle adaptativo
            "qast_feedback_enabled": self.qast_feedback_enabled,
            "lambda_factor_multiplier": self.lambda_factor_multiplier,
            "enrichment_cycles": self.enrichment_cycles,
            "adaptive_threshold": self.adaptive_threshold,
            "entanglement_style": self.entanglement_style,
            # Parâmetros de estado e histórico
            "max_history_size": self.max_history_size,
            "initial_state_type": self.initial_state_type,
            # Parâmetros de massa informacional
            "informational_mass": self.informational_mass,
            "initial_informational_mass": self.initial_informational_mass,
            "min_informational_mass": self.min_informational_mass,
            "hawking_factor": self.hawking_factor,
            "mass_threshold": self.mass_threshold,
            # Parâmetros térmicos
            "thermal_J_coupling": self.thermal_J_coupling,
            "thermal_beta_inverse_temp": self.thermal_beta_inverse_temp,
            "thermal_noise_enabled": self.thermal_noise_enabled,
            "thermal_noise_temperature": self.thermal_noise_temperature,
            "temperature": self.temperature,
            # Configurações avançadas
            "eqci_config": self.eqci_config,
            "qpm_config": self.qpm_config,
            "allow_qft_fallback": self.allow_qft_fallback,
            # Parâmetros QAST
            "qast_influence_on_lambda_factor": self.qast_influence_on_lambda_factor,
            "alpha_decay_type": self.alpha_decay_type,
            "alpha_H_symb_decay_base": self.alpha_H_symb_decay_base,
            "alpha_H_symb_decay_factor_linear": self.alpha_H_symb_decay_factor_linear,
            "alpha_H_symb_decay_factor_quadratic": self.alpha_H_symb_decay_factor_quadratic,
            "alpha_logistic_max": self.alpha_logistic_max,
            "alpha_logistic_beta": self.alpha_logistic_beta,
            "alpha_logistic_H0": self.alpha_logistic_H0,
            # Parâmetros do controlador PID
            "qast_input_control_enabled": self.qast_input_control_enabled,
            "qast_input_Kp": self.qast_input_Kp,
            "qast_input_Ki": self.qast_input_Ki,
            "qast_input_target_H_symb": self.qast_input_target_H_symb,
            "qast_input_min": self.qast_input_min,
            "qast_input_max": self.qast_input_max,
            "current_qast_input_value": self.current_qast_input_value,
            # Modo de controle
            "control_mode": self.control_mode,
        }

        # Adicionar parâmetros LQR se existirem
        if (
            hasattr(self, "target_informational_mass")
            and self.target_informational_mass is not None
        ):
            params["target_informational_mass"] = self.target_informational_mass
        if (
            hasattr(self, "target_H_symb_prime")
            and self.target_H_symb_prime is not None
        ):
            params["target_H_symb_prime"] = self.target_H_symb_prime
        if hasattr(self, "target_S_quantum") and self.target_S_quantum is not None:
            params["target_S_quantum"] = self.target_S_quantum

        # Filtrar somente parâmetros aceitos pelo __init__ para permitir
        # reutilização segura deste dicionário na criação de novas instâncias.
        allowed = set(inspect.signature(self.__init__).parameters)
        filtered = {k: v for k, v in params.items() if k in allowed}

        logger.debug(
            "QUALIAQuantumUniverse._get_params_dict retornando %s parâmetros",
            len(filtered),
        )
        return filtered

    def _get_circuit_cache_key(
        self,
        steps: int,
        thermal: bool = True,
        temperature: float = 0.05,
        retro_mode: str = "none",
        measure_frequency: int = 1,
        scr_depth_override: Optional[int] = None,
        apply_scrambling: bool = True,
    ) -> str:
        """Wrapper around :func:`generate_circuit_cache_key`."""
        scr_depth = scr_depth_override or self.scr_depth
        return generate_circuit_cache_key(
            steps,
            thermal,
            temperature,
            retro_mode,
            measure_frequency,
            scr_depth,
            apply_scrambling,
            self.n_qubits,
        )

    def _clear_circuit_cache(self) -> None:
        """Wrapper around :func:`clear_circuit_cache`."""
        clear_circuit_cache(self._circuit_cache)

    def run_on_backends(
        self,
        circuit: QuantumCircuit,
        shots: int = 1024,
        noise_model: Optional[Any] = None,
    ) -> Dict:
        """Proxy para :func:`run_on_backends` preservando ``use_gpu`` da instância."""

        return run_on_backends(circuit, shots, noise_model, use_gpu=self.use_gpu)

    def invalidate_qft_cache(self, n_qubits: Optional[int] | None = None) -> None:
        """Wrapper for :func:`invalidate_qft_cache`."""

        invalidate_qft_cache(self.n_qubits if n_qubits is None else n_qubits)

    def get_universe_state_representation(
        self, format_type: str = "statevector"
    ) -> Optional[Any]:
        """Wrapper around :func:`get_state_representation`."""
        logger.debug(
            f"QUALIAQuantumUniverse.get_universe_state_representation chamado com format_type='{format_type}'"
        )
        return get_state_representation(self.current_sv, format_type)

    def evolve_under_qpt_channel(self, qpt_channel_op: Any) -> None:
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            asyncio.run(self._evolve_under_qpt_channel_async(qpt_channel_op))
        else:
            loop.create_task(self._evolve_under_qpt_channel_async(qpt_channel_op))

    async def _evolve_under_qpt_channel_async(self, qpt_channel_op: Any) -> None:
        """
        Evolui o estado quântico atual do universo sob um operador de canal QPT (Quantum Process Tomography) fornecido.
        O qpt_channel_op deve ser um objeto compatível com o método `evolve` do Statevector do Qiskit (ex: Kraus).
        """
        logger.debug("QUALIAQuantumUniverse.evolve_under_qpt_channel chamado.")
        if self.current_sv is None:
            logger.warning(
                "QUALIAQuantumUniverse: current_sv é None. Não é possível evoluir sob canal QPT."
            )
            return

        if qpt_channel_op is None:
            logger.error(
                "QUALIAQuantumUniverse: Operador de canal QPT inválido ou não fornecido."
            )
            return

        try:
            async with self._sv_lock:
                self.last_statevector = shallow_copy_statevector(self.current_sv)
                # A evolução aqui deve ser feita com cuidado para garantir que o tipo do qpt_channel_op seja correto.
                # Se qpt_channel_op for uma QuantumCircuit, current_sv.evolve(QuantumCircuit) é válido.
                # Se for um Kraus, também.
                self.current_sv = self.current_sv.evolve(qpt_channel_op)
                if hasattr(
                    self.current_sv, "metadata"
                ):  # Adiciona metadata se não existir
                    if self.current_sv.metadata is None:
                        self.current_sv.metadata = {}
                elif Statevector is not None and isinstance(
                    self.current_sv, Statevector
                ):  # Garante que é um Statevector antes de tentar adicionar metadata
                    self.current_sv.metadata = {}

                self.state_history.append(shallow_copy_statevector(self.current_sv))
                if len(self.state_history) > self.max_history_size:
                    self.state_history.pop(0)
            logger.info("QUALIAQuantumUniverse: Estado evoluído sob canal QPT.")
        except Exception as e:
            logger.error(
                f"QUALIAQuantumUniverse: Erro ao evoluir estado sob canal QPT: {e}",
                exc_info=True,
            )
            # Considerar reverter para self.last_statevector ou deixar como está e logar o erro.
            # Por ora, o estado pode ter sido parcialmente modificado ou ser inválido.
            # Reverter pode esconder o problema. Logar é crucial.
            # self.current_sv = self.last_statevector # Opcional: reverter para o estado anterior

    def apply_random_scrambling(self, depth: int = 2) -> None:
        """Applies random scrambling layers to the current statevector."""

        if self.current_sv is None:
            logger.warning(
                "QUALIAQuantumUniverse: current_sv é None. Não é possível aplicar scrambling."
            )
            return

        try:
            qc = QuantumCircuit(self.n_qubits)
            advanced_scrambler(qc, qc.qubits[:], depth, style=self.entanglement_style)
            self.current_sv = self.current_sv.evolve(qc)
            self.state_history.append(shallow_copy_statevector(self.current_sv))
            if len(self.state_history) > self.max_history_size:
                self.state_history.pop(0)
            logger.info("Scrambling aleatório aplicado com profundidade %s", depth)
        except Exception as exc:
            logger.error(
                "QUALIAQuantumUniverse: falha ao aplicar scrambling aleatório: %s",
                exc,
                exc_info=True,
            )

    def auto_calibrate_scrambling(
        self,
        epsilon: float = 0.02,
        *,
        max_depth: int = 10,
        max_iters: int = 3,
    ) -> None:
        """Adjust ``scr_depth`` and ``measure_frequency`` based on the OTOC value.

        The goal is to keep the OTOC close to ``1`` while avoiding unnecessary
        CX operations. The method iteratively increases ``scr_depth`` until the
        OTOC reaches ``1 - epsilon`` or ``max_depth``. If the target is reached,
        the method tries to reduce the depth to the minimum that still satisfies
        the threshold. When the threshold cannot be reached and ``measure_frequency``
        is greater than zero, the frequency is decreased to mitigate state
        collapse.
        """

        state = self.get_current_statevector()
        if state is None:
            logger.warning(
                "auto_calibrate_scrambling: statevector indisponível; pulando calibração."
            )
            return

        target = 1.0 - epsilon

        otoc_val = self.calculate_otoc(state, time_step=1, n_qubits=self.n_qubits)
        if otoc_val is None:
            logger.warning("auto_calibrate_scrambling: OTOC não pôde ser calculado.")
            return

        iter_count = 0
        while (
            otoc_val is not None
            and otoc_val < target
            and self.scr_depth < max_depth
            and iter_count < max_iters
        ):
            self.scr_depth += 1
            iter_count += 1
            logger.debug(
                "auto_calibrate_scrambling: aumentando scr_depth para %s",
                self.scr_depth,
            )
            otoc_val = self.calculate_otoc(state, time_step=1, n_qubits=self.n_qubits)

        # Reduce depth if possible to save CX gates
        while self.scr_depth > 1:
            tentative = self.scr_depth - 1
            self.scr_depth = tentative
            new_val = self.calculate_otoc(state, time_step=1, n_qubits=self.n_qubits)
            if new_val is None or new_val < target:
                self.scr_depth = tentative + 1
                break
            logger.debug(
                "auto_calibrate_scrambling: reduzindo scr_depth para %s",
                self.scr_depth,
            )

        if otoc_val is not None and otoc_val < target and self.measure_frequency > 0:
            self.measure_frequency = max(0, self.measure_frequency - 1)
            logger.debug(
                "auto_calibrate_scrambling: ajustando measure_frequency para %s",
                self.measure_frequency,
            )

    def on_tick_metrics(self, metrics: Dict[str, Any]) -> None:
        """Handle metrics at each tick and trigger scrambling if needed."""

        diversity = metrics.get("counts_diversity")
        if diversity is None:
            return

        logger.info("counts_diversity=%.4f", diversity)

        if diversity <= 0.12:
            self.scr_depth += 1
            logger.info("scr_depth incremented to %s", self.scr_depth)
            if diversity < 0.05:
                self.apply_random_scrambling(depth=3)

    def compute_mass_stability(
        self,
        H_profile: Sequence[float],
        M0: Optional[float] = None,
        M_min: Optional[float] = None,
    ) -> Tuple[List[float], List[float]]:
        """
        Wrapper que usa os parâmetros self.alpha_* e self.informational_mass_*
        para simular M_k e V_k em relação a um perfil H_profile.

        Args:
          H_profile: sequência de entropias simbólicas H_k.
          M0: se fornecido, sobrescreve self.initial_informational_mass (ou self.informational_mass se initial não existir).
          M_min: se fornecido, sobrescreve self.min_informational_mass.

        Returns:
          (M_series, V_series) usando os parâmetros atuais da instância.
        """
        alpha_params = {
            "alpha0": self.alpha_H_symb_decay_base,
            "alpha1": self.alpha_H_symb_decay_factor_linear,
            "alpha2": self.alpha_H_symb_decay_factor_quadratic,
        }

        default_M0 = getattr(
            self, "initial_informational_mass", self.informational_mass
        )
        current_M0 = M0 if M0 is not None else default_M0

        current_M_min = M_min if M_min is not None else self.min_informational_mass

        self.logger.debug(
            f"compute_mass_stability: Usando alpha_params={alpha_params}, M0={current_M0}, M_min={current_M_min}"
        )

        M_series, V_series = simulate_mass_dynamics(
            H_profile, alpha_params, current_M0, current_M_min
        )
        return M_series, V_series

    def update_entropy_delta(self, delta_entropy: float) -> None:
        """Atualiza o detector de pulso e executa a reação quando necessário."""

        if self.pulse_detector.update(delta_entropy):
            self.handle_transcendence_pulse(delta_entropy)

    def handle_transcendence_pulse(self, delta_entropy: float) -> None:
        """Reage ao Pulso de Transcendência reduzindo exposição."""

        self.pulse_detected = True
        self.lambda_factor_multiplier *= 0.5
        self.logger.warning(
            "Pulso de Transcendência detectado (ΔH=%.4f). Novo lambda_factor_multiplier=%.3f",
            delta_entropy,
            self.lambda_factor_multiplier,
        )

    def _on_retrocausal_insight(self, payload: Dict[str, Any]) -> None:
        """Handle ``retrocausal.insight`` events applying feedback."""

        strength = float(payload.get("temporal_field_strength", 0.0))
        self.apply_retrocausal_feedback(strength)

    def apply_retrocausal_feedback(self, temporal_field_strength: float) -> None:
        """Adjust parameters based on retrocausal field strength.

        Parameters
        ----------
        temporal_field_strength
            Strength of detected retrocausal influence in ``[0, 1]``.
        """

        threshold = 0.7
        if temporal_field_strength <= threshold:
            return

        prev_alpha = self.alpha
        prev_retro = self.retro_strength
        self.alpha = min(self.alpha * 1.1, 1.0)
        if self.retro_strength > 0:
            new_retro = self.retro_strength * 1.2
        else:
            new_retro = 0.05
        self.retro_strength = min(new_retro, 1.0)

        self.logger.info(
            "Efeito retrocausal detectado, ajustando parâmetro alpha de %.3f para %.3f",
            prev_alpha,
            self.alpha,
        )
        self.logger.info(
            "Efeito retrocausal detectado, ajustando parâmetro retro_strength de %.3f para %.3f",
            prev_retro,
            self.retro_strength,
        )


# Fim da classe QUALIAQuantumUniverse


# retorna um Statevector, e para obter uma matriz densidade explícita, pode-se usar to_operator().
# Se o .evolve com um canal já resultar numa matriz densidade (menos comum para Statevector.evolve),
# então .data seria direto. Confirmar API Qiskit se necessário.
# Para Statevector.evolve(Kraus_op), o resultado é um Statevector (estado puro evoluído) ou uma matriz densidade
# (estado misto evoluído). Se for matriz densidade, .data pode ser usado.
# Se o resultado de .evolve() for um Statevector, .to_operator() converte para operador densidade.
# Para simplificar, `self.current_sv.to_operator().data` deve funcionar para ambos os casos se `current_sv` for `Statevector`.
# Se `current_sv` puder se tornar um `DensityMatrix` objeto após `evolve`, então `current_sv.data` seria usado.
# A Qiskit API para `Statevector.evolve` indica que retorna um `Statevector` ou `DensityMatrix`.
# Então, a lógica em `get_universe_state_representation` para `density_matrix` deveria ser:
# elif format_type == "density_matrix":
#     if isinstance(self.current_sv, Statevector):
#         return self.current_sv.to_operator().data
#     elif isinstance(self.current_sv, DensityMatrix): # Supondo a importação de DensityMatrix
#         return self.current_sv.data
# Por simplicidade, a implementação atual com .to_operator().data é um bom começo.

logger.debug("Módulo universe.py carregado.")
