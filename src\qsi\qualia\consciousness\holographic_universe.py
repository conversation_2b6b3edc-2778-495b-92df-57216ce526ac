"""
QUALIA Holographic Universe - Integração com sistema de consciência quântica.

Este módulo implementa um universo holográfico que simula propagação de informação
através de um campo 2D difusivo com realimentação não-linear. Integra-se com:
- Farsight Engine para insights emergentes
- QUALIAConsciousness para padrões de consciência
- RetrocausalityOperator para influências temporais
- Multimodal data streams para eventos do mercado

Baseado no conceito de que cada ponto do campo contém informação holográfica
do sistema completo, permitindo análise multi-escalar via wavelets.
"""

from __future__ import annotations

import numpy as np
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple, TYPE_CHECKING
from dataclasses import dataclass
from collections import deque
import pywt
from scipy.ndimage import gaussian_filter
import pandas as pd

from ..market.market_data_client import MarketDataClient
from ..event_bus import MarketDataUpdated
from ..memory.holographic_memory import HolographicMemory
from ..utils.logger import get_logger
from ..common.specs import MarketSpec

if TYPE_CHECKING:  # pragma: no cover - hints only
    from .real_data_collectors import RealDataCollector

logger = get_logger(__name__)


@dataclass
class HolographicEvent:
    """Representa um evento no universo holográfico."""

    position: Tuple[int, int]
    time: float
    amplitude: float
    spatial_sigma: float
    temporal_sigma: float
    event_type: str
    source_data: Dict[str, Any]
    confidence: float = 1.0


@dataclass
class HolographicPattern:
    """Padrão detectado no campo holográfico."""

    position: Tuple[int, int]
    strength: float
    dominant_frequency: float
    timeframe: str
    pattern_type: str
    confidence: float
    wavelet_coeffs: np.ndarray
    timestamp: float


@dataclass
class TradingSignal:
    """Sinal de trading gerado pelo universo holográfico."""

    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    strength: float
    timeframe: str
    source: str
    rationale: str
    timestamp: float
    metadata: Dict[str, Any]


class HolographicMarketUniverse:
    """
    Universo holográfico para modelagem de mercado financeiro.

    Simula propagação de informação através de campo 2D onde:
    - Eventos (news, sentiment, etc.) criam pulsos gaussianos
    - Informação se difunde através do espaço-tempo
    - Padrões emergentes são detectados via análise wavelet
    - Sinais de trading são extraídos dos padrões
    """

    def __init__(
        self,
        field_size: Tuple[int, int] = (200, 200),
        time_steps: int = 500,
        diffusion_rate: float = 0.25,
        feedback_strength: float = 0.05,
        event_bus: Optional[Any] = None,
        real_data_collector: Optional["RealDataCollector"] = None,
        min_history_length: int = 20,
        trading_signals_config: Optional[Dict[str, Any]] = None,
        memory: Optional[HolographicMemory] = None,
        quantum_delegate: Optional[Any] = None,
    ):
        """Initialize the holographic market universe.

        Parameters
        ----------
        field_size
            Dimensões do campo holográfico.
        time_steps
            Número máximo de estados mantidos em ``field_history``.
        diffusion_rate
            Taxa de difusão aplicada em cada evolução do campo.
        feedback_strength
            Fator multiplicador para o feedback dos eventos.
        event_bus
            Barramento opcional para publicação de eventos.
        min_history_length
            Comprimento mínimo da história para acionar a análise de padrões.
        trading_signals_config
            Configuração para geração de sinais de trading.
        memory
            Instância de :class:`HolographicMemory` para armazenar o estado do universo.
        """
        self.field_size = field_size
        self.time_steps = time_steps
        self.diffusion_rate = diffusion_rate
        self.feedback_strength = feedback_strength
        self.event_bus = event_bus
        self.real_data_collector = real_data_collector
        self.memory = memory
        self.quantum_delegate = quantum_delegate

        if self.event_bus:
            self.event_bus.subscribe_async(
                "market.data.updated", self._on_market_data_updated
            )

        # Configuração de sinais de trading
        self.trading_signals_config = trading_signals_config or {
            "proximity_threshold": 30,
            "confidence_threshold": 0.6,
            "min_strength": 0.7,
        }

        # Estado do universo
        self.current_field = self._initialize_field()
        self.field_history: List[np.ndarray] = []
        self.events_queue: deque = deque(maxlen=1000)
        self.patterns_history: List[HolographicPattern] = []

        # Parâmetro de janela mínima para análise
        self.min_history_length = min_history_length

        # Mapeamentos espaciais
        self.symbol_positions = self._initialize_symbol_positions()
        self.modality_regions = self._initialize_modality_regions()

        # Análise wavelet
        self.wavelet_scales = np.arange(1, 41)
        self.analysis_points = self._select_analysis_points()

        self.symbol_positions: Dict[str, Tuple[int, int]] = {}
        self.last_evolution_time: Optional[float] = None
        self.last_analysis_time: Optional[float] = None

        self._event_injection_count = 0  # YAA: Contador para throttle de logs

        logger.info(
            f"HolographicMarketUniverse inicializado: {field_size} field, "
            f"{time_steps} steps, diffusion={diffusion_rate}, feedback={feedback_strength}"
        )

    def delegated_statevector(self) -> Optional[Any]:
        """Return current statevector from ``quantum_delegate`` when available."""
        if self.quantum_delegate is None:
            return None
        try:
            if hasattr(self.quantum_delegate, "get_current_statevector"):
                return self.quantum_delegate.get_current_statevector()
        except Exception as exc:  # pragma: no cover - defensive
            logger.error("Erro ao obter statevector via quantum_delegate: %s", exc)
        return None

    def delegated_otoc(
        self,
        time_step: float = 1.0,
        n_qubits: Optional[int] = None,
        target_qubits: Optional[List[int]] = None,
    ) -> Optional[float]:
        """Calculate OTOC via ``quantum_delegate`` when possible."""
        if self.quantum_delegate is None:
            logger.debug("delegated_otoc ignorado: quantum_delegate ausente")
            return None

        try:
            statevector = self.delegated_statevector()
            if hasattr(self.quantum_delegate, "calculate_otoc"):
                return self.quantum_delegate.calculate_otoc(
                    statevector=statevector,
                    time_step=time_step,
                    n_qubits=n_qubits or getattr(self.quantum_delegate, "n_qubits", None),
                    target_qubits=target_qubits,
                )
        except Exception as exc:  # pragma: no cover - defensive
            logger.error("Erro delegando cálculo de OTOC: %s", exc)
        return None

    def _initialize_field(self) -> np.ndarray:
        """Inicializa campo com ruído branco de baixa amplitude."""
        return np.random.normal(0.0, 0.1, size=self.field_size)

    def _initialize_symbol_positions(self) -> Dict[str, Tuple[int, int]]:
        """Mapeia símbolos de trading para posições no campo."""
        symbols = ["BTC", "ETH", "ADA", "SOL", "MATIC", "DOT", "LINK", "UNI"]
        positions = {}

        # Distribui símbolos em círculo no centro do campo
        center_x, center_y = self.field_size[0] // 2, self.field_size[1] // 2
        radius = min(self.field_size) // 4

        for i, symbol in enumerate(symbols):
            angle = 2 * np.pi * i / len(symbols)
            x = int(center_x + radius * np.cos(angle))
            y = int(center_y + radius * np.sin(angle))
            positions[symbol] = (x, y)

        return positions

    def _initialize_modality_regions(self) -> Dict[str, Tuple[int, int]]:
        """Mapeia modalidades de dados para regiões do campo."""
        return {
            "audio": (50, 50),  # Região superior esquerda
            "visual": (150, 50),  # Região superior direita
            "sentiment": (50, 150),  # Região inferior esquerda
            "news": (150, 150),  # Região inferior direita
            "market": (100, 100),  # Centro - dados de mercado
        }

    def _select_analysis_points(self) -> List[Tuple[int, int]]:
        """Seleciona pontos chave para análise temporal."""
        points = []

        # Adiciona posições dos símbolos
        points.extend(self.symbol_positions.values())

        # Adiciona regiões de modalidades
        points.extend(self.modality_regions.values())

        # Adiciona pontos de interseção (onde modalidades se encontram)
        for i in range(0, self.field_size[0], 40):
            for j in range(0, self.field_size[1], 40):
                points.append((i, j))

        return points

    def inject_pulse(
        self,
        position: Tuple[int, int],
        t0: float,
        t: float,
        spatial_sigma: float,
        temporal_sigma: float,
        amplitude: float = 1.0,
    ) -> np.ndarray:
        """
        Injeta pulso gaussiano no campo.

        Args:
            position: Centro espacial (x, y)
            t0: Tempo central do pulso
            t: Tempo atual
            spatial_sigma: Largura espacial
            temporal_sigma: Largura temporal
            amplitude: Amplitude máxima

        Returns:
            Array 2D com o pulso gaussiano
        """
        # Fator temporal
        time_factor = np.exp(-((t - t0) ** 2) / (2 * temporal_sigma**2))

        if time_factor < 1e-6:
            return np.zeros(self.field_size)

        # Fator espacial
        x0, y0 = position
        x = np.arange(self.field_size[0])
        y = np.arange(self.field_size[1])
        X, Y = np.meshgrid(x, y, indexing="ij")

        space_factor = np.exp(
            -((X - x0) ** 2 + (Y - y0) ** 2) / (2 * spatial_sigma**2)
        )

        return amplitude * time_factor * space_factor

    def evolve_field(self, feedback: np.ndarray) -> np.ndarray:
        """
        Evolui campo por um passo temporal.

        Args:
            feedback: Array de realimentação para adicionar ao campo

        Returns:
            Novo estado do campo
        """
        # Difusão gaussiana (aproximação da equação do calor)
        diffused = gaussian_filter(self.current_field, sigma=self.diffusion_rate)

        # Adiciona realimentação
        new_field = diffused + self.feedback_strength * feedback

        return new_field

    async def inject_holographic_event(self, event: HolographicEvent) -> None:
        """Injeta evento holográfico no universo."""
        self.events_queue.append(event)

        # Publica evento no bus se disponível
        if self.event_bus:
            self.event_bus.publish(
                "holographic.event_injected",
                {
                    "event_type": event.event_type,
                    "position": event.position,
                    "amplitude": event.amplitude,
                    "timestamp": event.time,
                },
            )

        # Alterado de DEBUG para TRACE para reduzir verbosidade em produção
        if hasattr(logger, "trace"):
            logger.trace(
                f"Evento holográfico injetado: {event.event_type} em {event.position} "
                f"com amplitude {event.amplitude:.3f}"
            )

        x_indices, y_indices = np.meshgrid(
            np.arange(self.field_size[0]), np.arange(self.field_size[1]), indexing="ij"
        )
        distance_sq = (x_indices - event.position[0]) ** 2 + (
            y_indices - event.position[1]
        ) ** 2
        spatial_component = np.exp(-distance_sq / (2 * event.spatial_sigma**2))
        temporal_component = np.exp(
            -((time.time() - event.time) ** 2) / (2 * event.temporal_sigma**2)
        )
        hologram = event.amplitude * spatial_component * temporal_component
        self.current_field += hologram

        self._event_injection_count += 1
        if self._event_injection_count % 100 == 0:
            logger.info(
                f"🌀 Injetados {self._event_injection_count} eventos holográficos. "
                f"Último evento: {event.event_type} em {event.position} com amp {event.amplitude:.2f}"
            )

    async def _on_market_data_updated(self, payload: MarketDataUpdated) -> None:
        """Handle ``market.data.updated`` events."""
        if self.real_data_collector is None:
            return

        events = self.real_data_collector.convert_to_holographic_events(
            payload.market_data,
            payload.news_events,
            self.field_size,
        )
        for event in events:
            await self.inject_holographic_event(event)

    async def step_evolution(self, current_time: float) -> None:
        """Executa um passo de evolução do universo."""

        # Calcula feedback total de todos os eventos ativos
        total_feedback = np.zeros(self.field_size)

        for event in self.events_queue:
            pulse = self.inject_pulse(
                event.position,
                event.time,
                current_time,
                event.spatial_sigma,
                event.temporal_sigma,
                event.amplitude,
            )
            total_feedback += pulse

        # Evolui campo
        self.current_field = self.evolve_field(total_feedback)

        # Salva na história
        self.field_history.append(self.current_field.copy())

        # Limita tamanho da história
        if len(self.field_history) > self.time_steps:
            self.field_history.pop(0)

    def analyze_holographic_patterns(self) -> List[HolographicPattern]:
        """
        Analisa padrões holográficos usando transformada wavelet.

        Returns:
            Lista de padrões detectados
        """
        if len(self.field_history) < self.min_history_length:
            logger.debug(
                "Histórico insuficiente: %s < %s",
                len(self.field_history),
                self.min_history_length,
            )
            return []

        patterns = []

        for point in self.analysis_points:
            try:
                # Extrai série temporal no ponto
                available_history = min(
                    len(self.field_history), 20
                )  # Usa o que tem disponível
                time_series = np.array(
                    [
                        field[point[0], point[1]]
                        for field in self.field_history[-available_history:]
                    ]
                )

                if len(time_series) < 3:  # REDUZIDO de 10 para 3
                    continue

                # Análise wavelet contínua
                coeffs, freqs = pywt.cwt(time_series, self.wavelet_scales, "morl")

                # Detecta padrões significativos
                pattern_strength = np.max(np.abs(coeffs))

                if pattern_strength > 0.3:  # Threshold de significância (REDUZIDO)
                    # Encontra frequência dominante
                    max_coeff_idx = np.unravel_index(
                        np.argmax(np.abs(coeffs)), coeffs.shape
                    )
                    dominant_scale = self.wavelet_scales[max_coeff_idx[0]]
                    dominant_freq = 1.0 / dominant_scale

                    # Classifica padrão
                    pattern_type = self._classify_pattern(coeffs, time_series)
                    timeframe = self._scale_to_timeframe(dominant_scale)

                    # Calcula confiança
                    confidence = min(pattern_strength / 2.0, 1.0)

                    pattern = HolographicPattern(
                        position=point,
                        strength=pattern_strength,
                        dominant_frequency=dominant_freq,
                        timeframe=timeframe,
                        pattern_type=pattern_type,
                        confidence=confidence,
                        wavelet_coeffs=coeffs,
                        timestamp=time.time(),
                    )

                    patterns.append(pattern)

            except Exception as e:
                logger.warning(f"Erro analisando padrão em {point}: {e}")
                continue

        # Salva padrões na história
        self.patterns_history.extend(patterns)

        # Limita histórico de padrões
        if len(self.patterns_history) > 1000:
            self.patterns_history = self.patterns_history[-1000:]

        logger.info(
            f"🔍 Análise: {len(self.field_history)} campos, {len(self.analysis_points)} pontos, {len(patterns)} padrões"
        )

        return patterns

    def _classify_pattern(self, coeffs: np.ndarray, time_series: np.ndarray) -> str:
        """Classifica tipo de padrão baseado em características wavelet."""

        # Análise de tendência
        recent_trend = (
            np.mean(time_series[-5:]) - np.mean(time_series[-10:-5])
            if len(time_series) >= 10
            else 0
        )

        # Análise de volatilidade
        volatility = (
            np.std(time_series[-10:]) if len(time_series) >= 10 else np.std(time_series)
        )

        # Análise de frequências
        high_freq_energy = np.sum(np.abs(coeffs[:10, :]))  # Escalas pequenas
        low_freq_energy = np.sum(np.abs(coeffs[-10:, :]))  # Escalas grandes

        # Análise da série temporal para tendência mais refinada
        if len(time_series) >= 5:
            slope = np.polyfit(range(len(time_series)), time_series, 1)[0]

            # Classifica baseado na inclinação e força do padrão
            if slope > 0.1 or recent_trend > 0.2:
                return "bullish_momentum"
            elif slope < -0.1 or recent_trend < -0.2:
                return "bearish_momentum"
            elif volatility > 0.5:
                return (
                    "high_volatility_bullish"
                    if slope >= 0
                    else "high_volatility_bearish"
                )
            elif high_freq_energy > low_freq_energy * 1.5:
                return (
                    "rapid_oscillation_bullish"
                    if slope >= 0
                    else "rapid_oscillation_bearish"
                )
            elif low_freq_energy > high_freq_energy * 1.5:
                # Converte long_term_trend em bullish/bearish baseado na direção
                return "long_term_bullish" if slope >= 0 else "long_term_bearish"
            else:
                # Até padrões neutros podem ter direção baseada na força
                pattern_strength = np.max(np.abs(coeffs))
                if pattern_strength > 0.6:
                    return "neutral_bullish" if slope >= 0 else "neutral_bearish"
                else:
                    return "neutral_pattern"
        else:
            return "neutral_pattern"

    def _scale_to_timeframe(self, scale: float) -> str:
        """Converte escala wavelet para timeframe de trading."""
        if scale <= 5:
            return "1m"
        elif scale <= 10:
            return "5m"
        elif scale <= 20:
            return "15m"
        elif scale <= 30:
            return "1h"
        else:
            return "4h"

    def generate_trading_signals(
        self, patterns: List[HolographicPattern]
    ) -> List[TradingSignal]:
        """
        Gera sinais de trading baseados em padrões holográficos.

        Args:
            patterns: Lista de padrões detectados

        Returns:
            Lista de sinais de trading
        """
        logger.info(
            f"🔍 Iniciando geração de sinais com {len(patterns)} padrões detectados"
        )
        signals = []

        for symbol, symbol_position in self.symbol_positions.items():
            # Instrumentação: Log inicial por símbolo
            logger.debug(
                f"  -> Analisando símbolo {symbol} na posição {symbol_position}"
            )

            # Filtro de Distância e Confiança
            nearby_patterns = [
                p
                for p in patterns
                if self._calculate_distance(p.position, symbol_position)
                < self.trading_signals_config.get("proximity_threshold", 30)
                and p.confidence
                > self.trading_signals_config.get("confidence_threshold", 0.6)
            ]

            # Instrumentação: Log após filtros
            logger.debug(
                f"     - Padrões próximos e confiáveis: {len(nearby_patterns)}/{len(patterns)}"
            )

            if not nearby_patterns:
                continue

            # Combinação de Padrões
            combined_strength = np.mean([p.strength for p in nearby_patterns])

            # LOG DE OBSERVAÇÃO
            logger.info(
                f"🔬 OBSERVAÇÃO DE SINAL (Holográfico) para {symbol}: "
                f"Força Combinada = {combined_strength:.4f}"
            )

            # Determina Ação
            bullish_patterns = [
                p for p in nearby_patterns if "bullish" in p.pattern_type
            ]
            bearish_patterns = [
                p for p in nearby_patterns if "bearish" in p.pattern_type
            ]

            action = "HOLD"
            if len(bullish_patterns) > len(bearish_patterns):
                action = "BUY"
            elif len(bearish_patterns) > len(bullish_patterns):
                action = "SELL"

            # Instrumentação: Log da decisão de ação
            logger.debug(
                f"     - Força combinada: {combined_strength:.2f}, Ação: {action}"
            )

            # Filtro de Força e Ação (ADAPTATIVO)
            adaptive_strength_threshold = self._calculate_adaptive_threshold()
            if combined_strength > adaptive_strength_threshold and action != "HOLD":
                rationale = f"{len(bullish_patterns)} padrões bullish vs {len(bearish_patterns)} bearish"
                dominant_timeframe = max(
                    set(p.timeframe for p in nearby_patterns),
                    key=[p.timeframe for p in nearby_patterns].count,
                )

                signal = TradingSignal(
                    symbol=symbol,
                    action=action,
                    confidence=combined_strength,
                    strength=combined_strength,
                    timeframe=dominant_timeframe,
                    source="holographic_universe",
                    rationale=rationale,
                    timestamp=time.time(),
                    metadata={
                        "patterns_count": len(nearby_patterns),
                        "symbol_position": symbol_position,
                        "pattern_types": [p.pattern_type for p in nearby_patterns],
                        "bullish_count": len(bullish_patterns),
                        "bearish_count": len(bearish_patterns),
                    },
                )
                signals.append(signal)
                # Instrumentação: Log do sinal gerado
                logger.info(
                    f"    ✅ SINAL GERADO para {symbol}: {action} com força {combined_strength:.2f}"
                )
            else:
                # Instrumentação: Log da rejeição final
                logger.debug(
                    f"     - Rejeitado: Força ({combined_strength:.2f} <= {adaptive_strength_threshold:.2f}) ou Ação ({action}) não atende aos critérios."
                )

        logger.info(f"✅ Gerados {len(signals)} sinais de trading holográficos")
        return signals

    def _calculate_adaptive_threshold(self) -> float:
        """
        Calcula um threshold de força adaptativo para geração de sinais.
        O threshold é ajustado com base na energia e entropia do campo,
        permitindo que o sistema seja mais ou menos seletivo dependendo
        do estado do mercado (proxy pelo campo holográfico).
        """
        summary = self.get_field_summary()
        if not summary:
            return self.trading_signals_config.get("min_strength", 0.7)

        field_energy = summary.get("field_energy", 0)
        field_entropy = summary.get("field_entropy", 1)

        # Threshold base da configuração
        base_threshold = self.trading_signals_config.get("min_strength", 0.7)

        # Fator de ajuste baseado na energia (proxy para atividade/volatilidade)
        # Energia alta -> mercado ativo -> threshold mais baixo para capturar movimentos
        energy_factor = 1.0 - min(field_energy / 20.0, 0.4)  # Reduz em até 40%

        # Fator de ajuste baseado na entropia (proxy para desordem/incerteza)
        # Entropia alta -> mercado incerto -> threshold mais alto para ser mais seletivo
        entropy_factor = 1.0 + min(field_entropy / 2.0, 0.3)  # Aumenta em até 30%

        adaptive_threshold = base_threshold * energy_factor * entropy_factor

        # Garante que o threshold fique em um range razoável (0.25 a 0.85)
        final_threshold = np.clip(adaptive_threshold, 0.25, 0.85)

        logger.debug(
            f"Threshold adaptativo calculado: {final_threshold:.4f} "
            f"(base: {base_threshold}, energy: {field_energy:.2f}, entropy: {field_entropy:.2f})"
        )

        return float(final_threshold)

    def _calculate_distance(
        self, pos1: Tuple[int, int], pos2: Tuple[int, int]
    ) -> float:
        """Calcula distância euclidiana entre duas posições."""
        return np.sqrt((pos1[0] - pos2[0]) ** 2 + (pos1[1] - pos2[1]) ** 2)

    def get_field_summary(self) -> Dict[str, Any]:
        """Retorna resumo do estado atual do campo."""
        if len(self.field_history) == 0:
            return {}

        current_field = self.field_history[-1]

        return {
            "field_energy": float(np.sum(current_field**2)),
            "field_entropy": float(
                -np.sum(current_field * np.log(np.abs(current_field) + 1e-12))
            ),
            "max_amplitude": float(np.max(current_field)),
            "min_amplitude": float(np.min(current_field)),
            "active_events": len(self.events_queue),
            "patterns_detected": len(self.patterns_history),
            "field_size": self.field_size,
            "time_steps": len(self.field_history),
        }

    async def ingest_real_market_data(
        self,
        market_client: "MarketDataClient",
        symbol: str,
        timeframe: str = "1h",
        limit: int = 100,
    ) -> List[HolographicEvent]:
        """Injeta dados OHLCV reais como eventos holográficos.

        Parameters
        ----------
        market_client
            Instância de :class:`MarketDataClient` para obter os candles.
        symbol
            Símbolo no formato ``"BTC/USDT"``.
        timeframe
            Timeframe dos candles (ex.: ``"1h"``).
        limit
            Quantidade de candles a serem carregados.

        Returns
        -------
        List[HolographicEvent]
            Eventos criados a partir dos candles.
        """

        try:
            df = await market_client.fetch_ohlcv(
                spec=MarketSpec(symbol=symbol, timeframe=timeframe), limit=limit
            )
        except Exception as exc:  # pragma: no cover - delega erro ao chamador
            logger.error("Erro ao buscar OHLCV para %s@%s: %s", symbol, timeframe, exc)
            raise

        if df is None or df.empty:
            logger.warning("OHLCV vazio para %s@%s", symbol, timeframe)
            return []

        base_symbol = symbol.split("/")[0].upper()
        position = self.symbol_positions.get(
            base_symbol, self.modality_regions.get("market", (100, 100))
        )

        events: List[HolographicEvent] = []

        for ts, row in df.iterrows():
            open_p = float(row["open"])
            close_p = float(row["close"])
            change_pct = ((close_p - open_p) / open_p) * 100.0 if open_p else 0.0

            amplitude = min(abs(change_pct) / 2.0, 5.0)
            if change_pct < 0:
                amplitude = -amplitude

            event = HolographicEvent(
                position=position,
                time=(
                    float(getattr(ts, "timestamp", lambda: ts)())
                    if hasattr(ts, "timestamp")
                    else float(ts) / 1000.0
                ),
                amplitude=amplitude,
                spatial_sigma=10.0,
                temporal_sigma=5.0,
                event_type="ohlcv_candle",
                source_data={
                    "symbol": symbol,
                    "open": open_p,
                    "high": float(row["high"]),
                    "low": float(row["low"]),
                    "close": close_p,
                    "volume": float(row.get("volume", 0.0)),
                },
                confidence=0.9,
            )
            await self.inject_holographic_event(event)
            events.append(event)

        logger.info(
            "%s eventos de OHLCV injetados para %s@%s", len(events), symbol, timeframe
        )
        return events

    async def shutdown(self):
        """Limpa recursos do universo holográfico."""
        self.events_queue.clear()
        self.field_history.clear()
        self.patterns_history.clear()
        logger.info("HolographicMarketUniverse shutdown concluído")

    async def initialize(self):
        """Inicializa o universo holográfico."""
        self.current_field = self._initialize_field()
        self.field_history = []
        self.events_queue = deque(maxlen=1000)
        self.patterns_history = []
        self.last_evolution_time = time.time()
        logger.info("🌌 Universo Holográfico inicializado")

    async def _fetch_and_cache_news(
        self, market_client: Any, symbol: str, limit: int = 20
    ) -> pd.DataFrame:
        """Busca e armazena em cache o histórico de mercado para um símbolo."""
        cache_key = f"market_history_{symbol}_news"
        cached_data = self.cache.get(cache_key)
        if cached_data is not None and isinstance(cached_data, pd.DataFrame):
            return cached_data

        df = await market_client.fetch_news(symbol, limit=limit)
        if not df.empty:
            self.cache.set(cache_key, df)
        return df
