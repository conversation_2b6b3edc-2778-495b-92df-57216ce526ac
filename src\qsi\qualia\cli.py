from __future__ import annotations

import argparse
from typing import List, Optional


def build_parser() -> argparse.ArgumentParser:
    """Create the CLI argument parser."""
    parser = argparse.ArgumentParser(
        description="QUALIA Real-Time Trading System CLI",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--env-path",
        type=str,
        default=None,
        help="Caminho para o arquivo .env (sobrescreve QUALIA_ENV_PATH)",
    )
    parser.add_argument(
        "--symbols",
        type=str,
        nargs="+",
        required=True,
        help="Lista de símbolos de trading (ex: BTC/USDT ETH/USDT)",
    )
    parser.add_argument(
        "--timeframes",
        type=str,
        nargs="+",
        required=True,
        help="Lista de timeframes para análise (ex: 1h 5m)",
    )
    parser.add_argument(
        "--capital", type=float, required=True, help="Capital inicial para trading"
    )
    parser.add_argument(
        "--risk_profile",
        type=str,
        required=True,
        choices=["conservative", "moderate", "aggressive", "custom"],
        help="Perfil de risco para as estratégias de trading",
    )
    parser.add_argument(
        "--risk_per_trade_pct",
        type=float,
        default=None,
        help=(
            "Percentual de risco por trade (ex: 0.021 para 2.1 pct)."
            " Usado se --risk_profile for 'custom'."
        ),
    )
    parser.add_argument(
        "--mode",
        type=str,
        required=True,
        choices=["paper_trading", "live"],
        help="Modo de operação: 'paper_trading' ou 'live'",
    )
    parser.add_argument(
        "--duration_seconds",
        type=int,
        default=3600,
        help="Duração da sessão de trading em segundos",
    )
    parser.add_argument(
        "--max_qpm_memory_size",
        type=int,
        default=1000,
        help="Tamanho máximo da memória QPM.",
    )
    parser.add_argument(
        "--log_level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Nível de logging.",
    )
    parser.add_argument(
        "--disable_metacognition",
        action="store_true",
        help="Desabilita o motor de metacognição.",
    )
    parser.add_argument(
        "--base_directory",
        type=str,
        default=None,
        help="Diretório base para arquivos de dados e resultados.",
    )
    parser.add_argument(
        "--strategy_config_path",
        type=str,
        default="config/strategy_parameters.yaml",
        help="Caminho para o arquivo de configuração da estratégia.",
    )
    parser.add_argument(
        "--data_source",
        type=str,
        default="kraken",
        choices=["kraken", "kucoin"],
        help="Fonte dos dados de mercado ('kraken' ou 'kucoin').",
    )
    parser.add_argument(
        "--kraken_api_key",
        type=str,
        default=None,
        help="Chave da API Kraken (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--kraken_secret_key",
        type=str,
        default=None,
        help="Chave secreta da API Kraken (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--kucoin_api_key",
        type=str,
        default=None,
        help="Chave da API Kucoin (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--kucoin_secret_key",
        type=str,
        default=None,
        help="Chave secreta da API Kucoin (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--kucoin_passphrase",
        type=str,
        default=None,
        help="Passphrase da API Kucoin (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--fee_pct",
        type=float,
        default=0.0026,
        help="Sobrescreve a taxa de trading em simulacoes.",
    )
    parser.add_argument(
        "--qast_historical_data_path",
        type=str,
        default=None,
        help="Caminho para o arquivo CSV de dados históricos para QAST.",
    )
    parser.add_argument(
        "--trader_id",
        type=str,
        default=None,
        help="ID do trader (alternativa ao arquivo .env).",
    )
    parser.add_argument(
        "--poll_interval_override",
        type=float,
        default=None,
        help=(
            "Intervalo fixo de polling em segundos. Se omitido, "
            "calcula-se automaticamente baseado nos timeframes."
        ),
    )
    parser.add_argument(
        "--ticker_fetch_timeout",
        type=float,
        default=None,
        help="Tempo limite em segundos para buscar ticker. Sobrepoe TICKER_TIMEOUT.",
    )
    parser.add_argument(
        "--enable_hud",
        type=lambda x: str(x).lower() == "true",
        default=True,
        help="Ativa a HUD holográfica. Use 'False' para desativar.",
    )
    parser.add_argument(
        "--use_webgpu",
        type=lambda x: str(x).lower() == "true",
        default=False,
        help="Tenta utilizar WebGPU no frontend quando suportado.",
    )
    parser.add_argument(
        "--adaptive_poll",
        type=lambda x: str(x).lower() == "true",
        default=True,
        help="Habilita polling adaptativo com back-off exponencial.",
    )
    parser.add_argument(
        "--api_fail_threshold",
        type=int,
        default=None,
        help="Falhas consecutivas para abrir o circuit breaker.",
    )
    parser.add_argument(
        "--api_recovery_timeout",
        type=float,
        default=None,
        help="Tempo em segundos para tentar novamente apos circuito aberto.",
    )
    parser.add_argument(
        "--cb_fail_threshold",
        type=int,
        default=None,
        help=("Sobrescreve 'circuit_breaker_fail_threshold' definido em utils.yaml"),
    )
    parser.add_argument(
        "--call_max_retries",
        type=int,
        default=None,
        help="Sobrescreve 'call_max_retries' definido em utils.yaml",
    )
    parser.add_argument(
        "--skip_preflight",
        action="store_true",
        default=False,
        help=(
            "Ignora a verificacao pre-flight. Quando ativa, a verificacao mantém "
            "a conexao aberta para ser reutilizada na inicializacao."
        ),
    )
    parser.add_argument(
        "--enable_dimensional_trading",
        action="store_true",
        default=False,
        help="Habilitar sistema de trading dimensional multi-exchange"
    )
    parser.add_argument(
        "--enable_arbitrage",
        action="store_true",
        default=False,
        help="Habilitar arbitragem dimensional multi-exchange",
    )
    parser.add_argument(
        "--multi_exchange_config",
        type=str,
        default=None,
        help="Caminho para arquivo de configuração multi-exchange",
    )
    return parser


def parse_args(argv: Optional[List[str]] = None) -> argparse.Namespace:
    """Parse CLI arguments."""
    parser = build_parser()
    return parser.parse_args(argv)
