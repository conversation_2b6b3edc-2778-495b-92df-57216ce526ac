from qsi.validator import QuantumCompatibilityValidator


def setup_validator(monkeypatch, data, checks):
    validator = QuantumCompatibilityValidator(data)
    for name, ret in checks.items():
        monkeypatch.setattr(validator, name, lambda path, val=ret: val)
    return validator


def test_validate_success(monkeypatch):
    data = {"quantum_relevance": 0.8, "code_quality": 0.9}
    checks = {
        "_check_syntax": True,
        "_verify_quantum_principles": True,
        "_test_memory_management": True,
        "_simulate_quantum_behavior": True,
    }
    validator = setup_validator(monkeypatch, data, checks)
    assert validator.validate("x") is True


def test_validate_failure_due_to_check(monkeypatch):
    data = {"quantum_relevance": 0.8, "code_quality": 0.9}
    checks = {
        "_check_syntax": False,
        "_verify_quantum_principles": True,
        "_test_memory_management": True,
        "_simulate_quantum_behavior": True,
    }
    validator = setup_validator(monkeypatch, data, checks)
    assert validator.validate("x") is False


def test_validate_failure_due_to_metrics(monkeypatch):
    data = {"quantum_relevance": 0.6, "code_quality": 0.7}
    checks = {
        "_check_syntax": True,
        "_verify_quantum_principles": True,
        "_test_memory_management": True,
        "_simulate_quantum_behavior": True,
    }
    validator = setup_validator(monkeypatch, data, checks)
    assert validator.validate("x") is False
