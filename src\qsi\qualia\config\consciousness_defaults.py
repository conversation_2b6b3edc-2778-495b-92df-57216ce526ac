"""Default configuration for ``QUALIAConsciousness`` components.

Values come from ``config/consciousness_defaults.yaml`` unless the
``QUALIA_CONSCIOUSNESS_DEFAULTS`` environment variable specifies a
different path.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("consciousness_defaults.yaml")


def load_consciousness_defaults() -> Dict[str, Any]:
    """Load ``QUALIAConsciousness`` defaults from YAML.

    The path can be overridden using the ``QUALIA_CONSCIOUSNESS_DEFAULTS``
    environment variable.

    Returns
    -------
    Dict[str, Any]
        Mapping of configuration keys to values. Returns an empty mapping on
        error.
    """

    return load_yaml_config(
        "QUALIA_CONSCIOUSNESS_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_consciousness_defaults"]
