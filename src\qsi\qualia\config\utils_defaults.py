from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("utils.yaml")


def load_utils_defaults() -> Dict[str, Any]:
    """Load network resilience parameters from a YAML configuration file.

    The ``QUALIA_UTILS_CONFIG`` environment variable can override the bundled
    ``config/utils.yaml`` file.

    Returns
    -------
    Dict[str, Any]
        Mapping of parameter names to values. Returns an empty mapping on error.
    """
    return load_yaml_config(
        "QUALIA_UTILS_CONFIG",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_utils_defaults"]
