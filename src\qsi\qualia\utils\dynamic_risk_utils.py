from __future__ import annotations

from dataclasses import dataclass, fields
from typing import Any, Dict, List, Tuple, Union
import os
import numpy as np

from ..utils.logger import get_logger
from ..indicators import atr

try:  # During certain tests the market package may be stubbed
    from ..market.volatility_utils import calculate_implied_volatility
except Exception:  # pragma: no cover - fallback for tests with stubs

    def calculate_implied_volatility(*args, **kwargs) -> float:
        return 0.2


logger = get_logger(__name__)


@dataclass
class DynamicRiskParameters:
    """Parâmetros para controle de risco dinâmico."""

    atr_period: int = 14
    atr_multiplier_base: float = 0.8
    atr_multiplier_volatility_factor: float = 0.5
    take_profit_base_ratio: float = 2.5
    take_profit_volatility_adjustment: float = 0.3
    volatility_lookback_periods: int = 20
    volatility_threshold_low: float = 0.08
    volatility_threshold_high: float = 0.25
    recalibration_frequency_minutes: int = 5
    min_adjustment_threshold: float = 0.05
    max_adjustment_factor: float = 2.0
    regime_calm_multiplier: float = 0.8
    regime_volatile_multiplier: float = 1.3
    regime_normal_multiplier: float = 1.0


def validate_dynamic_risk_parameters(params: Dict[str, Any]) -> None:
    """Valida dicionário de parâmetros dinamicos."""
    valid_fields = {f.name for f in fields(DynamicRiskParameters)}
    for key, value in params.items():
        if key not in valid_fields:
            raise ValueError(f"Parâmetro desconhecido: {key}")
        if isinstance(value, (int, float)) and value < 0:
            raise ValueError(f"{key} deve ser não-negativo")


def load_risk_parameters(
    profile: str,
    config: Dict[str, Any],
    defaults: Dict[str, Any],
) -> DynamicRiskParameters:
    """Carrega parâmetros dinâmicos conforme perfil e configuração."""
    try:
        base_params = DynamicRiskParameters(**defaults)
    except TypeError:
        base_params = DynamicRiskParameters()
        for key, value in defaults.items():
            if hasattr(base_params, key):
                setattr(base_params, key, value)

    if profile == "conservative":
        base_params.atr_multiplier_base = 1.5
        base_params.take_profit_base_ratio = 2.5
        base_params.regime_volatile_multiplier = 1.5
        base_params.max_adjustment_factor = 1.5
    elif profile == "aggressive":
        base_params.atr_multiplier_base = 2.5
        base_params.take_profit_base_ratio = 1.5
        base_params.regime_calm_multiplier = 0.6
        base_params.max_adjustment_factor = 2.5

    overrides = config.get("dynamic_risk_parameters", {})
    validate_dynamic_risk_parameters(overrides)
    for key, value in overrides.items():
        if hasattr(base_params, key):
            setattr(base_params, key, value)

    for field in fields(DynamicRiskParameters):
        env_name = f"QUALIA_DYN_RISK_{field.name.upper()}"
        env_value = os.getenv(env_name)
        if env_value is not None:
            try:
                cast = int(env_value) if field.type is int else float(env_value)
            except ValueError:
                logger.error("Valor inválido para %s: %s", env_name, env_value)
                continue
            setattr(base_params, field.name, cast)
    return base_params


def calculate_atr_levels(
    high_data: Union[List[float], np.ndarray],
    low_data: Union[List[float], np.ndarray],
    close_data: Union[List[float], np.ndarray],
    current_price: float,
    params: DynamicRiskParameters,
    *,
    use_gpu: bool | None = None,
) -> Tuple[float, float, float]:
    """Calcula ATR e distâncias de SL/TP."""
    atr_values = atr(
        high=high_data, low=low_data, close=close_data, period=params.atr_period
    )
    current_atr = (
        atr_values[-1] if not np.isnan(atr_values[-1]) else np.nanmean(atr_values[-5:])
    )
    if np.isnan(current_atr) or current_atr <= 0:
        current_atr = current_price * 0.01
        logger.warning("ATR inválido, usando fallback: %s", current_atr)

    implied_vol = calculate_implied_volatility(close_data, use_gpu=use_gpu)
    vol_adjustment = 1.0
    if implied_vol < params.volatility_threshold_low:
        vol_adjustment = 0.8
    elif implied_vol > params.volatility_threshold_high:
        vol_adjustment = 1.3

    effective_multiplier = (
        params.atr_multiplier_base
        * vol_adjustment
        * (1 + implied_vol * params.atr_multiplier_volatility_factor)
    )
    effective_multiplier = np.clip(
        effective_multiplier, 0.5, params.max_adjustment_factor * 2
    )
    stop_distance = current_atr * effective_multiplier
    take_profit_distance = stop_distance * params.take_profit_base_ratio
    tp_vol_adjustment = 1 + (implied_vol * params.take_profit_volatility_adjustment)
    take_profit_distance *= tp_vol_adjustment
    return float(current_atr), float(stop_distance), float(take_profit_distance)


__all__ = [
    "DynamicRiskParameters",
    "validate_dynamic_risk_parameters",
    "load_risk_parameters",
    "calculate_atr_levels",
]
