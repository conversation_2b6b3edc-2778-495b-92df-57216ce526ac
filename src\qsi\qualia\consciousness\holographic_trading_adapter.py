"""
Holographic Trading Adapter - Integração com QUALIARealTimeTrader

Este módulo cria uma ponte entre o sistema holográfico enhanced e o
QUALIARealTimeTrader existente, aproveitando toda a infraestrutura
madura de execução, risk management e gestão de posições.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from ..core.qualia_execution_interface import QUALIAExecutionInterface
from ..core.qast_oracle_decision_engine import OracleDecision
from ..market.decision_context import ScalpingDecision
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class HolographicSignal:
    """Sinal de trading gerado pelo sistema holográfico."""

    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    timeframe: str
    timestamp: float

    # Dados enriquecidos
    rsi: Optional[float] = None
    volume_ratio: Optional[float] = None
    volatility: Optional[float] = None
    price_change_pct: Optional[float] = None

    # Estados quânticos
    quantum_rsi_encoded: bool = False
    quantum_volume_encoded: bool = False

    # Contexto holográfico
    pattern_strength: Optional[float] = None
    field_energy: Optional[float] = None
    entropy: Optional[float] = None


@dataclass
class HolographicTradeResult:
    """Resultado de um trade executado via sinais holográficos."""

    signal: HolographicSignal
    trade_executed: bool
    execution_price: Optional[float] = None
    position_size: Optional[float] = None
    order_id: Optional[str] = None
    rejection_reason: Optional[str] = None
    execution_timestamp: Optional[float] = None


class HolographicTradingAdapter:
    """
    Adapter que integra sinais holográficos ao QUALIAExecutionInterface.

    Responsabilidades:
    - Converter sinais holográficos para formato OracleDecision
    - Integrar com sistema de execução existente
    - Manter rastreabilidade entre sinais e trades
    - Coletar métricas específicas do sistema holográfico
    """

    def __init__(
        self,
        trader: Optional[QUALIAExecutionInterface] = None,
        min_confidence: float = 0.6,
        max_concurrent_positions: int = 3,
        enable_holographic_risk_override: bool = True,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        Inicializa o adapter de trading holográfico.

        Args:
            trader: Instância do QUALIAExecutionInterface
            min_confidence: Confidence mínima para sinais (pode ser sobrescrito por config)
            max_concurrent_positions: Máximo de posições simultâneas
            enable_holographic_risk_override: Se deve aplicar validações de risco holográficas
            config: Configuração completa do sistema para obter thresholds dinâmicos
        """
        self.trader = trader

        # Obtém configurações holográficas específicas ou usa defaults
        holographic_config = {}
        if config:
            holographic_config = config.get("holographic_trading", {})

        # Thresholds configuráveis (prioriza config, depois parâmetros, depois defaults)
        self.min_confidence = holographic_config.get("min_confidence", min_confidence)
        self.max_concurrent_positions = holographic_config.get(
            "max_concurrent_positions", max_concurrent_positions
        )
        self.enable_holographic_risk_override = holographic_config.get(
            "enable_holographic_risk_override", enable_holographic_risk_override
        )
        self.volume_threshold = holographic_config.get("volume_threshold", 0.5)

        # Configuração geral para thresholds de aprovação
        signal_approval_config = config.get("signal_approval", {}) if config else {}
        self.aggressive_mode = signal_approval_config.get(
            "enable_aggressive_mode", False
        )

        # Log da configuração aplicada
        logger.info("🌀 HolographicTradingAdapter configurado:")
        logger.info(f"   min_confidence: {self.min_confidence}")
        logger.info(f"   volume_threshold: {self.volume_threshold}")
        logger.info(f"   aggressive_mode: {self.aggressive_mode}")
        logger.info(f"   risk_override: {self.enable_holographic_risk_override}")

        # Métricas e estado
        self.signals_processed = 0
        self.trades_executed = 0
        self.signals_rejected = 0
        self.holographic_positions: Dict[str, Dict[str, Any]] = {}

        # Métricas holográficas detalhadas
        self.holographic_metrics = {
            "total_signals": 0,
            "executed_trades": 0,
            "avg_confidence": 0.0,
            "quantum_encoded_trades": 0,
            "pattern_strength_avg": 0.0,
            "success_rate": 0.0,
        }

        logger.info("HolographicTradingAdapter inicializado")

    async def process_holographic_signal(
        self, signal: HolographicSignal
    ) -> HolographicTradeResult:
        """
        Processa um sinal holográfico e executa trade se aprovado.

        Args:
            signal: Sinal gerado pelo sistema holográfico

        Returns:
            Resultado da execução do trade
        """
        self.signals_processed += 1
        self.holographic_metrics["total_signals"] += 1

        logger.info(
            f"🌀 Processando sinal holográfico: {signal.symbol} {signal.action} "
            f"confidence={signal.confidence:.2f} rsi={getattr(signal, 'rsi', None)} "
            f"quantum_rsi={getattr(signal, 'quantum_rsi_encoded', False)}"
        )

        try:
            # 1. Validações preliminares
            validation_result = await self._validate_holographic_signal(signal)
            if not validation_result["approved"]:
                self.signals_rejected += 1
                return HolographicTradeResult(
                    signal=signal,
                    trade_executed=False,
                    rejection_reason=validation_result["reason"],
                )

            # 2. Converte para formato OracleDecision
            decision = OracleDecision(
                symbol=signal.symbol,
                action=signal.action,
                confidence=signal.confidence,
                size=0.0,  # Será calculado pelo risk manager
                stop_loss=None,  # Será calculado
                take_profit=None,  # Será calculado
                reasoning=[
                    f"Holographic signal: pattern_strength={getattr(signal, 'pattern_strength', 0.0) or 0.0:.2f}",
                    (
                        f"Field energy: {field_energy:.2f}"
                        if (field_energy := getattr(signal, "field_energy", None))
                        is not None
                        else "Field energy: N/A"
                    ),
                    (
                        f"RSI: {getattr(signal, 'rsi', 0.0):.1f}"
                        if getattr(signal, "rsi", None) is not None
                        else "RSI: N/A"
                    ),
                    "Quantum encoded: {}".format(
                        "Yes" if getattr(signal, "quantum_rsi_encoded", False) else "No"
                    ),
                ],
                quantum_signature=None,
                holographic_patterns=(
                    [
                        {
                            "strength": getattr(signal, "pattern_strength", None),
                            "confidence": signal.confidence,
                            "field_energy": getattr(signal, "field_energy", None),
                            "entropy": getattr(signal, "entropy", None),
                        }
                    ]
                    if getattr(signal, "pattern_strength", None) is not None
                    else []
                ),
                metacognitive_context={
                    "source": "holographic_adapter",
                    "quantum_enhanced": getattr(signal, "quantum_rsi_encoded", False)
                    or getattr(signal, "quantum_volume_encoded", False),
                },
                risk_assessment={},
                risk_approved=True,
                timestamp=signal.timestamp,
            )

            # 3. Obtém preço atual
            current_price = self._get_current_price(signal.symbol)
            if current_price is None:
                return HolographicTradeResult(
                    signal=signal,
                    trade_executed=False,
                    rejection_reason="Preço atual não disponível",
                )

            # 4. Executa através do QUALIAExecutionInterface
            execution_result = await self._execute_via_trader(
                signal, decision, current_price
            )

            # 5. Atualiza métricas
            self._update_holographic_metrics(signal, execution_result)

            return execution_result

        except Exception as e:
            logger.error(f"Erro processando sinal holográfico: {e}", exc_info=True)
            return HolographicTradeResult(
                signal=signal,
                trade_executed=False,
                rejection_reason=f"Erro interno: {str(e)}",
            )

    async def _validate_holographic_signal(
        self, signal: HolographicSignal
    ) -> Dict[str, Any]:
        """Validações específicas para sinais holográficos."""

        # 1. Confidence mínima
        if signal.confidence < self.min_confidence:
            return {
                "approved": False,
                "reason": f"Confidence {signal.confidence:.2f} < {self.min_confidence}",
            }

        # 2. Ação válida
        if signal.action not in ["BUY", "SELL"]:
            return {"approved": False, "reason": f"Ação inválida: {signal.action}"}

        # 3. Limite de posições concorrentes
        open_positions = len(
            [
                p
                for p in self.holographic_positions.values()
                if p.get("status") == "open"
            ]
        )
        if open_positions >= self.max_concurrent_positions:
            return {
                "approved": False,
                "reason": f"Máximo de {self.max_concurrent_positions} posições atingido",
            }

        # 4. Validações específicas do holográfico
        if self.enable_holographic_risk_override:
            holographic_risk = self._assess_holographic_risk(signal)
            if not holographic_risk["approved"]:
                return holographic_risk

        # 5. Validação do trader base
        trader_validation = await self._validate_with_base_trader(signal)
        if not trader_validation["approved"]:
            return trader_validation

        return {"approved": True, "reason": "Todas validações passaram"}

    def _assess_holographic_risk(self, signal: HolographicSignal) -> Dict[str, Any]:
        """Avaliação de risco específica para sinais holográficos."""

        # Em modo agressivo, aplica validações mais permissivas
        if self.aggressive_mode:
            logger.debug(
                f"🔥 Avaliação de risco holográfico em modo agressivo para {signal.symbol}"
            )

        # RSI extremo (mais permissivo em modo agressivo)
        rsi_val = getattr(signal, "rsi", None)
        if rsi_val is not None:
            rsi_buy_limit = 85 if self.aggressive_mode else 75
            rsi_sell_limit = 15 if self.aggressive_mode else 25

            if signal.action == "BUY" and rsi_val > rsi_buy_limit:
                return {
                    "approved": False,
                    "reason": f"RSI muito alto para BUY: {rsi_val:.1f} > {rsi_buy_limit}",
                }
            elif signal.action == "SELL" and rsi_val < rsi_sell_limit:
                return {
                    "approved": False,
                    "reason": f"RSI muito baixo para SELL: {rsi_val:.1f} < {rsi_sell_limit}",
                }

        # Volatilidade excessiva (mais permissivo em modo agressivo)
        volatility = getattr(signal, "volatility", None)
        volatility_limit = 15.0 if self.aggressive_mode else 8.0
        if volatility is not None and volatility > volatility_limit:
            return {
                "approved": False,
                "reason": f"Volatilidade alta: {volatility:.2f}% > {volatility_limit}%",
            }

        # Volume insuficiente (usando threshold configurável)
        volume_ratio = getattr(signal, "volume_ratio", None)
        if volume_ratio is not None and volume_ratio < self.volume_threshold:
            return {
                "approved": False,
                "reason": f"Volume baixo: {volume_ratio:.2f}x < {self.volume_threshold}x",
            }

        # Pattern strength insuficiente (mais permissivo em modo agressivo)
        pattern_strength = getattr(signal, "pattern_strength", None)
        pattern_strength_limit = 0.2 if self.aggressive_mode else 0.4
        if pattern_strength is not None and pattern_strength < pattern_strength_limit:
            return {
                "approved": False,
                "reason": f"Pattern strength baixa: {pattern_strength:.2f} < {pattern_strength_limit}",
            }

        return {
            "approved": True,
            "reason": f"Risk assessment holográfico {'agressivo' if self.aggressive_mode else 'padrão'} OK",
        }

    async def _validate_with_base_trader(
        self, signal: HolographicSignal
    ) -> Dict[str, Any]:
        """Valida com o sistema de risk management do trader base."""

        try:
            # Usa o risk manager do trader existente
            if hasattr(self.trader, "risk_manager") and self.trader.risk_manager:
                current_price = self._get_current_price(signal.symbol)
                if current_price is None:
                    return {
                        "approved": False,
                        "reason": "Preço não disponível para validação",
                    }

                # Calcula stop loss baseado na volatilidade
                stop_loss_pct = 0.02  # 2% padrão
                volatility = getattr(signal, "volatility", None)
                if volatility:
                    stop_loss_pct = min(volatility / 100 * 1.5, 0.05)  # Max 5%

                stop_loss_price = (
                    current_price * (1 - stop_loss_pct)
                    if signal.action == "BUY"
                    else current_price * (1 + stop_loss_pct)
                )

                risk_result = self.trader.risk_manager.calculate_position_size(
                    symbol=signal.symbol,
                    current_price=current_price,
                    stop_loss_price=stop_loss_price,
                    confidence=signal.confidence,
                    volatility=volatility,
                )

                if not risk_result.get("position_allowed", False):
                    return {
                        "approved": False,
                        "reason": f"Risk manager: {risk_result.get('reason', 'unknown')}",
                    }

            return {"approved": True, "reason": "Base trader validation OK"}

        except Exception as e:
            logger.error(f"Erro na validação com base trader: {e}")
            return {"approved": False, "reason": f"Erro na validação: {str(e)}"}

    async def _execute_via_trader(
        self,
        signal: HolographicSignal,
        decision: OracleDecision,
        current_price: float,
    ) -> HolographicTradeResult:
        """Executa trade através do QUALIAExecutionInterface."""

        try:
            # Chama o método de processamento de decisão do trader
            await self.trader._process_trading_decision(
                symbol=signal.symbol,
                decision=decision,
                current_price=current_price,
                current_q_signature_packet_arg=None,  # Não temos packet quântico
                market_snapshot_at_decision=None,
                decision_context_details={
                    "source": "holographic",
                    "signal_timestamp": signal.timestamp,
                    "holographic_data": {
                        "rsi": getattr(signal, "rsi", None),
                        "volume_ratio": getattr(signal, "volume_ratio", None),
                        "volatility": getattr(signal, "volatility", None),
                        "quantum_encoded": getattr(
                            signal, "quantum_rsi_encoded", False
                        ),
                        "pattern_strength": getattr(signal, "pattern_strength", None),
                        "field_energy": getattr(signal, "field_energy", None),
                    },
                },
            )

            self.trades_executed += 1
            self.holographic_metrics["executed_trades"] += 1

            # Registra posição holográfica
            position_id = f"{signal.symbol}_{int(time.time())}"
            self.holographic_positions[position_id] = {
                "signal": signal,
                "decision": decision,
                "execution_price": current_price,
                "timestamp": time.time(),
                "status": "open",
            }

            logger.info(
                f"✅ Trade holográfico executado: {signal.symbol} {signal.action} "
                f"@ {current_price:.4f} confidence={signal.confidence:.2f}"
            )

            return HolographicTradeResult(
                signal=signal,
                trade_executed=True,
                execution_price=current_price,
                execution_timestamp=time.time(),
                order_id=position_id,
            )

        except Exception as e:
            logger.error(f"Erro executando trade via trader: {e}", exc_info=True)
            return HolographicTradeResult(
                signal=signal,
                trade_executed=False,
                rejection_reason=f"Erro na execução: {str(e)}",
            )

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Obtém preço atual do símbolo."""
        try:
            return self.trader._get_current_price(symbol)
        except Exception as e:
            logger.error(f"Erro obtendo preço para {symbol}: {e}")
            return None

    def _update_holographic_metrics(
        self, signal: HolographicSignal, result: HolographicTradeResult
    ):
        """Atualiza métricas específicas do sistema holográfico."""

        # Confidence média
        total_signals = self.holographic_metrics["total_signals"]
        current_avg = self.holographic_metrics["avg_confidence"]
        self.holographic_metrics["avg_confidence"] = (
            current_avg * (total_signals - 1) + signal.confidence
        ) / total_signals

        # Trades com quantum encoding
        if getattr(signal, "quantum_rsi_encoded", False) or getattr(
            signal, "quantum_volume_encoded", False
        ):
            self.holographic_metrics["quantum_encoded_trades"] += 1

        # Pattern strength média
        pattern_strength = getattr(signal, "pattern_strength", None)
        if pattern_strength is not None:
            current_pattern_avg = self.holographic_metrics["pattern_strength_avg"]
            self.holographic_metrics["pattern_strength_avg"] = (
                current_pattern_avg * (total_signals - 1) + pattern_strength
            ) / total_signals

        # Success rate (será calculado posteriormente com base nos resultados)
        if result.trade_executed:
            executed = self.holographic_metrics["executed_trades"]
            self.holographic_metrics["success_rate"] = executed / total_signals * 100

    async def convert_signals_to_decisions(
        self, signals: List[HolographicSignal]
    ) -> List[OracleDecision]:
        """Converte sinais holográficos em decisões do Oracle."""

        decisions = []

        for signal in signals:
            try:
                # Valida sinal
                validation = await self._validate_holographic_signal(signal)
                if not validation["approved"]:
                    self.signals_rejected += 1
                    logger.debug(f"Sinal rejeitado: {validation['reason']}")
                    continue

                # Converte para OracleDecision
                decision = OracleDecision(
                    symbol=signal.symbol,
                    action=signal.action,
                    confidence=signal.confidence,
                    size=0.0,  # Será calculado pelo risk manager
                    stop_loss=None,  # Será calculado
                    take_profit=None,  # Será calculado
                    reasoning=[
                        f"Holographic signal: pattern_strength={getattr(signal, 'pattern_strength', 0.0) or 0.0:.2f}",
                        (
                            f"Field energy: {field_e:.2f}"
                            if (field_e := getattr(signal, "field_energy", None))
                            is not None
                            else "Field energy: N/A"
                        ),
                        (
                            f"RSI: {getattr(signal, 'rsi', 0.0):.1f}"
                            if getattr(signal, "rsi", None) is not None
                            else "RSI: N/A"
                        ),
                        "Quantum encoded: {}".format(
                            "Yes"
                            if getattr(signal, "quantum_rsi_encoded", False)
                            else "No"
                        ),
                    ],
                    quantum_signature=None,
                    holographic_patterns=(
                        [
                            {
                                "strength": getattr(signal, "pattern_strength", None),
                                "confidence": signal.confidence,
                                "field_energy": getattr(signal, "field_energy", None),
                                "entropy": getattr(signal, "entropy", None),
                            }
                        ]
                        if getattr(signal, "pattern_strength", None) is not None
                        else []
                    ),
                    metacognitive_context={
                        "source": "holographic_adapter",
                        "quantum_enhanced": getattr(
                            signal, "quantum_rsi_encoded", False
                        )
                        or getattr(signal, "quantum_volume_encoded", False),
                    },
                    risk_assessment={},
                    risk_approved=True,
                    timestamp=signal.timestamp,
                )

                decisions.append(decision)

            except Exception as exc:
                logger.error(f"Erro convertendo sinal holográfico: {exc}")

        return decisions

    def get_holographic_performance_report(self) -> Dict[str, Any]:
        """Gera relatório de performance específico do sistema holográfico."""

        open_positions = len(
            [
                p
                for p in self.holographic_positions.values()
                if p.get("status") == "open"
            ]
        )
        closed_positions = len(
            [
                p
                for p in self.holographic_positions.values()
                if p.get("status") == "closed"
            ]
        )

        return {
            "holographic_metrics": self.holographic_metrics.copy(),
            "signals_processed": self.signals_processed,
            "trades_executed": self.trades_executed,
            "signals_rejected": self.signals_rejected,
            "execution_rate": (self.trades_executed / max(self.signals_processed, 1))
            * 100,
            "positions": {
                "open": open_positions,
                "closed": closed_positions,
                "total": len(self.holographic_positions),
            },
            "quantum_encoding_rate": (
                self.holographic_metrics["quantum_encoded_trades"]
                / max(self.holographic_metrics["total_signals"], 1)
            )
            * 100,
        }

    async def close_all_holographic_positions(self, reason: str = "System shutdown"):
        """Fecha todas as posições abertas pelo sistema holográfico."""

        open_positions = [
            (pid, p)
            for pid, p in self.holographic_positions.items()
            if p.get("status") == "open"
        ]

        for position_id, position_data in open_positions:
            try:
                signal = position_data["signal"]
                current_price = self._get_current_price(signal.symbol)

                if current_price:
                    # Fecha posição através do trader
                    await self.trader._close_position(
                        symbol=signal.symbol,
                        order_id=position_id,
                        trigger_price=current_price,
                        reason=reason,
                    )

                    # Atualiza status
                    self.holographic_positions[position_id]["status"] = "closed"

                    logger.info(f"Posição holográfica fechada: {position_id}")

            except Exception as e:
                logger.error(f"Erro fechando posição holográfica {position_id}: {e}")


# Factory function
def create_holographic_trading_adapter(
    trader: Optional[QUALIAExecutionInterface] = None,
    config: Optional[Dict[str, Any]] = None,
) -> HolographicTradingAdapter:
    """
    Cria instância do adapter de trading holográfico.

    Args:
        trader: Instância do QUALIAExecutionInterface
        config: Configuração opcional

    Returns:
        Adapter configurado
    """
    config = config or {}

    return HolographicTradingAdapter(
        trader=trader,
        min_confidence=config.get("min_confidence", 0.6),
        max_concurrent_positions=config.get("max_concurrent_positions", 3),
        enable_holographic_risk_override=config.get(
            "enable_holographic_risk_override", True
        ),
        config=config,
    )
