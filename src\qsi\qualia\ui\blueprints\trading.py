"""Wrapper blueprint importing trading routes."""

from __future__ import annotations

from flask import Blueprint

from ...ui.trading import routes as trading_routes

trading_bp: Blueprint = trading_routes.trading_bp


@trading_bp.route("/api/trading/execute_and_visualize", methods=["GET", "POST"])
def execute_trading_and_visualize():  # pragma: no cover - heavy integration
    """Execute a trading cycle and visualize the resulting quantum circuit."""
    from datetime import datetime, timezone

    from flask import jsonify, request

    from ...utils.logger import get_logger
    from ...ui.initialize import get_qualia_state, initialize_qualia_system

    logger = get_logger(__name__)
    qualia_state = get_qualia_state()
    if qualia_state.universe is None:
        initialize_qualia_system(qualia_state)

    if request.method == "GET":
        output_type = request.args.get("output_type", default="text")
        symbol = request.args.get("symbol", default="BTC/USDT")
        timeframe = request.args.get("timeframe", default="1h")
    else:
        data = request.json or {}
        output_type = data.get("output_type", "text")
        symbol = data.get("symbol", "BTC/USDT")
        timeframe = data.get("timeframe", "1h")

    try:
        logger.info("Executando ciclo de trading para %s em %s", symbol, timeframe)
        qualia_state.universe.run(steps=3)
        analysis_result = {
            "symbol": symbol,
            "timeframe": timeframe,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": {"quantum_entropy": 0.92, "quantum_coherence": 0.003},
            "message": "Circuito quântico gerado com sucesso para análise de dados de mercado.",
        }
        if getattr(qualia_state.universe, "metrics_history", None):
            try:
                metrics = qualia_state.universe.metrics_history
                if metrics.get("quantum_entropy"):
                    analysis_result["metrics"]["quantum_entropy"] = metrics[
                        "quantum_entropy"
                    ][-1]
                if metrics.get("coherence"):
                    analysis_result["metrics"]["quantum_coherence"] = metrics[
                        "coherence"
                    ][-1]
            except Exception as metrics_err:  # pragma: no cover - optional
                logger.warning(
                    "Não foi possível acessar métricas detalhadas: %s", metrics_err
                )
        logger.info("Obtendo visualização do circuito")
        if output_type in ["text", "latex", "mpl", "bloch"]:
            circuit_viz = qualia_state.universe.visualize_quantum_circuit(
                output_type=output_type
            )
            if circuit_viz is None:
                logger.info("Tentando executar passos adicionais para gerar o circuito")
                qualia_state.universe.run(steps=3)
                circuit_viz = qualia_state.universe.visualize_quantum_circuit(
                    output_type=output_type
                )
            return jsonify(
                {
                    "success": True,
                    "circuit_visualization": (
                        str(circuit_viz)
                        if circuit_viz is not None
                        else "Circuito não disponível"
                    ),
                    "analysis_result": analysis_result,
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            )
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Tipo de visualização não suportado: {output_type}",
                    "supported_types": ["text", "latex", "mpl", "bloch"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            ),
            400,
        )
    except Exception as exc:  # pragma: no cover - optional
        logger.error("Erro ao executar trading e visualizar circuito: %s", exc)
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Erro: {str(exc)}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            ),
            500,
        )
