from __future__ import annotations

"""Backtesting utilities for :mod:`nova_estrategia_qualia`."""

from typing import Any, Dict

import numpy as np

from ...utils.logger import get_logger

logger = get_logger(__name__)


def simulate_portfolio(
    strategy: Any,
    positions_s1: np.ndarray,
    positions_s2: np.ndarray,
    positions_s3: np.ndarray,
    market_returns: np.ndarray,
    initial_capital: float,
) -> Dict[str, Any]:
    """Simulate portfolio performance from generated positions.

    Parameters
    ----------
    strategy
        Strategy instance providing weights for each sub-strategy.
    positions_s1
        Array of positions from sub-strategy ``s1``.
    positions_s2
        Array of positions from sub-strategy ``s2``.
    positions_s3
        Array of positions from sub-strategy ``s3``.
    market_returns
        Market returns for each tick expressed as decimal values.
    initial_capital
        Starting capital for the simulation.

    Returns
    -------
    Dict[str, Any]
        Dictionary containing total percentage PnL, final capital and
        combined returns per tick.
    """

    weights = strategy.weights
    w1 = weights.get("s1", 0.0)
    w2 = weights.get("s2", 0.0)
    w3 = weights.get("s3", 0.0)

    positions = w1 * positions_s1 + w2 * positions_s2 + w3 * positions_s3

    combined_returns = np.zeros_like(market_returns)
    combined_returns[1:] = positions[:-1] * market_returns[1:]

    cumulative = initial_capital * np.cumprod(1 + combined_returns)
    capital = float(cumulative[-1])

    pnl_pct = (capital - initial_capital) / initial_capital * 100
    return {
        "total_pnl_pct": pnl_pct,
        "final_capital": capital,
        "combined_returns": combined_returns.tolist(),
    }
