from __future__ import annotations

import numpy as np
from qiskit import QuantumCircuit
from typing import Sequence

from ..utils.logger import get_logger

logger = get_logger(__name__)


try:
    from toy_black_hole import advanced_scrambler as external_scrambler
except Exception:  # pragma: no cover - optional fallback
    external_scrambler = None


def advanced_scrambler(
    qc: QuantumCircuit, regs: Sequence, depth: int = 3, style: str = "linear"
) -> None:
    """Aplica camadas de scrambling com rota\xc3\xa7\xc3\xb5es aleat\xc3\xb3rias e CX.

    Se ``src.toy_black_hole`` estiver dispon\xc3\xadvel, delega ao scrambler externo.
    Caso contr\xc3\xa1rio, utiliza uma implementa\xc3\xa7\xc3\xa3o simplificada.
    """
    if external_scrambler is not None:
        return external_scrambler(qc, regs, depth=depth, style=style)

    n = len(regs)
    num_layers = np.random.randint(3, 5)
    for _ in range(num_layers):
        for q in regs:
            qc.ry(np.random.uniform(0, 2 * np.pi), q)
            qc.rz(np.random.uniform(0, 2 * np.pi), q)

        if style == "full":
            for i in range(n):
                for j in range(i + 1, n):
                    qc.cx(regs[i], regs[j])
        elif style == "circular":
            for i in range(n):
                qc.cx(regs[i], regs[(i + 1) % n])
        elif style == "linear_swap":
            for i in range(n - 1):
                qc.cx(regs[i], regs[i + 1])
                qc.swap(regs[i], regs[i + 1])
        elif style == "linear_random_cx":
            rng = np.random.default_rng()
            idx = np.arange(n)
            rng.shuffle(idx)
            used_pairs: set[tuple[int, int]] = set()
            for i in range(0, n - 1, 2):
                qc.cx(regs[idx[i]], regs[idx[i + 1]])
                used_pairs.add(tuple(sorted((idx[i], idx[i + 1]))))
            if n > 1:
                possible_pairs = [
                    (i, j)
                    for i in range(n)
                    for j in range(i + 1, n)
                    if (i, j) not in used_pairs
                ]
                if possible_pairs:
                    pair = possible_pairs[rng.integers(len(possible_pairs))]
                else:
                    pair = (0, 1)
                qc.cx(regs[pair[0]], regs[pair[1]])
        else:  # "linear" ou desconhecido
            idx = np.arange(n)
            np.random.shuffle(idx)
            for i in range(0, n - 1, 2):
                qc.cx(regs[idx[i]], regs[idx[i + 1]])
        qc.barrier()


def _apply_random_scrambling_layer(qc: QuantumCircuit, regs: Sequence) -> None:
    """Aplica uma camada aleat\xc3\xb3ria de Hadamard e CNOT."""

    rng = np.random.default_rng()
    qc.h(regs)

    if len(regs) > 1:
        idx = np.arange(len(regs))
        rng.shuffle(idx)
        for i in range(len(idx) - 1):
            qc.cx(regs[idx[i]], regs[idx[i + 1]])

    qc.barrier()
