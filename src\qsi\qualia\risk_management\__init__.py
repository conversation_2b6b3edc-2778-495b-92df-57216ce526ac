"""Ferramentas de gerenciamento de risco do QUALIA.

Este pacote define a interface base de gerenciadores de risco e implementações
simples usadas em testes. Também expõe utilidades para registrar novas classes
de risk manager.
"""

from __future__ import annotations

import importlib
from typing import Any

__all__ = [
    "QUALIARiskManagerBase",
    "PositionSizingResult",
    "CapitalUpdateResult",
    "SimpleRiskManager",
    "AdvancedRiskManager",
    "StressSimulator",
    "RiskStressSimulator",
    "StressScenario",
    "register_risk_manager",
]

_MODULE_MAP = {
    "QUALIARiskManagerBase": "qualia.risk_management.risk_manager_base",
    "PositionSizingResult": "qualia.risk_management.risk_manager_base",
    "CapitalUpdateResult": "qualia.risk_management.risk_manager_base",
    "SimpleRiskManager": "qualia.risk_management.simple_risk_manager",
    "AdvancedRiskManager": "qualia.risk_management.advanced_risk_manager",
    "StressSimulator": "qualia.risk_management.stress_simulator",
    "RiskStressSimulator": "qualia.risk_management.risk_stress_simulator",
    "StressScenario": "qualia.risk_management.risk_stress_simulator",
    "register_risk_manager": "qualia.risk_management.risk_manager_builder",
}


def __getattr__(name: str) -> Any:
    module_path = _MODULE_MAP.get(name)
    if module_path is None:
        raise AttributeError(f"module 'qualia.risk_management' has no attribute {name}")
    module = importlib.import_module(module_path)
    return getattr(module, name)

