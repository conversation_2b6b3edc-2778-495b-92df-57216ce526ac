"""Backend utilities for QUALIA."""

from __future__ import annotations

from typing import Any, Dict, Optional
import os

from ..utils.logger import get_logger
from ..config.settings import backend_device

logger = get_logger(__name__)

# Flags indicating backend availability. Real imports happen lazily in helpers.
Aer = None
AerSimulator = None
BasicAer = None
_aer_available = False
_basic_aer_available = False
_cupy_available = False
try:  # GPU support check (optional dependency)
    import cupy  # type: ignore  # noqa: F401

    _cupy_available = True
except Exception:
    _cupy_available = False


def _instantiate_aer_simulator(
    backend_name: str, options: Dict[str, Any]
) -> Optional[Any]:
    """Try to instantiate ``AerSimulator`` lazily."""
    global _aer_available
    try:
        from qiskit_aer import AerSimulator as AerSimulatorLocal

        _aer_available = True
    except ImportError:
        try:
            from qiskit.providers.aer import AerSimulator as AerSimulatorLocal

            _aer_available = True
        except Exception:
            return None
    except Exception as exc:  # pragma: no cover - caminho improvável
        logger.error(
            "Erro inesperado ao importar AerSimulator: %s",
            exc,
            exc_info=True,
        )
        return None

    try:
        if backend_name.startswith("aer_simulator") or backend_name in {
            "statevector_simulator",
            "qasm_simulator",
            "unitary_simulator",
            "density_matrix_simulator",
        }:
            # Mapeia o nome do backend para o método correspondente do
            # ``AerSimulator`` para manter compatibilidade com nomes legados.
            if backend_name in {"statevector_simulator", "aer_simulator_statevector"}:
                options.setdefault("method", "statevector")
            elif backend_name in {"unitary_simulator", "aer_simulator_unitary"}:
                options.setdefault("method", "unitary")
            elif backend_name in {
                "density_matrix_simulator",
                "aer_simulator_density_matrix",
            }:
                options.setdefault("method", "density_matrix")

            simulator = AerSimulatorLocal(**options)
            logger.info(
                "AerSimulator instanciado com sucesso para '%s' utilizando o método '%s' com opções %s",
                backend_name,
                options.get("method", "padrão"),
                options,
            )
            return simulator
    except Exception as exc:  # pragma: no cover - difícil reproduzir
        logger.error(
            "Erro ao instanciar AerSimulator para '%s' utilizando o método '%s' com opções %s: %s",
            backend_name,
            options.get("method", "padrão"),
            options,
            exc,
            exc_info=True,
        )
    return None


def _get_legacy_backend(backend_name: str, options: Dict[str, Any]) -> Optional[Any]:
    """Fallback using ``Aer.get_backend`` or ``BasicAer.get_backend``."""
    global _basic_aer_available
    try:
        from qiskit.providers.aer import Aer as AerLocal

        backend = AerLocal.get_backend(backend_name)
        if options and hasattr(backend, "set_options"):
            backend.set_options(**options)
        _basic_aer_available = True
        return backend
    except ImportError:
        logger.debug(
            "Módulo Aer não está disponível para '%s'",
            backend_name,
        )
    except Exception as exc:
        logger.error(
            "Erro ao obter backend '%s' via Aer.get_backend: %s",
            backend_name,
            exc,
            exc_info=True,
        )

    if not _basic_aer_available:
        BasicAerLocal = None
        try:
            from qiskit.providers.basicaer import BasicAer as BasicAerLocal  # type: ignore
        except Exception:
            try:
                from qiskit import BasicAer as BasicAerLocal  # type: ignore
            except Exception as exc:  # pragma: no cover - caminho improvável
                logger.error(
                    "Erro ao importar BasicAer: %s",
                    exc,
                    exc_info=True,
                )
        if BasicAerLocal is not None:
            try:
                backend = BasicAerLocal.get_backend(backend_name)
                if options and hasattr(backend, "set_options"):
                    backend.set_options(**options)
                _basic_aer_available = True
                return backend
            except Exception as exc:  # pragma: no cover - caminho improvável
                logger.error(
                    "Erro ao obter backend '%s' via BasicAer.get_backend: %s",
                    backend_name,
                    exc,
                    exc_info=True,
                )
    return None


def _get_ibm_backend(backend_name: str) -> Optional[Any]:
    """Try to obtain a backend from IBM Quantum using the ``IBMQ_TOKEN`` env var."""
    token = os.getenv("IBMQ_TOKEN")
    if not token:
        return None
    try:
        from qiskit_ibm_provider import IBMProvider

        provider = IBMProvider(token=token)
    except Exception:
        try:  # pragma: no cover - fallback for older qiskit versions
            from qiskit import IBMQ  # type: ignore

            if not IBMQ.active_account():
                IBMQ.enable_account(token)
            provider = IBMQ.get_provider()
        except Exception as exc:  # pragma: no cover - network/login issues
            logger.error("Erro ao autenticar no IBM Quantum: %s", exc, exc_info=True)
            return None

    try:
        return provider.get_backend(backend_name)
    except Exception as exc:  # pragma: no cover - backend unavailable
        logger.error(
            "Falha ao obter backend '%s' via IBM Quantum: %s",
            backend_name,
            exc,
            exc_info=True,
        )
        return None


def safe_get_backend(
    backend_name: str, use_gpu: bool = False, **options: Any
) -> Optional[Any]:
    """Safely obtain a Qiskit backend respecting configuration preferences."""
    device_pref = backend_device
    if use_gpu:
        device_pref = "GPU"
    if device_pref == "GPU" and _cupy_available and "device" not in options:
        options["device"] = "GPU"

    backend = _instantiate_aer_simulator(backend_name, options)
    if backend is not None:
        return backend

    backend = _get_legacy_backend(backend_name, options)
    if backend is not None:
        return backend

    backend = _get_ibm_backend(backend_name)
    if backend is not None:
        return backend

    logger.error(
        "Falha crítica ao obter backend '%s' através de todos os métodos tentados em safe_get_backend.",
        backend_name,
    )
    return None


def is_gpu_available() -> bool:
    """Return True if GPU acceleration is available."""
    if not _cupy_available:
        return False
    try:  # pragma: no cover - simples conferência
        import cupy as cp

        cp.array([0])
        return True
    except Exception:
        return False


def optimize_backend_for_circuit(backend: Any, circuit_size: int) -> Dict[str, Any]:
    """Return optimization options based on circuit size."""
    if backend is None:
        return {}
    options: Dict[str, Any] = {}
    if circuit_size <= 10:
        options.update(
            {
                "max_parallel_threads": 16,
                "max_parallel_experiments": 8,
                "precision": "double",
            }
        )
    elif circuit_size <= 20:
        options.update(
            {
                "max_parallel_threads": 8,
                "max_parallel_experiments": 4,
                "precision": "single",
            }
        )
    else:
        options.update(
            {
                "max_parallel_threads": 4,
                "max_parallel_experiments": 1,
                "precision": "single",
            }
        )
    if is_gpu_available():
        options["device"] = "GPU"
    return options


def select_best_backend(circuit_properties: Dict[str, Any]) -> str:
    """Choose the best backend for a circuit."""
    n_qubits = circuit_properties.get("n_qubits", 0)
    has_measurements = circuit_properties.get("has_measurements", True)
    needs_statevector = circuit_properties.get("needs_statevector", False)

    if needs_statevector:
        if n_qubits <= 20:
            return "statevector_simulator"
        logger.warning(
            "Circuito muito grande para statevector. Usando qasm_simulator.",
        )
        return "qasm_simulator"

    if not has_measurements and n_qubits <= 10:
        return "unitary_simulator"

    return "qasm_simulator"


def get_backend(name: str, **options: Any) -> Optional[Any]:
    """Compatibility wrapper for :func:`safe_get_backend`."""
    return safe_get_backend(name, **options)


def is_aer_available() -> bool:
    """Return True if Qiskit Aer is available."""
    return _aer_available and Aer is not None


def is_basic_aer_available() -> bool:
    """Return True if BasicAer is available."""
    return _basic_aer_available and BasicAer is not None


def get_qiskit_version_info() -> Dict[str, str]:
    """Return version information for installed Qiskit packages."""
    info: Dict[str, str] = {}
    try:
        import qiskit

        info["qiskit"] = qiskit.__version__
    except Exception:
        info["qiskit"] = "não disponível"
    try:
        import qiskit_aer

        info["qiskit_aer"] = qiskit_aer.__version__
    except Exception:
        info["qiskit_aer"] = "não disponível"
    return info


def get_aer():
    """Return the loaded Aer module or ``None``."""
    return Aer if _aer_available else None


__all__ = [
    "safe_get_backend",
    "get_backend",
    "is_aer_available",
    "is_basic_aer_available",
    "is_gpu_available",
    "get_qiskit_version_info",
    "get_aer",
    "optimize_backend_for_circuit",
    "select_best_backend",
]
