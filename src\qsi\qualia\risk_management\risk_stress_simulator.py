from __future__ import annotations

"""Risk stress simulation module."""

import time
from dataclasses import dataclass
from typing import Sequence, Dict, Any, Optional

from .advanced_risk_manager import AdvancedRiskManager
from ..memory.event_bus import SimpleEventBus
from ..events import RiskStressMetrics
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class StressScenario:
    """Configuration for a stress test scenario."""

    liquidity: Sequence[float]
    volatility: Sequence[float]


class RiskStressSimulator:
    """Run stress scenarios on an :class:`AdvancedRiskManager`."""

    def __init__(
        self,
        risk_manager: AdvancedRiskManager,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        self.risk_manager = risk_manager
        self.event_bus = event_bus or risk_manager.event_bus

    def run(
        self, price_series: Sequence[float], scenario: StressScenario
    ) -> Dict[str, Any]:
        """Execute stress simulation for ``scenario``.

        Parameters
        ----------
        price_series
            Sequential close prices used to compute profit and loss.
        scenario
            Liquidity and volatility patterns defining the stress case.

        Returns
        -------
        Dict[str, Any]
            Metrics such as ``max_drawdown_pct``, ``capital_consumption`` and
            latency statistics.
        """
        if not (
            len(price_series) == len(scenario.liquidity) == len(scenario.volatility)
        ):
            raise ValueError(
                "price_series, liquidity and volatility must have same length"
            )

        start_time = time.perf_counter()
        latencies: list[float] = []
        max_drawdown = 0.0

        for i in range(1, len(price_series)):
            step_start = time.perf_counter()
            pnl = price_series[i] - price_series[i - 1]

            if scenario.liquidity[i] < 0.5:
                pnl *= 1.5
            if scenario.volatility[i] > self.risk_manager.max_volatility:
                pnl *= -1.0

            self.risk_manager.process_trade_result({"realized_pnl": pnl})
            max_drawdown = max(max_drawdown, self.risk_manager.current_drawdown_pct)
            latencies.append((time.perf_counter() - step_start) * 1000)

        total_latency_ms = (time.perf_counter() - start_time) * 1000
        avg_latency_ms = sum(latencies) / len(latencies) if latencies else 0.0
        capital_consumption = max(
            0.0,
            self.risk_manager.initial_capital - self.risk_manager.current_capital,
        )

        metrics = {
            "max_drawdown_pct": max_drawdown,
            "average_latency_ms": avg_latency_ms,
            "total_latency_ms": total_latency_ms,
            "capital_consumption": capital_consumption,
        }

        if self.event_bus:
            self.event_bus.publish(
                "risk.stress.metrics", RiskStressMetrics(metrics=metrics)
            )

        logger.info(
            "Risk stress simulation finished: drawdown=%.2f%% capital=%.2f",
            max_drawdown,
            capital_consumption,
        )
        return metrics


__all__ = ["RiskStressSimulator", "StressScenario"]
