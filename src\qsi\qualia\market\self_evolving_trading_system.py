"""
Sistema de Trading Auto-Adaptativo via QAST.

Este módulo implementa um sistema completo de trading que evolui autonomamente
através do ciclo QAST (Quantum Awareness, Self-reflection, Transformation) do
QUALIA, permitindo que o sistema aprenda e se adapte continuamente às
mudanças no mercado.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
import time

from datadog import DogStatsd

from ..utils.logger import get_logger


logger = get_logger(__name__)


class SelfEvolvingTradingSystem:
    """
    Sistema de trading que evolui autonomamente através do ciclo QAST.

    Esta classe implementa um sistema de trading completo que utiliza o ciclo QAST
    do QUALIA para evoluir e adaptar-se continuamente, aprendendo com seus próprios
    resultados e otimizando seus parâmetros e estratégias.
    """

    def __init__(
        self,
        trading_engine,
        qualia_universe,
        statsd_client: Optional[DogStatsd] = None,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        Inicializa o sistema de trading auto-adaptativo.

        Args:
            trading_engine: Motor de trading que gerencia operações e estratégias
            qualia_universe: Referência ao QUALIA Universe
            statsd_client: Cliente opcional do DogStatsd para métricas
        """
        from ..config.self_evolving_defaults import (
            load_self_evolving_defaults,
        )

        self.trading_engine = trading_engine
        self.qualia_universe = qualia_universe
        self.statsd = statsd_client or DogStatsd()
        defaults = load_self_evolving_defaults()
        self.config = {**defaults, **(config or {})}
        self.learning_history = []
        self.optimization_history = []
        self.current_state = {
            "learning_cycle_active": False,
            "cycle_start_time": None,
            "current_parameters": {},
            "last_optimization": None,
        }

    def _call_if_exists(
        self, obj: Any, method_name: str, *args: Any, **kwargs: Any
    ) -> Any:
        """Call ``obj.method_name`` if it exists.

        Parameters
        ----------
        obj
            Object that may implement ``method_name``.
        method_name
            Name of the method to call.
        *args, **kwargs
            Arguments forwarded to the method if it exists.

        Returns
        -------
        Any | None
            Return value of the method or ``None`` when the attribute is
            missing.
        """

        if hasattr(obj, method_name):
            return getattr(obj, method_name)(*args, **kwargs)
        return None

    def initialize_learning_cycle(self):
        """
        Inicia um ciclo de aprendizado contínuo.

        Returns:
            Dicionário com informações sobre o ciclo iniciado
        """
        logger.info("Starting learning cycle")
        # Criar uma representação do estado atual do sistema para o QUALIA
        system_state = self._encode_trading_system_state()

        # Marcar início do ciclo
        self.current_state["learning_cycle_active"] = True
        self.current_state["cycle_start_time"] = datetime.now(timezone.utc).isoformat()
        self.current_state["current_parameters"] = (
            self._call_if_exists(self.trading_engine, "get_parameters") or {}
        )

        # Iniciar ciclo QAST para autoreflexão do sistema
        if hasattr(self.qualia_universe, "consciousness"):
            self._call_if_exists(
                self.qualia_universe.consciousness,
                "initialize_qast_cycle",
                focus="system_optimization",
                initial_state=system_state,
            )

        # Registrar início no histórico
        cycle_info = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event": "learning_cycle_initialized",
            "initial_state": system_state,
        }
        self.learning_history.append(cycle_info)

        logger.info("Learning cycle initialized")

        return cycle_info

    def execute_learning_iteration(
        self,
        market_data: Dict[str, Any],
        trading_results: Dict[str, Any],
        trace_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Executa uma iteração do ciclo de aprendizado.

        Args:
            market_data: Dados de mercado recentes
            trading_results: Resultados recentes de trading
            trace_id: ID opcional para rastreamento de métricas

        Returns:
            Dicionário com sugestões de otimização
        """
        logger.debug("Executing learning iteration")
        start_time = time.perf_counter()
        # Verificar se ciclo está ativo
        if not self.current_state["learning_cycle_active"]:
            logger.info("Learning iteration called but cycle inactive")
            return {
                "success": False,
                "message": "Ciclo de aprendizado não está ativo. Execute initialize_learning_cycle primeiro.",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        # Preparar dados para reflexão
        current_parameters = (
            self._call_if_exists(self.trading_engine, "get_parameters") or {}
        )

        reflection_data = {
            "market_data": self._prepare_market_data(market_data),
            "trading_results": self._prepare_trading_results(trading_results),
            "current_parameters": current_parameters,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Alimentar dados para o ciclo QAST
        if hasattr(self.qualia_universe, "consciousness"):
            self._call_if_exists(
                self.qualia_universe.consciousness,
                "process_perception",
                perception_type="learning_data",
                perception_data=reflection_data,
            )

        # Executar um ciclo QAST
        qast_result = None
        if hasattr(self.qualia_universe, "consciousness"):
            qast_result = self._call_if_exists(
                self.qualia_universe.consciousness, "process_qast_cycle"
            )

        # Extrair sugestões de otimização
        optimization_suggestions = self._extract_optimization_insights(qast_result)

        # Aplicar otimizações ao sistema de trading, se autorizado
        if optimization_suggestions.get("apply_automatically", False):
            self._apply_system_optimizations(
                optimization_suggestions, trace_id=trace_id
            )
            optimization_suggestions["changes_applied"] = True
        else:
            optimization_suggestions["changes_applied"] = False

        # Registrar aprendizado
        iteration_record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "qast_result": qast_result,
            "optimization_suggestions": optimization_suggestions,
            "applied_optimizations": optimization_suggestions.get(
                "changes_applied", False
            ),
        }

        self.learning_history.append(iteration_record)

        # Atualizar estado atual
        self.current_state["last_optimization"] = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "suggestions": optimization_suggestions,
        }

        elapsed_ms = (time.perf_counter() - start_time) * 1000
        tags = [f"trace_id:{trace_id}"] if trace_id else None
        self.statsd.timing("self_evolving.iteration_time_ms", elapsed_ms, tags=tags)

        return optimization_suggestions

    def apply_pending_optimizations(
        self, trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Aplica otimizações pendentes que não foram aplicadas automaticamente.

        Args:
            trace_id: ID opcional para rastreamento de métricas

        Returns:
            Dicionário com informações sobre as mudanças aplicadas
        """
        logger.debug("Applying pending optimizations")
        # Verificar se há otimizações pendentes
        if not self.current_state.get("last_optimization"):
            logger.info("No pending optimizations to apply")
            return {
                "success": False,
                "message": "Nenhuma otimização pendente para aplicar",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        last_opt = self.current_state["last_optimization"]
        suggestions = last_opt.get("suggestions", {})

        # Se já foram aplicadas, não aplicar novamente
        if suggestions.get("changes_applied", False):
            logger.info("Pending optimizations already applied")
            return {
                "success": False,
                "message": "Otimizações já foram aplicadas anteriormente",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "last_applied": last_opt.get("timestamp"),
            }

        # Aplicar as otimizações
        self._apply_system_optimizations(suggestions, trace_id=trace_id)

        # Atualizar estado
        self.current_state["last_optimization"]["suggestions"]["changes_applied"] = True

        # Registrar aplicação
        application_record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event": "pending_optimizations_applied",
            "suggestions": suggestions,
        }

        self.optimization_history.append(application_record)

        return {
            "success": True,
            "message": "Otimizações pendentes aplicadas com sucesso",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "applied_suggestions": suggestions.get("parameter_changes", {}),
        }

    def end_learning_cycle(self, trace_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Finaliza o ciclo de aprendizado atual.

        Args:
            trace_id: ID opcional para rastreamento de métricas

        Returns:
            Dicionário com resumo do ciclo de aprendizado
        """
        logger.info("Ending learning cycle")
        if not self.current_state["learning_cycle_active"]:
            logger.info("No active learning cycle to end")
            return {
                "success": False,
                "message": "Nenhum ciclo de aprendizado ativo para finalizar",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        # Calcular duração do ciclo
        start_time = datetime.fromisoformat(self.current_state["cycle_start_time"])
        end_time = datetime.now(timezone.utc)
        duration = (end_time - start_time).total_seconds()

        # Extrair informações relevantes
        iterations = sum(
            1
            for record in self.learning_history
            if record.get("qast_result") is not None
            and datetime.fromisoformat(record["timestamp"]) >= start_time
        )

        optimizations_applied = sum(
            1
            for record in self.learning_history
            if record.get("applied_optimizations", False)
            and datetime.fromisoformat(record["timestamp"]) >= start_time
        )

        # Resumir mudanças nos parâmetros
        initial_params = self.current_state["current_parameters"]
        current_params = (
            self._call_if_exists(self.trading_engine, "get_parameters") or {}
        )

        parameter_changes = {}
        for key in set(initial_params.keys()).union(current_params.keys()):
            if key in initial_params and key in current_params:
                if initial_params[key] != current_params[key]:
                    parameter_changes[key] = {
                        "from": initial_params[key],
                        "to": current_params[key],
                        "change_pct": self._calculate_change_percentage(
                            initial_params[key], current_params[key]
                        ),
                    }

        # Criar resumo
        cycle_summary = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "cycle_start": self.current_state["cycle_start_time"],
            "cycle_end": datetime.now(timezone.utc).isoformat(),
            "duration_seconds": duration,
            "iterations_completed": iterations,
            "optimizations_applied": optimizations_applied,
            "parameter_changes": parameter_changes,
        }

        # Registrar finalização no histórico
        end_record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event": "learning_cycle_ended",
            "summary": cycle_summary,
        }

        self.learning_history.append(end_record)

        # Resetar estado atual
        self.current_state["learning_cycle_active"] = False
        self.current_state["cycle_start_time"] = None

        logger.info("Learning cycle ended")

        tags = [f"trace_id:{trace_id}"] if trace_id else None
        self.statsd.timing(
            "self_evolving.cycle_duration_ms",
            duration * 1000,
            tags=tags,
        )

        return {
            "success": True,
            "message": "Ciclo de aprendizado finalizado com sucesso",
            "cycle_summary": cycle_summary,
        }

    def _encode_trading_system_state(self) -> Dict[str, Any]:
        """
        Codifica o estado atual do sistema de trading para processamento QAST.

        Returns:
            Representação do estado do sistema para o QUALIA
        """
        # Tentar obter parâmetros atuais do motor de trading
        current_parameters = {}
        result = self._call_if_exists(self.trading_engine, "get_parameters")
        if result is not None:
            current_parameters = result

        # Tentar obter estatísticas de performance
        performance_metrics = {}
        result = self._call_if_exists(self.trading_engine, "get_performance_metrics")
        if result is not None:
            performance_metrics = result

        # Tentar obter configurações de estratégias
        strategies_config = {}
        result = self._call_if_exists(self.trading_engine, "get_strategies_config")
        if result is not None:
            strategies_config = result

        # Codificar estado completo
        system_state = {
            "type": "trading_system_state",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "parameters": current_parameters,
            "performance": performance_metrics,
            "strategies": strategies_config,
            "learning_history_size": len(self.learning_history),
            "normalized_parameters": self._normalize_parameters(current_parameters),
        }

        return system_state

    def _normalize_parameters(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """
        Normaliza parâmetros para representação quântica.

        Args:
            parameters: Dicionário de parâmetros do sistema

        Returns:
            Dicionário com parâmetros normalizados para processamento quântico
        """
        normalized = {}
        divs = self.config.get("normalization_divisors", {})
        period_div = divs.get("period", 200.0)
        stop_div = divs.get("stop", 0.05)
        profit_div = divs.get("profit", 0.1)
        val_high = divs.get("value_high", 10000.0)
        val_mid1 = divs.get("value_mid1", 1000.0)
        val_mid2 = divs.get("value_mid2", 100.0)
        val_mid3 = divs.get("value_mid3", 10.0)

        for key, value in parameters.items():
            # Tentar normalizar baseado no tipo
            if isinstance(value, (int, float)) and not isinstance(value, bool):
                # Fazer uma normalização simples baseada em faixas típicas
                if key.endswith("_period") or key.startswith("period"):
                    # Períodos de indicadores (normalmente entre 1-200)
                    normalized[key] = min(1.0, value / period_div)
                elif "stop" in key.lower() or "sl" in key.lower():
                    # Stop loss (normalmente entre 0.001-0.05)
                    normalized[key] = min(1.0, value / stop_div)
                elif "profit" in key.lower() or "tp" in key.lower():
                    # Take profit (normalmente entre 0.002-0.1)
                    normalized[key] = min(1.0, value / profit_div)
                elif "size" in key.lower() or "volume" in key.lower():
                    # Tamanho de posição (normalmente entre 0.01-1.0)
                    normalized[key] = min(1.0, value)
                else:
                    # Para outros valores numéricos, normalizar para 0-1
                    # assumindo valores razoáveis para trading
                    if abs(value) > val_mid1:
                        normalized[key] = min(1.0, abs(value) / val_high)
                    elif abs(value) > val_mid2:
                        normalized[key] = min(1.0, abs(value) / val_mid1)
                    elif abs(value) > val_mid3:
                        normalized[key] = min(1.0, abs(value) / val_mid2)
                    elif abs(value) > 1:
                        normalized[key] = min(1.0, abs(value) / val_mid3)
                    else:
                        normalized[key] = min(1.0, abs(value))
            elif isinstance(value, bool):
                normalized[key] = 1.0 if value else 0.0

        return normalized

    def _prepare_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepara dados de mercado para processamento QAST.

        Args:
            market_data: Dados brutos de mercado

        Returns:
            Dados de mercado processados para QAST
        """
        # Extrair apenas informações relevantes e simplificar
        processed_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": {},
        }

        # Extrair métricas de mercado relevantes, se existirem
        for key in ["price", "volume", "volatility", "trend", "sentiment"]:
            if key in market_data:
                processed_data["metrics"][key] = market_data[key]

        # Extrair indicadores técnicos, se existirem
        if "indicators" in market_data:
            processed_data["indicators"] = market_data["indicators"]

        # Adicionar métricas quânticas se existirem
        if "quantum_metrics" in market_data:
            processed_data["quantum_metrics"] = market_data["quantum_metrics"]

        return processed_data

    def _prepare_trading_results(
        self, trading_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Prepara resultados de trading para processamento QAST.

        Args:
            trading_results: Resultados brutos de trading

        Returns:
            Resultados de trading processados para QAST
        """
        # Extrair apenas informações relevantes
        processed_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": {},
        }

        # Extrair métricas de performance
        important_metrics = [
            "profit_loss",
            "win_rate",
            "profit_factor",
            "max_drawdown",
            "sharpe_ratio",
            "trades_count",
            "avg_profit",
            "avg_loss",
            "avg_holding_time",
        ]

        for metric in important_metrics:
            if metric in trading_results:
                processed_results["metrics"][metric] = trading_results[metric]

        # Extrair trades recentes se existirem
        if "recent_trades" in trading_results:
            processed_results["recent_trades"] = trading_results["recent_trades"]

        return processed_results

    def _extract_optimization_insights(
        self, qast_result: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extrai insights de otimização do resultado QAST.

        Args:
            qast_result: Resultado do ciclo QAST

        Returns:
            Dicionário com sugestões de otimização
        """
        # Estrutura básica para as sugestões
        suggestions = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "parameter_changes": {},
            "strategy_recommendations": [],
            "risk_adjustments": {},
            "apply_automatically": False,
            "confidence": 0.0,
        }

        # Se não houver resultado QAST, retornar sugestões vazias
        if not qast_result:
            suggestions["message"] = (
                "Nenhum resultado QAST disponível para extração de insights"
            )
            return suggestions

        # Extrair métricas-chave
        metrics = qast_result.get("metrics", {})
        coherence = metrics.get("coherence", 0.0)
        entropy = metrics.get("entropy", 0.0)
        reflection_depth = metrics.get("self_reflection_depth", 0.0)

        cfg = self.config
        high_coh = cfg.get("high_coherence_threshold", 0.7)
        low_coh = cfg.get("low_coherence_threshold", 0.3)
        high_ent = cfg.get("high_entropy_threshold", 0.7)
        low_ent = cfg.get("low_entropy_threshold", 0.3)
        auto_reflect = cfg.get("auto_reflection_threshold", 0.5)
        reduce_factor = cfg.get("position_size_reduce_factor", 0.8)
        increase_factor = cfg.get("position_size_increase_factor", 1.15)
        stop_inc = cfg.get("stop_loss_increase_factor", 1.2)
        stop_dec = cfg.get("stop_loss_decrease_factor", 0.9)
        risk_low = cfg.get("risk_multiplier_low", 0.7)
        risk_high = cfg.get("risk_multiplier_high", 1.2)
        conf_div = cfg.get("confidence_divisor", 2)
        coh_uncertain = cfg.get("uncertain_coherence_threshold", 0.4)
        strat_coh = cfg.get("strategy_coherence_threshold", 0.6)
        strat_ent = cfg.get("strategy_entropy_threshold", 0.4)
        tf_ent_thresh = cfg.get("entropy_timeframe_threshold", 0.8)

        # Confiança baseada em coerência e profundidade de reflexão
        confidence = (coherence + reflection_depth) / conf_div
        suggestions["confidence"] = confidence

        # Decidir se deve aplicar automaticamente
        # Aplicar automaticamente apenas se alta coerência e reflexão
        suggestions["apply_automatically"] = (
            coherence > high_coh and reflection_depth > auto_reflect
        )

        # Se baixa coerência, não fazer alterações significativas
        if coherence < low_coh:
            suggestions["message"] = (
                "Baixa coerência quântica. Não recomendado fazer alterações significativas no momento."
            )
            suggestions["apply_automatically"] = False
            return suggestions

        # Obter parâmetros atuais
        current_parameters = (
            self._call_if_exists(self.trading_engine, "get_parameters") or {}
        )

        # Gerar sugestões de otimização baseadas em padrões QAST e condições atuais
        # Esta é uma implementação simplificada e deve ser expandida com lógica específica
        # baseada nas métricas QAST e na situação atual do sistema

        # Exemplo: ajustar tamanho da posição baseado em entropia
        if "position_size_scale" in current_parameters:
            current_size = current_parameters["position_size_scale"]

            # Se alta entropia (mercado caótico), reduzir tamanho
            if entropy > high_ent:
                new_size = current_size * reduce_factor
                suggestions["parameter_changes"]["position_size_scale"] = {
                    "from": current_size,
                    "to": new_size,
                    "reason": "Alta entropia quântica indica mercado caótico",
                    "change_percentage": -20,
                }

            # Se baixa entropia e alta coerência (mercado previsível), aumentar
            # tamanho
            elif entropy < low_ent and coherence > high_coh:
                new_size = current_size * increase_factor
                suggestions["parameter_changes"]["position_size_scale"] = {
                    "from": current_size,
                    "to": new_size,
                    "reason": "Baixa entropia e alta coerência indicam mercado previsível",
                    "change_percentage": 15,
                }

        # Exemplo: ajustar stop loss baseado em coerência
        if "stop_loss_percentage" in current_parameters:
            current_stop = current_parameters["stop_loss_percentage"]

            # Se baixa coerência (direção incerta), aumentar stop
            if coherence < coh_uncertain:
                new_stop = current_stop * stop_inc
                suggestions["parameter_changes"]["stop_loss_percentage"] = {
                    "from": current_stop,
                    "to": new_stop,
                    "reason": "Baixa coerência indica direção de mercado incerta",
                    "change_percentage": 20,
                }

            # Se alta coerência (direção clara), reduzir stop
            elif coherence > high_coh:
                new_stop = current_stop * stop_dec
                suggestions["parameter_changes"]["stop_loss_percentage"] = {
                    "from": current_stop,
                    "to": new_stop,
                    "reason": "Alta coerência indica direção de mercado clara",
                    "change_percentage": -10,
                }

        # Exemplo: ajustar timeframe baseado em entropia
        if "preferred_timeframe" in current_parameters:
            current_tf = current_parameters["preferred_timeframe"]

            # Se alta entropia, mover para timeframes maiores
            if entropy > tf_ent_thresh and current_tf in ["1m", "5m", "15m"]:
                suggestions["parameter_changes"]["preferred_timeframe"] = {
                    "from": current_tf,
                    "to": self._get_higher_timeframe(current_tf),
                    "reason": "Alta entropia quântica, recomendado timeframe maior para reduzir ruído",
                    "change_percentage": None,
                }

        # Adicionar recomendações estratégicas
        if coherence > strat_coh and entropy < strat_ent:
            suggestions["strategy_recommendations"].append(
                {
                    "type": "strategy_focus",
                    "recommendation": "trend_following",
                    "confidence": coherence * (1 - entropy),
                    "reason": "Alta coerência e baixa entropia indicam boas condições para seguir tendências",
                }
            )

        if coherence < low_coh and entropy < strat_ent:
            suggestions["strategy_recommendations"].append(
                {
                    "type": "strategy_focus",
                    "recommendation": "range_trading",
                    "confidence": (1 - coherence) * (1 - entropy),
                    "reason": "Baixa coerência e baixa entropia indicam condições de mercado em range",
                }
            )

        if entropy > high_ent:
            suggestions["strategy_recommendations"].append(
                {
                    "type": "exposure_adjustment",
                    "recommendation": "reduce_exposure",
                    "confidence": entropy,
                    "reason": "Alta entropia indica mercado caótico, recomendado reduzir exposição",
                }
            )

        # Ajustar risco global
        if coherence < low_coh or entropy > high_ent:
            suggestions["risk_adjustments"] = {
                "global_risk_multiplier": risk_low,
                "reason": f"Condições quânticas desfavoráveis: coerência={coherence:.2f}, entropia={entropy:.2f}",
            }
        elif coherence > high_coh and entropy < low_ent:
            suggestions["risk_adjustments"] = {
                "global_risk_multiplier": risk_high,
                "reason": f"Condições quânticas favoráveis: coerência={coherence:.2f}, entropia={entropy:.2f}",
            }

        return suggestions

    def _apply_system_optimizations(
        self, suggestions: Dict[str, Any], trace_id: Optional[str] = None
    ) -> bool:
        """
        Aplica otimizações ao sistema de trading.

        Args:
            suggestions: Sugestões de otimização
            trace_id: ID opcional para rastreamento de métricas

        Returns:
            True se mudanças foram aplicadas, False caso contrário
        """
        logger.debug("Applying system optimizations")
        # Verificar se há parâmetros para alterar
        if not suggestions.get("parameter_changes"):
            logger.info("No parameter changes to apply")
            return False

        # Aplicar mudanças nos parâmetros
        for param_name, change_info in suggestions["parameter_changes"].items():
            new_value = change_info.get("to")

            # Usar a interface do trading_engine para alterar parâmetros
            self._call_if_exists(
                self.trading_engine, "set_parameter", param_name, new_value
            )

        # Aplicar ajustes de risco global
        if suggestions.get("risk_adjustments"):
            risk_mult = suggestions["risk_adjustments"].get("global_risk_multiplier")
            if risk_mult is not None:
                self._call_if_exists(
                    self.trading_engine, "set_risk_multiplier", risk_mult
                )

        # Registrar otimização
        optimization_record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event": "system_optimizations_applied",
            "parameter_changes": suggestions.get("parameter_changes", {}),
            "risk_adjustments": suggestions.get("risk_adjustments", {}),
        }

        self.optimization_history.append(optimization_record)

        tags = [f"trace_id:{trace_id}"] if trace_id else None
        self.statsd.increment("self_evolving.optimizations_applied", tags=tags)

        return True

    def _calculate_change_percentage(
        self, old_value: Any, new_value: Any
    ) -> Optional[float]:
        """
        Calcula a porcentagem de mudança entre dois valores.

        Args:
            old_value: Valor antigo
            new_value: Valor novo

        Returns:
            Porcentagem de mudança ou None se não for calculável
        """
        try:
            if isinstance(old_value, (int, float)) and isinstance(
                new_value, (int, float)
            ):
                if old_value == 0:
                    return None
                return ((new_value - old_value) / old_value) * 100
            return None
        except (TypeError, ZeroDivisionError):
            return None

    def _get_higher_timeframe(self, current_tf: str) -> str:
        """
        Retorna um timeframe maior que o atual.

        Args:
            current_tf: Timeframe atual

        Returns:
            Próximo timeframe maior
        """
        tf_order = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]

        try:
            current_index = tf_order.index(current_tf)
            if current_index < len(tf_order) - 1:
                return tf_order[current_index + 1]
            return current_tf
        except ValueError:
            return current_tf

    def get_system_status(self) -> Dict[str, Any]:
        """
        Retorna o status atual do sistema.

        Returns:
            Dicionário com informações sobre o estado atual do sistema
        """
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "learning_cycle_active": self.current_state["learning_cycle_active"],
            "cycle_start_time": self.current_state["cycle_start_time"],
            "optimizations_applied": len(self.optimization_history),
            "learning_iterations": len(self.learning_history),
            "last_optimization": self.current_state.get("last_optimization"),
        }

    def get_learning_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Retorna o histórico recente de aprendizado.

        Args:
            limit: Número máximo de registros a retornar

        Returns:
            Lista com o histórico recente de aprendizado
        """
        return self.learning_history[-limit:] if self.learning_history else []
