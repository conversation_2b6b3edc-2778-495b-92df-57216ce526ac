"""Registro de Estratégias do QUALIA Framework (DEPRECATED).

Este módulo oferece utilidades legadas para consulta de estratégias, mas o uso
recomendado é a :class:`StrategyFactory`. As importações deste módulo serão
removidas em versões futuras.
"""

import importlib
import inspect
from ..utils.logger import get_logger
import pkgutil
import sys  # Necessário para consultar sys.modules
from typing import Any, Callable, Dict, List, Optional, Type

# Mantemos apenas TradingStrategy; o antigo STRATEGY_REGISTRY foi descontinuado
from ..strategies.strategy_interface import TradingStrategy

# Importa utilidades da StrategyFactory, usada como ponto único de registro
from ..strategies.strategy_factory import (
    get_strategy_class_in_factory,
    get_available_strategies_in_factory,
    is_strategy_registered_in_factory,
)

logger = get_logger(__name__)


def get_strategy_by_name(name: str) -> Type[TradingStrategy]:
    """Obtém a classe de uma estratégia pelo alias."""
    strategy_cls = get_strategy_class_in_factory(name)
    if strategy_cls is None:
        raise KeyError(f"Estratégia '{name}' não encontrada na StrategyFactory.")
    return strategy_cls


def get_available_strategies() -> List[str]:
    """Retorna a lista de aliases de estratégias disponíveis."""
    return get_available_strategies_in_factory()


def strategy_exists(name: str) -> bool:
    """Verifica se uma estratégia está registrada na StrategyFactory."""
    return is_strategy_registered(name)


def is_strategy_registered(name: str) -> bool:
    """Alias de compatibilidade para ``strategy_exists``."""
    return is_strategy_registered_in_factory(name)


def register_builtin_strategies() -> None:
    """
    Descobre e registra automaticamente todas as estratégias definidas
    nos submódulos de qualia.strategies, exceto as do diretório legacy.
    Este método deve ser chamado uma vez na inicialização para popular a fábrica.
    """
    logger.info("Discovering and registering built-in strategies (excluding legacy)...")

    current_package_name = __package__  # Ex: 'qualia.strategies'
    if not current_package_name:
        logger.error(
            "Could not determine package name for strategy registration. Strategies may not be registered."
        )
        return

    try:
        package_module = sys.modules[current_package_name]
        package_path = package_module.__path__
        prefix = package_module.__name__ + "."
    except KeyError:
        logger.error(
            f"Package '{current_package_name}' not found in sys.modules. Cannot register strategies dynamically."
        )
        return

    logger.debug(
        f"Iterating modules for strategy registration in path: {package_path} with prefix: {prefix}"
    )

    # ``pkgutil.walk_packages`` percorre recursivamente subpacotes, garantindo
    # que módulos dentro de pacotes aninhados também sejam descobertos. Isso é
    # necessário para estratégias que vivem em subdiretórios, como
    # ``quantum_trend_reversal``.
    for importer, modname, ispkg in pkgutil.walk_packages(package_path, prefix):
        # Pular módulos do diretório legacy
        if ".legacy" in modname:
            logger.debug(f"Skipping legacy strategy module: {modname}")
            continue
        try:
            # Evitar re-importar o próprio registry ou o __init__ do pacote principal se não contiverem estratégias
            # e para evitar possíveis loops de importação residuais.
            if modname == __name__:  # __name__ aqui será 'qualia.strategies.registry'
                logger.debug(f"Skipping self-import: {modname}")
                continue
            if (
                modname == current_package_name
            ):  # Evitar importar o __init__.py do pacote via seu nome de pacote
                logger.debug(
                    f"Skipping package __init__ import via package name: {modname}"
                )
                continue
            # Adicionalmente, verificar se o __init__.py é o próprio modname
            # (pkgutil pode listá-lo como prefix + __init__)
            if modname == prefix + "__init__":
                logger.debug(f"Skipping explicit __init__ module: {modname}")
                continue

            # Filtrar para não importar módulos que não são diretamente estratégias ou que causam problemas conhecidos.
            # Exemplo: não importar 'strategy_interface' ou 'strategy_factory' como se fossem módulos de estratégia.
            # Esta é uma lista de exclusão simples; pode ser necessário
            # refinar.
            excluded_modules = [
                prefix + "strategy_interface",
                prefix + "strategy_factory",
                prefix + "registry",
            ]
            if modname in excluded_modules:
                logger.debug(f"Skipping known non-strategy/utility module: {modname}")
                continue

            logger.debug(f"Attempting to import strategy module or package: {modname}")
            importlib.import_module(modname)
        except ImportError as e:
            logger.warning(
                f"Could not import module '{modname}' during strategy registration: {e}"
            )
        except Exception as e_general:
            logger.error(
                f"Unexpected error importing module '{modname}': {e_general}",
                exc_info=True,
            )

    # Usa o método estático da StrategyFactory para listar aliases registrados
    registered_aliases = get_available_strategies_in_factory()
    logger.info(
        f"Total strategies registered in StrategyFactory: {len(registered_aliases)}"
    )
    if registered_aliases:
        logger.info(
            f"Available strategies in Factory: {sorted(list(registered_aliases))}"
        )
    else:
        logger.warning(
            "No strategies were registered in the StrategyFactory. Check strategy definitions and decorators."
        )

    # Log de categorias disponíveis (requer que a factory exponha metadados)
    # categories = set()
    # for name in registered_names:
    #     strategy_class = factory_instance.get_strategy_class(name)
    #     if hasattr(strategy_class, 'metadata') and isinstance(strategy_class.metadata, dict):
    #         categories.add(strategy_class.metadata.get('category', 'desconhecida'))
    # logger.info(f"Categorias disponíveis na Factory: {sorted(list(categories))}")


# O registro das estratégias acontece automaticamente quando o pacote
# ``src.qualia`` é importado, pois ``register_builtin_strategies`` é
# invocado no ``__init__`` do pacote. Chamadas adicionais a esta função
# são idempotentes, mas normalmente desnecessárias.
