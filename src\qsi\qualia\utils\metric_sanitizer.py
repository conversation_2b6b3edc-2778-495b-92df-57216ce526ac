from __future__ import annotations

"""Utilities for sanitizing metrics received via WebSocket."""

from typing import Any, Dict, Iterable, List

from .logger import get_logger

logger = get_logger(__name__)


def _clamp(value: float, min_value: float, max_value: float) -> float:
    """Return ``value`` clamped between ``min_value`` and ``max_value``."""
    return max(min_value, min(max_value, value))


def _to_float(name: str, value: Any, default: float = 0.0) -> float:
    """Convert *value* to ``float`` logging warnings on failure."""
    try:
        return float(value)
    except (TypeError, ValueError):
        logger.warning("M\u00e9trica %s inv\u00e1lida: %s", name, value)
        return default


def sanitize_metrics(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """Return a sanitized copy of *metrics* with clamped values.

    Values outside the expected ranges are clipped and a warning is logged.
    """
    sanitized: Dict[str, Any] = dict(metrics)

    entropy = _to_float("entropy", metrics.get("entropy", 0.0))
    if not 0.0 <= entropy <= 1.0:
        logger.warning("entropy fora de faixa: %s", entropy)
    sanitized["entropy"] = _clamp(entropy, 0.0, 1.0)

    coherence = _to_float("coherence", metrics.get("coherence", 0.0))
    if not 0.0 <= coherence <= 1.0:
        logger.warning("coherence fora de faixa: %s", coherence)
    sanitized["coherence"] = _clamp(coherence, 0.0, 1.0)

    hue = _to_float("hue", metrics.get("hue", 0.0))
    if not 0.0 <= hue <= 360.0:
        logger.warning("hue fora de faixa: %s", hue)
    sanitized["hue"] = _clamp(hue, 0.0, 360.0)

    brightness = _to_float("brightness", metrics.get("brightness", 0.0))
    if not 0.0 <= brightness <= 1.0:
        logger.warning("brightness fora de faixa: %s", brightness)
    sanitized["brightness"] = _clamp(brightness, 0.0, 1.0)

    emb_norm = _to_float("embedding_norm", metrics.get("embedding_norm", 0.0))
    if not 0.0 <= emb_norm <= 1.0:
        logger.warning("embedding_norm fora de faixa: %s", emb_norm)
    sanitized["embedding_norm"] = _clamp(emb_norm, 0.0, 1.0)

    if "liquidity_buckets" in metrics:
        buckets = metrics["liquidity_buckets"]
        if not isinstance(buckets, Iterable):
            logger.warning("liquidity_buckets inv\u00e1lido: %s", buckets)
            sanitized["liquidity_buckets"] = []
        else:
            sanitized_buckets: List[float] = []
            for val in buckets:
                fval = _to_float("liquidity_buckets", val)
                if not 0.0 <= fval <= 1.0:
                    logger.warning("liquidity_buckets fora de faixa: %s", fval)
                sanitized_buckets.append(_clamp(fval, 0.0, 1.0))
            sanitized["liquidity_buckets"] = sanitized_buckets

    if "trend_strength" in metrics:
        t_strength = _to_float("trend_strength", metrics.get("trend_strength"))
        if not 0.0 <= t_strength <= 1.0:
            logger.warning("trend_strength fora de faixa: %s", t_strength)
        sanitized["trend_strength"] = _clamp(t_strength, 0.0, 1.0)

    if "delta_entropy" in metrics:
        delta = _to_float("delta_entropy", metrics.get("delta_entropy"))
        if not -1.0 <= delta <= 1.0:
            logger.warning("delta_entropy fora de faixa: %s", delta)
        sanitized["delta_entropy"] = _clamp(delta, -1.0, 1.0)

    return sanitized
