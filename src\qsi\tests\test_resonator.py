import numpy as np
from qsi.resonator import GeometricQSIResonator


def test_resonator_allows_custom_phi():
    resonator = GeometricQSIResonator(dimensions=2, phi1=3.0, phi2=4.0)
    assert resonator.U1.shape == (2, 2)
    assert resonator.U2.shape == (2, 2)


def test_resonator_accepts_custom_unitaries():
    U = np.eye(2, dtype=np.complex128)
    resonator = GeometricQSIResonator(dimensions=2, U1=U, U2=U)
    assert np.allclose(resonator.U1, U)
    assert np.allclose(resonator.U2, U)

