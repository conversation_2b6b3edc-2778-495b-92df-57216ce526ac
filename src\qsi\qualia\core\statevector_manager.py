"""Gerenciamento de statevectors e execução assíncrona de simulações."""

from __future__ import annotations

import async<PERSON>
from typing import Any, Dict, Optional

from qiskit import QuantumCircuit
from qiskit.providers.exceptions import JobTimeoutError
from qiskit.exceptions import QiskitError

from ..utils.logger import get_logger

logger = get_logger(__name__)


def run_on_backends(
    circuit: QuantumCircuit,
    shots: int = 1024,
    noise_model: Optional[Any] = None,
    *,
    use_gpu: bool = False,
) -> Dict:
    """Executa um circuito em diferentes backends."""
    results = {}

    if circuit is None or not hasattr(circuit, "num_qubits"):
        logger.error("run_on_backends: Circuito inválido fornecido")
        return {"error": "Invalid circuit"}

    if shots <= 0:
        raise ValueError("shots must be positive")

    from . import universe as _universe

    backend_sv = _universe.safe_get_backend("statevector_simulator", use_gpu=use_gpu)
    if isinstance(circuit, QuantumCircuit) and hasattr(backend_sv, "run"):
        try:
            job_sv = backend_sv.run(circuit)
            results["statevector"] = job_sv.result().get_statevector(circuit)
        except (JobTimeoutError, QiskitError) as exc:
            logger.error("Falha ao executar statevector backend: %s", exc)

    backend_qasm = _universe.safe_get_backend("qasm_simulator", use_gpu=use_gpu)
    if isinstance(circuit, QuantumCircuit) and hasattr(backend_qasm, "run"):
        try:
            job_qasm = backend_qasm.run(circuit, shots=shots)
            results["qasm"] = job_qasm.result().get_counts(circuit)
        except (JobTimeoutError, QiskitError) as exc:
            logger.error("Falha ao executar qasm backend: %s", exc)

    if noise_model is not None:
        backend_noisy = _universe.safe_get_backend("qasm_simulator", use_gpu=use_gpu)
        if isinstance(circuit, QuantumCircuit) and hasattr(backend_noisy, "run"):
            try:
                job_noisy = backend_noisy.run(
                    circuit, shots=shots, noise_model=noise_model
                )
                results["noisy"] = job_noisy.result().get_counts(circuit)
            except (JobTimeoutError, QiskitError) as exc:
                logger.error("Falha ao executar backend com ruído: %s", exc)

    return results


async def run_on_backends_async(
    circuit: QuantumCircuit,
    shots: int = 1024,
    noise_model: Optional[Any] = None,
    *,
    use_gpu: bool = False,
) -> Dict:
    """Versão assíncrona de :func:`run_on_backends` usando threads."""
    return await asyncio.to_thread(
        run_on_backends, circuit, shots, noise_model, use_gpu=use_gpu
    )
