"""
Utilitários para Estratégias de Trading do QUALIA.
"""

from ..utils.logger import get_logger
from typing import (
    Dict,
    Optional,
    Any,
    Tuple,
    Type,
    TYPE_CHECKING,
    List,
    get_args,
    get_origin,
)
import pandas as pd
import importlib
import copy

# Importações diretas de dentro do pacote QUALIA para evitar dependências circulares complexas
# se strategy_utils for usado por strategy_factory ou vice-versa em algum ponto.
# É melhor manter as dependências o mais lineares possível.
from ..strategies.strategy_interface import OrderDecision, TradingStrategy
from ..strategies.strategy_factory import (
    StrategyFactory,
    get_strategy_class_in_factory,
)
from dataclasses import is_dataclass, asdict
import inspect
from .params import QualiaTSVFParams

# Para type hinting, podemos precisar de uma forward reference ou importação tardia se houver ciclos.
# Por ora, vamos usar 'Any' para os tipos de estratégia e QAST para simplificar.
# Posteriormente, pode-se refinar com:
# TradingStrategy = "qualia.strategies.strategy_interface.TradingStrategy"
# QASTEvolutionaryStrategy = "qualia.strategies.qast_evolutionary_strategy.QASTEvolutionaryStrategy"

from ..config.config_manager import ConfigManager

if TYPE_CHECKING:
    # Importa apenas para type checking para evitar dependências em tempo de execução
    from ..analytics.performance_analyzer import PerformanceAnalyzer

logger = get_logger(__name__)  # Usar o nome do módulo para o logger


def make_signal_df(signal: str, confidence: float, **extra: Any) -> pd.DataFrame:
    """Return a standardized DataFrame for trading signals.

    Parameters
    ----------
    signal
        Trading signal such as ``"buy"``, ``"sell"`` or ``"hold"``.
    confidence
        Value between 0 and 1 representing signal strength.
    extra
        Additional columns to include in the output.

    Returns
    -------
    pandas.DataFrame
        DataFrame with at least ``signal`` and ``confidence`` columns.
    """

    data = {"signal": [signal], "confidence": [confidence]}
    for key, value in extra.items():
        data[key] = [value]
    return pd.DataFrame(data)


def _extract_dataclass(annotation: Any) -> Optional[Type[Any]]:
    """Return dataclass type found in type annotation if any."""

    if annotation is inspect._empty:
        return None

    origin = get_origin(annotation)
    if origin is None:
        if isinstance(annotation, type) and is_dataclass(annotation):
            return annotation
        return None

    for arg in get_args(annotation):
        result = _extract_dataclass(arg)
        if result is not None:
            return result
    return None


def get_params_dataclass_for_alias(alias: str) -> Optional[Type[Any]]:
    """Return dataclass used for the ``params`` argument of the strategy.

    Parameters
    ----------
    alias : str
        Strategy alias registered in :class:`StrategyFactory`.

    Returns
    -------
    Optional[Type[Any]]
        Dataclass type if identified, otherwise ``None``.
    """

    strategy_cls = StrategyFactory.get_strategy_class(alias)
    if strategy_cls is None:
        return None

    try:
        sig = inspect.signature(strategy_cls.__init__)
        params_param = sig.parameters.get("params")
        if not params_param:
            return None
        return _extract_dataclass(params_param.annotation)
    except (TypeError, ValueError):
        return None


def load_strategy_class(class_name_to_load: str) -> Type[TradingStrategy]:
    """
    Carrega dinamicamente uma classe de estratégia com base em seu nome.

    Args:
        class_name_to_load: O nome da classe da estratégia a ser carregada.

    Returns:
        A classe da estratégia carregada.

    Raises:
        ValueError: Se a estratégia não for encontrada ou não for uma subclasse de TradingStrategy.
        ImportError: Se o módulo da estratégia não puder ser importado.
        AttributeError: Se a classe não for encontrada dentro do módulo.
    """
    strategy_cls = get_strategy_class_in_factory(class_name_to_load)
    if strategy_cls is not None:
        if not issubclass(strategy_cls, TradingStrategy):
            logger.error(
                f"Classe '{class_name_to_load}' obtida da fábrica mas não é subclasse de TradingStrategy."
            )
            raise ValueError(
                f"'{class_name_to_load}' obtida da fábrica não é uma TradingStrategy."
            )
        logger.info(
            f"Classe de estratégia '{class_name_to_load}' obtida diretamente da fábrica."
        )
        return strategy_cls

    # Fallback: tentar importar supondo que class_name_to_load seja caminho completo 'module.Class'
    if "." in class_name_to_load:
        module_name, cls_name = class_name_to_load.rsplit(".", 1)
        try:
            module = importlib.import_module(module_name)
        except ImportError as e:
            logger.error(
                f"Falha ao importar o módulo '{module_name}' para a estratégia '{class_name_to_load}': {e}"
            )
            raise
        try:
            loaded_class = getattr(module, cls_name)
        except AttributeError as e:
            logger.error(
                f"Classe '{cls_name}' não encontrada no módulo '{module_name}' após importação: {e}"
            )
            raise

        if not issubclass(loaded_class, TradingStrategy):
            logger.error(
                f"Classe '{cls_name}' no módulo '{module_name}' não é uma subclasse de TradingStrategy."
            )
            raise ValueError(
                f"'{cls_name}' em '{module_name}' não é uma TradingStrategy."
            )
        logger.info(
            f"Classe de estratégia '{class_name_to_load}' carregada com sucesso do módulo '{module_name}'."
        )
        return loaded_class

    logger.error(
        f"Estratégia '{class_name_to_load}' não encontrada na fábrica e não é um caminho de módulo válido."
    )

    class _TemporaryStrategy(TradingStrategy):
        """Estratégia neutra utilizada quando o alias não é encontrado."""

        strategy_name = class_name_to_load

        def __init__(
            self, *args: Any, **kwargs: Any
        ) -> None:  # pragma: no cover - stub
            logger.warning(
                "Alias de estratégia '%s' não encontrado. Utilizando _TemporaryStrategy.",
                class_name_to_load,
            )
            super().__init__(*args, **kwargs)
            self.received_market_data: List[pd.DataFrame] = []

        def _initialize_specific_parameters(self) -> None:  # pragma: no cover - stub
            pass

        def analyze_market(
            self,
            market_data: pd.DataFrame,
            *args: Any,
            **kwargs: Any,
        ) -> OrderDecision:  # pragma: no cover - stub
            self.received_market_data.append(market_data.copy())
            return OrderDecision(signal="HOLD", confidence=0.0)

        def backtest(
            self,
            market_data_map: Dict[str, pd.DataFrame],
            *args: Any,
            **kwargs: Any,
        ) -> dict:  # pragma: no cover - stub
            if self.symbol in market_data_map:
                self.received_market_data.append(market_data_map[self.symbol].copy())
            initial_capital = kwargs.get("initial_capital", 0.0)
            return {
                "final_capital": initial_capital,
                "trades_details": [],
                "total_fees": 0.0,
            }

    return _TemporaryStrategy


def create_strategy_and_qast_engine(
    strategy_alias: str,
    strategy_params: Optional[Dict[str, Any]] = None,
    qast_params: Optional[Dict[str, Any]] = None,
    shared_context: Optional[Dict[str, Any]] = None,
    config_manager: Optional[ConfigManager] = None,
) -> Tuple[Any, Any]:  # Idealmente: Tuple[TradingStrategy, QASTEvolutionaryStrategy]
    """
    Cria uma instância de uma estratégia de trading (via fábrica) e seu motor QAST associado.

    Args:
        strategy_alias:
            O alias da estratégia principal a ser criada (e.g., ``'NovaEstrategiaQUALIA'``).
        strategy_params:
            Dicionário de parâmetros para a estratégia principal.
        qast_params:
            Dicionário de parâmetros para o ``QASTEvolutionaryStrategy``.
        shared_context:
            Contexto compartilhado para inicializar a estratégia (pode incluir ``QASTController`` ou Metacognition).
        config_manager:
            Instância opcional de :class:`~qualia.config.config_manager.ConfigManager`.

    Returns:
        Uma tupla contendo a instância da estratégia e seu motor QAST.
    """
    factory = StrategyFactory()

    if strategy_params is None:
        strategy_params_dict = {}
    elif is_dataclass(strategy_params):
        strategy_params_dict = asdict(strategy_params)
    else:
        strategy_params_dict = strategy_params
        if isinstance(strategy_params_dict, dict):
            dataclass_type = get_params_dataclass_for_alias(strategy_alias)
            allowed_fields: List[str] = []
            if dataclass_type is not None:
                allowed_fields = list(dataclass_type.__annotations__.keys())

            if allowed_fields:
                unknown_keys = [
                    k for k in strategy_params_dict if k not in allowed_fields
                ]
                if unknown_keys:
                    logger.error(
                        "Parâmetros desconhecidos fornecidos para %s: %s",
                        dataclass_type.__name__ if dataclass_type else "params",
                        unknown_keys,
                    )
                    raise ValueError(f"Invalid parameter names: {unknown_keys}")

    actual_strategy_params = strategy_params_dict
    actual_qast_params = qast_params or {}
    actual_shared_context = shared_context or {}

    # Para importar QASTEvolutionaryStrategy apenas quando necessário e evitar ciclos de importação
    # no nível superior, podemos importá-lo aqui.
    try:
        from ..market.qast_evolutionary_strategy import (
            QASTEvolutionaryStrategy,
        )
    except ImportError:
        logger.error(
            "Falha ao importar QASTEvolutionaryStrategy. O motor QAST não será criado."
        )
        # Tratar o erro: ou levantar uma exceção ou retornar None para o motor qast
        # Neste caso, vamos prosseguir sem o motor QAST se não puder ser importado,
        # o que degradaria a funcionalidade, mas pode permitir que o sistema inicie.
        QASTEvolutionaryStrategy = None

    logger.info(
        "Criando estratégia '%s' com params: %s e contexto: %s",
        strategy_alias,
        list(actual_strategy_params.keys()),
        list(actual_shared_context.keys()),
    )

    main_strategy_instance = None
    try:
        main_strategy_instance = factory.create_strategy(
            alias=strategy_alias,
            params=actual_strategy_params,
            context=actual_shared_context,
            config_manager=config_manager,
        )
        logger.info(
            f"Estratégia '{strategy_alias}' ({getattr(main_strategy_instance, 'name', 'NomeDesconhecido')}) criada e inicializada pela fábrica."
        )
        if hasattr(main_strategy_instance, "initialize"):
            logger.info("Chamando initialize() para estratégia '%s'", strategy_alias)
            main_strategy_instance.initialize(actual_shared_context)
            logger.info(
                "Estratégia '%s' inicializada com sucesso via initialize()",
                strategy_alias,
            )
    except Exception as e:
        logger.error(
            f"Erro ao criar estratégia '{strategy_alias}' via fábrica: {e}",
            exc_info=True,
        )
        raise  # Re-levantar a exceção para que o chamador saiba da falha.

    if not main_strategy_instance:
        # Se a criação da estratégia falhou e não levantou exceção (improvável com o raise acima),
        # não podemos criar o motor QAST.
        logger.error(
            f"Instância da estratégia principal para '{strategy_alias}' não pôde ser criada. Motor QAST não será inicializado."
        )
        return None, None  # Ou levantar uma exceção

    qast_engine = None
    if QASTEvolutionaryStrategy:  # Prossiga apenas se a importação foi bem-sucedida
        logger.info(
            f"Inicializando QASTEvolutionaryStrategy para '{strategy_alias}' com template: {getattr(main_strategy_instance, 'name', 'NomeDesconhecido')}"
        )

        qast_population_size = actual_qast_params.get("population_size", 32)
        qast_base_mutation_strength = actual_qast_params.get(
            "base_mutation_strength", 0.1
        )
        qast_base_elite_ratio = actual_qast_params.get("base_elite_ratio", 0.2)
        qast_selective_pressure_adjustment = actual_qast_params.get(
            "selective_pressure_adjustment", 1.0
        )

        qast_engine = QASTEvolutionaryStrategy(
            strategy_template=main_strategy_instance.get_template_config(),
            qualia_consciousness=actual_shared_context.get(
                "qualia_consciousness_module"
            ),
            population_size=qast_population_size,
            base_mutation_strength=qast_base_mutation_strength,
            base_elite_ratio=qast_base_elite_ratio,
            config=qast_params,
            mode=actual_shared_context.get("trader_mode", "backtest"),
        )

        initial_mutation_for_pop = actual_qast_params.get(
            "initial_population_mutation_strength", qast_base_mutation_strength
        )
        qast_engine.initialize_population(mutation_strength=initial_mutation_for_pop)

        logger.info(
            f"QASTEvolutionaryStrategy para '{strategy_alias}' inicializada e população criada."
        )
    else:
        logger.error(
            f"QASTEvolutionaryStrategy não pôde ser importada. Motor QAST para '{strategy_alias}' não foi criado."
        )

    return main_strategy_instance, qast_engine


def copy_strategy(strategy_representation: Any) -> Any:
    """Return a deep copy of a strategy representation.

    Parameters
    ----------
    strategy_representation : Any
        Original strategy object or dictionary.

    Returns
    -------
    Any
        New instance with the same parameters.
    """

    runtime_attrs = [
        "dynamic_risk_controller",
        "_dynamic_risk_controller_initialized",
    ]

    memo: Dict[int, Any] = {}
    for attr in runtime_attrs:
        obj = getattr(strategy_representation, attr, None)
        if obj is not None:
            memo[id(obj)] = obj

    try:
        new_strategy = copy.deepcopy(strategy_representation, memo)
    except Exception:
        new_strategy = type(strategy_representation)()
        for attr, value in getattr(strategy_representation, "__dict__", {}).items():
            try:
                setattr(new_strategy, attr, copy.deepcopy(value, memo))
            except Exception:
                setattr(new_strategy, attr, value)

    for attr in runtime_attrs:
        if hasattr(strategy_representation, attr):
            setattr(new_strategy, attr, getattr(strategy_representation, attr))

    return new_strategy
