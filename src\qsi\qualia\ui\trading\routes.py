"""Flask Blueprint routes for the QUALIA trading interface."""

from __future__ import annotations

from datetime import datetime, timezone
from flask import Blueprint, render_template, jsonify, request, abort, Response
from flask_wtf.csrf import validate_csrf
from typing import Any, Dict, List

from ...utils.logger import get_logger

from .state import TRADING_STATE

# Funções de simulação utilizadas apenas em ambientes de desenvolvimento
from ...market.data_simulators import (
    generate_mock_market_data,
    simulate_positions,
    simulate_trade_history,
    simulate_performance_metrics,
)

logger = get_logger(__name__)

trading_bp = Blueprint(
    "trading",
    __name__,
    url_prefix="/trading",
    template_folder="../../templates/trading",
)


@trading_bp.before_request
def enforce_csrf() -> None:
    if request.method in {"POST", "PUT", "DELETE", "PATCH"}:
        from flask import current_app

        if not current_app.secret_key:
            return
        token = request.headers.get("X-CSRFToken")
        try:
            validate_csrf(token)
        except Exception:
            abort(400, "CSRF token inválido")


try:
    from ...market.kraken_integration import KrakenIntegration
    from ...market.trading_engine import TradingEngine

    MODULES_LOADED = True
except ImportError as e:  # pragma: no cover - optional dependency
    logger.warning(f"Aviso de importação: {e}")
    MODULES_LOADED = False

TRADING_ENGINE = None
DEFAULT_SYMBOLS = ["BTC/USD", "ETH/USD", "XRP/USD", "ADA/USD", "SOL/USD"]


def set_trading_state(state: "TradingState") -> None:
    """Inject custom ``TradingState`` instance for testing."""

    global TRADING_STATE
    TRADING_STATE = state


def set_trading_engine(engine: Any | None) -> None:
    """Inject custom trading engine for testing."""

    global TRADING_ENGINE
    TRADING_ENGINE = engine


@trading_bp.route("/")
def trading_dashboard() -> str:
    return render_template("trading/index.html")


@trading_bp.route("/api/status")
def get_trading_status() -> Response:
    if not TRADING_STATE["last_update"]:
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()
    return jsonify(
        {
            "status": TRADING_STATE["status"],
            "last_update": TRADING_STATE["last_update"],
            "error_message": TRADING_STATE["error_message"],
            "api_configured": TRADING_STATE["api_configured"],
            "live_mode": TRADING_STATE["live_mode"],
            "performance_metrics": TRADING_STATE["performance_metrics"],
            "quantum_metrics": TRADING_STATE["quantum_metrics"],
        }
    )


@trading_bp.route("/api/pairs")
def get_trading_pairs() -> Response:
    pairs = [
        "BTC/USD",
        "ETH/USD",
        "SOL/USD",
        "XRP/USD",
        "ADA/USD",
        "LINK/USD",
        "DOT/USD",
        "DOGE/USD",
        "AVAX/USD",
        "MATIC/USD",
        "UNI/USD",
        "ATOM/USD",
        "LTC/USD",
        "BCH/USD",
        "XLM/USD",
    ]
    if TRADING_STATE["exchange"] is not None:
        try:
            markets = TRADING_STATE["exchange"].load_markets()
            pairs = sorted(markets.keys())
        except Exception as e:  # pragma: no cover - external dependency
            logger.error(f"Erro ao obter pares de trading: {e}")
    return jsonify({"pairs": pairs})


@trading_bp.route("/api/initialize", methods=["POST"])
def initialize_trading() -> Response:
    global TRADING_ENGINE
    data = request.json
    if not data.get("api_key") or not data.get("api_secret"):
        return jsonify(
            {"success": False, "message": "API Key e Secret são obrigatórios."}
        )
    if not data.get("symbols"):
        return jsonify(
            {"success": False, "message": "Selecione pelo menos um par de trading."}
        )

    TRADING_STATE["api_key"] = data["api_key"]
    TRADING_STATE["api_secret"] = data["api_secret"]
    TRADING_STATE["symbols"] = data["symbols"]
    TRADING_STATE["risk_profile"] = data.get("risk_profile", "balanced")
    TRADING_STATE["initial_capital"] = data.get("initial_capital", 10000)
    TRADING_STATE["live_mode"] = data.get("live_mode", False)
    TRADING_STATE["api_configured"] = True

    if MODULES_LOADED:
        if TRADING_ENGINE is not None:
            try:
                TRADING_ENGINE.stop()
            except Exception:  # pragma: no cover - stop may fail in dev
                pass
        TRADING_ENGINE = TradingEngine(
            api_key=data["api_key"],
            api_secret=data["api_secret"],
            symbols=data["symbols"],
            initial_capital=data.get("initial_capital", 10000),
            risk_profile=data.get("risk_profile", "balanced"),
            use_quantum_analysis=data.get("use_quantum", True),
            live_mode=data.get("live_mode", False),
        )
    TRADING_STATE["status"] = "initialized"
    TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

    if not MODULES_LOADED:
        TRADING_STATE["market_data"] = generate_mock_market_data(
            TRADING_STATE["symbols"]
        )
        TRADING_STATE["positions"] = []
        TRADING_STATE["trade_history"] = []

    return jsonify(
        {
            "success": True,
            "message": "Sistema de trading inicializado com sucesso.",
            "status": TRADING_STATE["status"],
        }
    )
