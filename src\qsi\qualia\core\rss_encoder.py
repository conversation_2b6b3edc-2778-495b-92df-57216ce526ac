"""RSS/Text Quantum Encoders

`RSSTextSentimentEncoder` converte a polaridade de texto (notícia, tweet,
headline RSS) em uma rotação `Ry` de 1 qubit.

Snapshot esperado::

    {
        "text": str,          # título ou conteúdo
        "sentiment": float,  # opcional, intervalo [-1, 1]
        "timestamp": float   # opcional
    }

Se `sentiment` não for fornecido, aplica-se um heurístico simples baseado em
palavras positivas/negativas. Não requer bibliotecas externas pesadas.
"""

from __future__ import annotations

import re
from typing import Any, Dict, Sequence

import random

from sklearn.feature_extraction.text import TfidfVectorizer

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
except Exception:  # pragma: no cover - optional dependency
    SentimentIntensityAnalyzer = None  # type: ignore[misc]

import numpy as np

from qualia.config import encoder_registry as _er
from ..utils.logger import get_logger

# Use same QuantumEncoder object used by registry to avoid duplicates
QuantumEncoder = _er.QuantumEncoder

logger = get_logger(__name__)

# ---------------------------------------------------------------------------
# RSSTextSentimentEncoder
# ---------------------------------------------------------------------------


class RSSTextSentimentEncoder(QuantumEncoder):
    """Encoder de sentimento textual para um único qubit (Ry).

    Converte a polaridade no range [-1, 1] → [0, 1] e mapeia em θ = π/2 * x.
    """

    # Palavras com pesos associados para uma análise mais granular
    _WORD_WEIGHTS = {
        # Positivas (pesos > 0)
        "good": 0.5,
        "great": 0.75,
        "positive": 0.6,
        "up": 0.4,
        "gain": 0.6,
        "improve": 0.5,
        "growth": 0.7,
        "success": 0.8,
        "record": 0.7,
        "bullish": 0.8,
        "buy": 0.6,
        "strong": 0.6,
        "breaking": 0.5,
        "optimistic": 0.7,
        "rally": 0.6,
        "surge": 0.7,
        "outperform": 0.8,
        # Negativas (pesos < 0)
        "bad": -0.5,
        "negative": -0.6,
        "down": -0.4,
        "loss": -0.6,
        "decline": -0.7,
        "fail": -0.8,
        "crisis": -0.9,
        "drop": -0.6,
        "bearish": -0.8,
        "sell": -0.6,
        "weak": -0.6,
        "fear": -0.7,
        "risk": -0.5,
        "volatile": -0.4,
        "crash": -0.9,
    }

    _NEGATION_WORDS = {"not", "no", "never", "without", "failed"}

    _INTENSIFIERS = {
        "very": 1.5,
        "extremely": 2.0,
        "highly": 1.8,
        "major": 1.5,
        "significant": 1.7,
        "substantially": 1.6,
        "slightly": 0.5,
        "partially": 0.6,
        "somewhat": 0.7,
    }

    def __init__(self, name: str = "RSSTextSentimentEncoder") -> None:  # noqa: D401
        super().__init__(name)
        self.analyzer = (
            SentimentIntensityAnalyzer() if SentimentIntensityAnalyzer else None
        )
        self._vectorizer = TfidfVectorizer(vocabulary=list(self._WORD_WEIGHTS.keys()))
        # Ajusta idf inicial para a lista predefinida de palavras
        self._vectorizer.fit([""])

    # ---------------------------- Helpers ----------------------------------

    def _advanced_tokenization(self, text: str) -> list[str]:
        """Tokeniza o texto em palavras."""
        return re.findall(r"[A-Za-z]+", text.lower())

    def _contextual_analysis(self, tokens: list[str]) -> list[float]:
        """Analisa o contexto, aplicando negação e intensificadores."""
        scores = []
        negation_active = False
        intensifier = 1.0

        for i, token in enumerate(tokens):
            if token in self._NEGATION_WORDS:
                negation_active = True
                continue

            if token in self._INTENSIFIERS:
                intensifier = self._INTENSIFIERS[token]
                continue

            if token in self._WORD_WEIGHTS:
                score = self._WORD_WEIGHTS[token] * intensifier
                if negation_active:
                    score *= -1
                scores.append(score)

            # Resetar estado para o próximo token
            negation_active = False
            intensifier = 1.0
        return scores

    def _weighted_sentiment(self, scores: list[float]) -> float:
        """Calcula o sentimento final a partir dos scores ponderados."""
        if not scores:
            return 0.0

        total_score = sum(scores)
        # Normalização baseada na soma dos pesos absolutos para evitar distorções
        # e dar um score mais representativo da intensidade geral.
        sum_of_abs_weights = sum(abs(s) for s in scores)
        if sum_of_abs_weights == 0:
            return 0.0

        # O sentimento é a média dos scores, efetivamente.
        # Clip para garantir que o resultado final permaneça em [-1, 1]
        sentiment = np.clip(total_score / len(scores) if scores else 0.0, -1.0, 1.0)
        return float(sentiment)

    def _estimate_sentiment(self, text: str) -> float:
        """Estima o sentimento usando a nova pipeline de análise."""
        tokens = self._advanced_tokenization(text)
        sentiment_scores = self._contextual_analysis(tokens)
        return self._weighted_sentiment(sentiment_scores)

    def _tfidf_polarity(self, text: str) -> float:
        """Estimativa de polaridade via TF-IDF.

        Parameters
        ----------
        text : str
            Texto a ser analisado.

        Returns
        -------
        float
            Valor de polaridade no intervalo ``[-1, 1]``.

        Notes
        -----
        Essa abordagem funciona como *fallback* quando a análise principal
        retorna um valor próximo de ``0``. Ao ponderar as palavras por TF-IDF,
        há maior evidência das mais relevantes, quebrando empates de textos
        aparentemente neutros.
        """
        tfidf = self._vectorizer.transform([text])
        feats = self._vectorizer.get_feature_names_out()
        weights = np.array([self._WORD_WEIGHTS.get(w, 0.0) for w in feats])
        score = float(tfidf.toarray().dot(weights)[0])
        return float(np.clip(score, -1.0, 1.0))

    def _get_sentiment(self, snap: Dict[str, Any]) -> float:
        """Obtém a polaridade de ``snap``.

        Parameters
        ----------
        snap : dict
            Snapshot contendo ``"text"`` ou ``"sentiment"``.

        Returns
        -------
        float
            Valor no intervalo ``[-1, 1]``.

        Notes
        -----
        Sempre que a análise direta gera um resultado muito próximo de ``0``,
        aplica-se ``_tfidf_polarity`` para refinar a estimativa e evitar um
        sentimento neutro por falta de contexto.
        """
        if "sentiment" in snap and snap["sentiment"] is not None:
            score = float(np.clip(snap["sentiment"], -1.0, 1.0))
        else:
            text = str(snap.get("text", ""))
            if self.analyzer is not None:
                try:
                    score = float(self.analyzer.polarity_scores(text)["compound"])
                except Exception:  # pragma: no cover - analyzer errors
                    score = self._estimate_sentiment(text)
            else:
                score = self._estimate_sentiment(text)

        if abs(score) < 0.05:
            text = str(snap.get("text", ""))
            score = self._tfidf_polarity(text)

        return score

    # ----------------------------- Encode ----------------------------------

    def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:  # type: ignore[override]
        """Codifica ``snap`` em um vetor de amplitudes.

        Parameters
        ----------
        snap : dict
            Snapshot com ``"text"`` ou ``"sentiment"``.

        Returns
        -------
        numpy.ndarray
            Vetor ``[cos(theta), sin(theta)]`` representando a rotação ``Ry``.

        Notes
        -----
        Quando o sentimento calculado é ``0``, uma perturbação aleatória é
        aplicada para injetar entropia no estado, evitando amplitudes
        determinísticas que poderiam enviesar amostragens futuras.
        """
        sentiment = self._get_sentiment(snap)
        if sentiment == 0.0:
            return np.array(
                [
                    random.gauss(0.7, 0.01),
                    random.gauss(0.7, 0.01),
                ],
                dtype=np.float32,
            )

        # Normaliza [-1,1] → [0,1]
        norm = (sentiment + 1.0) / 2.0
        theta = norm * (np.pi / 2.0)
        return np.array([np.cos(theta), np.sin(theta)], dtype=np.float32)

    def _encode_batch(self, snaps: Sequence[Dict[str, Any]]) -> np.ndarray:  # type: ignore[override]
        """Codifica uma sequência de snapshots.

        Parameters
        ----------
        snaps : Sequence[dict]
            Lista de snapshots com texto ou sentimentos pré-calculados.

        Returns
        -------
        numpy.ndarray
            Array ``(n, 2)`` com os vetores normalizados de cada item.

        Notes
        -----
        A mesma perturbação aleatória aplicada em :meth:`_encode_single` é usada
        aqui quando o sentimento é ``0`` para garantir variabilidade estatística
        nos estados retornados.
        """
        sentiments = np.array([self._get_sentiment(s) for s in snaps], dtype=np.float32)
        out = []
        for sentiment in sentiments:
            if sentiment == 0.0:
                out.append([random.gauss(0.7, 0.01), random.gauss(0.7, 0.01)])
            else:
                norm = (sentiment + 1.0) / 2.0
                theta = norm * (np.pi / 2.0)
                out.append([np.cos(theta), np.sin(theta)])
        return np.array(out, dtype=np.float32)

    def get_quantum_operation(self, snap: Dict[str, Any]):  # type: ignore[override]
        s = self._get_sentiment(snap)
        theta = ((s + 1.0) / 2.0) * (np.pi / 2.0)
        return ("ry", [float(theta)], [0])


# ---------------------------------------------------------------------------
# Registro
# ---------------------------------------------------------------------------

_er.register_encoder("quantum_rss_sentiment", RSSTextSentimentEncoder)
logger.info("Text encoder registrado: quantum_rss_sentiment")
