"""Utilities for simulating return paths."""

from __future__ import annotations

import importlib.util
import numpy as np

try:  # pragma: no cover - optional acceleration
    import numba
except Exception:  # pragma: no cover - missing optional dependency
    numba = None

__all__ = ["simulate_returns", "simulate_paths", "simulate_quantum_paths"]


def _simulate_returns_core(
    mu: float, sigma: float, horizon: int, num_paths: int
) -> np.ndarray:
    """Core implementation for generating random return paths."""
    return np.random.normal(loc=mu, scale=sigma, size=(num_paths, horizon))


if numba is not None:  # pragma: no cover - optional acceleration
    _simulate_returns_jit = numba.njit(parallel=True)(_simulate_returns_core)
else:  # pragma: no cover - optional acceleration
    _simulate_returns_jit = None

# Detect optional GPU library
CUPY_AVAILABLE = importlib.util.find_spec("cupy") is not None


def simulate_returns(
    mu: float, sigma: float, horizon: int, num_paths: int, parallel: bool = False
) -> np.ndarray:
    """Generate random return paths using a normal distribution.

    Parameters
    ----------
    mu
        Mean of the distribution of returns.
    sigma
        Standard deviation of returns.
    horizon
        Number of time steps for each simulated path.
    num_paths
        Number of independent paths to generate.
    parallel
        If ``True`` and Numba is available, use JIT-compiled parallel version.

    Returns
    -------
    numpy.ndarray
        Array of shape ``(num_paths, horizon)`` containing the simulated returns.
    """

    mu = float(mu)
    sigma = float(sigma)
    horizon = int(horizon)
    num_paths = int(num_paths)

    if parallel and CUPY_AVAILABLE:
        import cupy as cp

        cp_rng = cp.random.normal(loc=mu, scale=sigma, size=(num_paths, horizon))
        return cp.asnumpy(cp_rng)

    if parallel and _simulate_returns_jit is not None:
        return _simulate_returns_jit(mu, sigma, horizon, num_paths)

    return _simulate_returns_core(mu, sigma, horizon, num_paths)


def simulate_paths(mu: float, sigma: float, horizon: int, num_paths: int) -> np.ndarray:
    """Generate cumulative return trajectories.

    This function builds upon :func:`simulate_returns` and returns the
    cumulative sum of the generated returns for each path.

    Parameters
    ----------
    mu
        Mean of the distribution of returns.
    sigma
        Standard deviation of returns.
    horizon
        Number of time steps for each simulated path.
    num_paths
        Number of independent paths to generate.

    Returns
    -------
    numpy.ndarray
        Array of shape ``(num_paths, horizon)`` containing the cumulative
        trajectories.
    """

    returns = simulate_returns(mu=mu, sigma=sigma, horizon=horizon, num_paths=num_paths)
    return np.cumsum(returns, axis=1)


def simulate_quantum_paths(
    mu: float,
    sigma: float,
    horizon: int,
    num_paths: int,
) -> np.ndarray:
    """Generate return trajectories using Qiskit's quantum RNG.

    Parameters
    ----------
    mu
        Mean of the distribution of returns.
    sigma
        Standard deviation of returns.
    horizon
        Number of time steps for each simulated path.
    num_paths
        Number of independent paths to generate.

    Returns
    -------
    numpy.ndarray
        Array of shape ``(num_paths, horizon)`` containing the simulated returns.
    """

    if importlib.util.find_spec("qiskit") is None:
        raise ImportError("simulate_quantum_paths requires qiskit to be installed")

    from qiskit.utils import algorithm_globals

    return algorithm_globals.random.normal(
        loc=mu, scale=sigma, size=(num_paths, horizon)
    )
