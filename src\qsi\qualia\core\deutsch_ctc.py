import numpy as np
from qiskit.quantum_info import partial_trace, DensityMatrix, Statevector


def _convert_rho_cr(
    rho_cr_input: np.ndarray | DensityMatrix | Statevector, dim_cr: int
) -> DensityMatrix:
    """Return ``rho_cr_input`` as :class:`DensityMatrix` validating dimensions."""
    if isinstance(rho_cr_input, Statevector):
        rho_cr = DensityMatrix(rho_cr_input)
    elif isinstance(rho_cr_input, np.ndarray):
        try:
            rho_cr = DensityMatrix(rho_cr_input)
        except Exception as e:
            raise ValueError(
                f"Falha ao converter rho_cr_input (ndarray) para DensityMatrix: {e}"
            )
    elif isinstance(rho_cr_input, DensityMatrix):
        rho_cr = rho_cr_input
    else:
        raise TypeError(
            "rho_cr_input deve ser np.ndarray, qiskit Statevector ou DensityMatrix."
        )

    if getattr(rho_cr, "data", None) is not None:
        if rho_cr.data.shape != (dim_cr, dim_cr):
            raise ValueError(
                f"rho_cr.data.shape inconsistente. Esperado ({dim_cr}, {dim_cr}), obtido {rho_cr.data.shape}"
            )
    elif rho_cr.dim != dim_cr:
        raise ValueError(
            f"rho_cr.dim inconsistente. Esperado {dim_cr}, obtido {rho_cr.dim}"
        )
    return rho_cr


def _iterate_once(
    rho_cr: DensityMatrix,
    rho_ctc_guess: DensityMatrix,
    U_interaction_matrix: np.ndarray,
    qargs_cr: list[int],
    verbose: bool,
    iteration: int,
    tol: float,
) -> tuple[DensityMatrix | None, bool]:
    """Perform one iteration of the Deutsch CTC fixed-point search."""
    rho_total_guess = rho_cr.tensor(rho_ctc_guess)
    rho_total_evolved = rho_total_guess.evolve(U_interaction_matrix)
    rho_ctc_next = partial_trace(rho_total_evolved, qargs_cr)

    if not rho_ctc_next.is_valid():
        if verbose:
            logger.debug(
                "Iteração %s: rho_ctc_next não é válida. Tentando normalizar.",
                iteration + 1,
            )
        trace_val = np.trace(rho_ctc_next.data)
        if np.abs(trace_val) > 1e-9:
            rho_ctc_next = DensityMatrix(rho_ctc_next.data / trace_val)
        else:
            if verbose:
                logger.error(
                    "Iteração %s: Traço de rho_ctc_next é zero. Abortando.",
                    iteration + 1,
                )
            return None, False

    diff_norm = np.linalg.norm(rho_ctc_next.data - rho_ctc_guess.data, ord="fro")
    if verbose:
        logger.debug(
            "Iteração %s/%s: Norma da diferença = %.2e",
            iteration + 1,
            iteration + 1,
            diff_norm,
        )
    converged = diff_norm < tol
    return rho_ctc_next, converged
from ..utils.logger import get_logger

logger = get_logger(__name__)


def solve_deutsch_ctc(
    rho_cr_input: np.ndarray | DensityMatrix | Statevector,
    U_interaction_matrix: np.ndarray,
    num_cr_qubits: int,
    num_ctc_qubits: int,
    tol: float = 1e-7,
    max_iter: int = 100,
    verbose: bool = False,
) -> DensityMatrix | None:
    """
    Resolve a condição de auto-consistência de Deutsch para um Closed Timelike Curve (CTC).

    A equação é: ρ_ctc = Tr_CR[ U_interaction * (ρ_cr ⊗ ρ_ctc_guess) * U_interaction† ]

    Args:
        rho_cr_input: Matriz de densidade do sistema cronológico (CR).
                      Pode ser um np.ndarray, qiskit DensityMatrix ou Statevector.
        U_interaction_matrix: Matriz unitária da interação entre CR e CTC.
                              Deve ter dimensões (2**(num_cr_qubits + num_ctc_qubits), 2**(num_cr_qubits + num_ctc_qubits)).
        num_cr_qubits: Número de qubits no sistema CR.
        num_ctc_qubits: Número de qubits no sistema CTC.
        tol: Tolerância para convergência da norma de Frobenius.
        max_iter: Número máximo de iterações.
        verbose: Se True, imprime informações de convergência.

    Returns:
        A matriz de densidade ρ_ctc que satisfaz a condição, ou None se não convergir.
    """
    dim_cr = 2**num_cr_qubits
    dim_ctc = 2**num_ctc_qubits

    if not isinstance(
        U_interaction_matrix, np.ndarray
    ) or U_interaction_matrix.shape != (dim_cr * dim_ctc, dim_cr * dim_ctc):
        raise ValueError(
            f"U_interaction_matrix deve ser um np.ndarray com shape ({dim_cr * dim_ctc}, {dim_cr * dim_ctc})"
        )

    rho_cr = _convert_rho_cr(rho_cr_input, dim_cr)

    # Inicializa ρ_ctc_guess (estado maximamente misto)
    rho_ctc_guess = DensityMatrix(np.eye(dim_ctc) / dim_ctc)

    U_dag = U_interaction_matrix.conj().T

    # Qubits do sistema CR são os primeiros
    qargs_cr = list(range(num_cr_qubits))

    for i in range(max_iter):
        rho_ctc_next, converged = _iterate_once(
            rho_cr,
            rho_ctc_guess,
            U_interaction_matrix,
            qargs_cr,
            verbose,
            i,
            tol,
        )

        if rho_ctc_next is None:
            return None

        if converged:
            if verbose:
                logger.info("Convergência alcançada na iteração %s.", i + 1)
            if not rho_ctc_next.is_valid(atol=1e-3):
                logger.warning(
                    "Aviso: Solução convergida rho_ctc não é estritamente válida."
                )
            return rho_ctc_next

        rho_ctc_guess = rho_ctc_next

    if verbose:
        logger.warning("Número máximo de iterações atingido sem convergência.")
    return None


if __name__ == "__main__":
    # Exemplo de teste simples
    logger.info("Iniciando teste de solve_deutsch_ctc...")

    # Parâmetros
    N_CR_QUBITS = 1
    N_CTC_QUBITS = 1

    # Estado inicial para CR (ex: |0⟩⟨0|)
    psi_cr = np.zeros(2**N_CR_QUBITS)
    psi_cr[0] = 1.0
    rho_cr_test = DensityMatrix(np.outer(psi_cr, psi_cr.conj()))
    logger.info("\nρ_CR inicial:\n%s", rho_cr_test.data)

    # Operador de interação U (ex: CNOT entre CR (controle) e CTC (alvo))
    # Qubit CR é q0, Qubit CTC é q1
    # CNOT = |0⟩⟨0| ⊗ I + |1⟩⟨1| ⊗ X
    I_ctc = np.eye(2**N_CTC_QUBITS)
    X_ctc = np.array([[0, 1], [1, 0]])  # Para 1 qubit CTC

    P0_cr = np.array([[1, 0], [0, 0]])  # |0⟩⟨0| para CR
    P1_cr = np.array([[0, 0], [0, 1]])  # |1⟩⟨1| para CR

    # U_CNOT = np.kron(P0_cr, I_ctc) + np.kron(P1_cr, X_ctc)
    # Usando Qiskit para construir o CNOT para evitar erros de ordenação de
    # kron
    from qiskit import QuantumCircuit
    from qiskit.quantum_info import Operator

    qc_interaction = QuantumCircuit(N_CR_QUBITS + N_CTC_QUBITS)
    # CR qubit é o controle (índice 0), CTC qubit é o alvo (índice N_CR_QUBITS)
    qc_interaction.cx(0, N_CR_QUBITS)
    U_test = Operator(qc_interaction).data
    logger.info("\nMatriz de Interação U (CNOT CR->CTC):\n%s", U_test)

    # Resolver para ρ_CTC
    logger.info("\nResolvendo para ρ_CTC...")
    rho_ctc_solution = solve_deutsch_ctc(
        rho_cr_test, U_test, N_CR_QUBITS, N_CTC_QUBITS, verbose=True, tol=1e-7
    )

    if rho_ctc_solution is not None:
        logger.info(
            "\nSolução ρ_CTC encontrada:\n%s",
            np.around(rho_ctc_solution.data, decimals=5),
        )
        logger.info("Traço de ρ_CTC: %.5f", np.trace(rho_ctc_solution.data))
        logger.info("É hermitiana? %s", rho_ctc_solution.is_hermitian())

        # Verificar a condição de ponto fixo manualmente (para um passo)
        rho_total_guess_final = rho_cr_test.tensor(rho_ctc_solution)
        rho_total_evolved_final = rho_total_guess_final.evolve(U_test)
        rho_ctc_check = partial_trace(rho_total_evolved_final, list(range(N_CR_QUBITS)))

        logger.info(
            "\nVerificação de ponto fixo (Tr_CR(U(ρ_CR ⊗ ρ_CTC_sol)U†)):\n%s",
            np.around(rho_ctc_check.data, decimals=5),
        )
        diff_check = np.linalg.norm(
            rho_ctc_solution.data - rho_ctc_check.data, ord="fro"
        )
        logger.info("Norma da diferença na verificação: %.2e", diff_check)

        # Teste específico: se CR = |0><0| e U=CNOT, então ρ_CTC = ρ_CTC_guess, ou seja, não evolui
        # pois U se torna I_CR ⊗ I_CTC. A solução deveria ser maximamente misturada se essa for a partida.
        # No entanto, a teoria de Deutsch para CNOT é mais sutil.
        # Para ρ_CR = |0⟩⟨0|, o CNOT não faz nada com o alvo, então ρ_CTC = Tr_CR[ρ_CR ⊗ ρ_CTC] = ρ_CTC.
        # Se ρ_CTC inicial for maximamente misto, ele permanece maximamente
        # misto.

        # Teste com CR = |+⟩⟨+|
        psi_plus_cr = np.array([1, 1]) / np.sqrt(2)
        rho_plus_cr_test = DensityMatrix(np.outer(psi_plus_cr, psi_plus_cr.conj()))
        logger.info("\n--- Teste com ρ_CR = |+⟩⟨+| --- ")
        logger.info("ρ_CR = |+⟩⟨+|:\n%s", rho_plus_cr_test.data)

        rho_ctc_plus_solution = solve_deutsch_ctc(
            rho_plus_cr_test, U_test, N_CR_QUBITS, N_CTC_QUBITS, verbose=True, tol=1e-7
        )
        if rho_ctc_plus_solution is not None:
            logger.info(
                "\nSolução ρ_CTC para ρ_CR = |+⟩⟨+|:\n%s",
                np.around(rho_ctc_plus_solution.data, decimals=5),
            )
            # Para U=CNOT e ρ_CR = |+⟩⟨+|, a ρ_CTC resultante deve ser |0⟩⟨0|
            # Ver Deutsch, D. (1991). Quantum mechanics near closed timelike
            # lines. Physical Review D, 44(10), 3197–3217. Fig 2.
            expected_rho_ctc_data = np.array([[1, 0], [0, 0]], dtype=complex)
            diff_from_expected = np.linalg.norm(
                rho_ctc_plus_solution.data - expected_rho_ctc_data, ord="fro"
            )
            logger.info(
                "Norma da diferença para o esperado (|0⟩⟨0|): %.2e",
                diff_from_expected,
            )
            assert diff_from_expected < 1e-5, "Resultado para ρ_CR=|+><+| não é |0><0|"

    else:
        logger.warning("\nNão foi possível encontrar uma solução para ρ_CTC.")
