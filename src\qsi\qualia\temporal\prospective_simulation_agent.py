from __future__ import annotations

"""Generate prospective market scenarios for retrocausal strategies.

This module samples possible future paths from historical price series and
publishes them on the event bus.  The results are also persisted to
``nexus_coherence_history.jsonl`` so that retrocausal strategies can evaluate
coherence between anticipated and realized market behavior.
"""

from dataclasses import dataclass
from datetime import datetime, timezone
import json
import os
from typing import Sequence, List

import numpy as np
from scipy.stats import norm

# Import the lightweight event bus directly to avoid heavy dependencies
from typing import Protocol, Any


class EventBusProtocol(Protocol):
    """Simplified interface expected from an event bus."""

    def publish(self, name: str, payload: Any) -> None:
        ...


from ..utils.logger import get_logger

logger = get_logger(__name__)

PROSPECTIVE_EVENT = "temporal.prospective_scenarios"
_HISTORY_PATH = os.getenv("QUALIA_NEXUS_HISTORY", "nexus_coherence_history.jsonl")


@dataclass
class ProspectiveScenarioEvent:
    """Payload containing simulated future paths."""

    scenarios: List[List[float]]
    timestamp: float


class ProspectiveSimulationAgent:
    """Generate and publish prospective market scenarios."""

    def __init__(
        self,
        event_bus: EventBusProtocol,
        *,
        horizon: int = 10,
        num_samples: int = 20,
        history_path: str | None = None,
    ) -> None:
        self.event_bus = event_bus
        self.horizon = horizon
        self.num_samples = num_samples
        self.history_path = history_path or _HISTORY_PATH
        os.makedirs(os.path.dirname(self.history_path) or ".", exist_ok=True)

    # ------------------------------------------------------------------
    def _simulate(self, history: Sequence[float]) -> List[List[float]]:
        hist = np.asarray(history, dtype=float)
        if hist.size < 2:
            raise ValueError("history must contain at least two observations")

        returns = np.diff(np.log(hist))
        mu = float(np.mean(returns))
        sigma = float(np.std(returns, ddof=1))
        sigma = sigma if sigma > 0 else 1e-6

        scenarios: List[List[float]] = []
        for _ in range(self.num_samples):
            rnd = norm.rvs(loc=mu, scale=sigma, size=self.horizon)
            path = [float(hist[-1])]
            for r in rnd:
                path.append(path[-1] * float(np.exp(r)))
            scenarios.append(path[1:])
        return scenarios

    # ------------------------------------------------------------------
    def generate(self, history: Sequence[float]) -> List[List[float]]:
        """Simulate and share anticipated market paths.

        The generated scenarios are dispatched via the event bus so that
        retrocausal strategies can incorporate them when evaluating potential
        trades.  Each generated batch is also appended to
        ``nexus_coherence_history.jsonl`` for later analysis of coherence
        between simulated and real outcomes.
        """
        scenarios = self._simulate(history)
        ts = datetime.now(timezone.utc).timestamp()
        payload = ProspectiveScenarioEvent(scenarios=scenarios, timestamp=ts)

        try:
            self.event_bus.publish(PROSPECTIVE_EVENT, payload)
        except Exception as exc:  # pragma: no cover - defensive
            logger.warning("Failed to publish prospective scenarios: %s", exc)

        record = {"type": "prospective", "timestamp": ts, "samples": scenarios}
        with open(self.history_path, "a", encoding="utf-8") as fh:
            fh.write(json.dumps(record, ensure_ascii=False) + "\n")
        return scenarios


__all__ = [
    "PROSPECTIVE_EVENT",
    "ProspectiveScenarioEvent",
    "ProspectiveSimulationAgent",
]
