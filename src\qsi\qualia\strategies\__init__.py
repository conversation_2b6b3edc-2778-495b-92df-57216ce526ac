"""
Módulo de Estratégias do QUALIA Framework

Este módulo implementa o padrão Strategy para algoritmos de trading,
permitindo que diferentes estratégias sejam encapsuladas, tornando-as
intercambiáveis e independentes do código cliente que as utiliza.

Benefícios:
1. Desacoplamento entre contexto e algoritmos
2. Fácil adição de novas estratégias sem modificar código existente
3. Troca dinâmica de estratégias em tempo de execução
4. Melhor testabilidade de componentes isolados
"""

# Integrações pendentes
# -----------------------
# As estratégias deverão, no futuro, acionar os operadores conceituais
# definidos em ``qualia.core.operator_interfaces`` assim que as
# implementações estiverem disponíveis.

# As funções legadas do módulo ``registry`` (get_strategy_by_name, list_available_strategies, etc.)
# serão removidas. A funcionalidade deve ser acessada via ``StrategyFactory``.
# import qualia.strategies.registry  # DEPRECATED

from ..strategies.strategy_interface import (
    TradingStrategy,
    TradingContext,
    register_strategy,
)

# Fábrica Moderna de Estratégias
from ..strategies.strategy_factory import StrategyFactory

# Utilitários para estratégias (se houver funções comuns)
from ..strategies.strategy_utils import create_strategy_and_qast_engine

# Importar estratégias específicas para que sejam registradas pela StrategyFactory
# via o decorador @register_strategy.

# Estratégia mais recente do sistema
from ..strategies.nova_estrategia_qualia import QualiaTSVFStrategy
from ..strategies.exceptions import DataRequirementsError
from ..strategies.meta_quantum_controller import MetaQuantumController
from ..strategies.adaptive_liquidity_manager import AdaptiveLiquidityManager
from ..strategies.coherence_aggregator import CoherenceAggregator
from .enhanced_quantum_momentum import EnhancedQuantumMomentumStrategy

# Importar a estratégia composta para registro automático
from ..strategies.composite import CompositeStrategy

# Estratégias Legadas não são mais importadas aqui para registro automático.
# O registro deve ocorrer via ``StrategyFactory`` ou importação direta do módulo da
# estratégia que usa o decorador ``@register_strategy``.
# Em especial, ``scalping_strategies_original`` foi movido para ``strategies/legacy``
# e deve ser importado manualmente caso seja necessário.

# Expor seletivamente para o exterior do pacote
__all__ = [
    "TradingStrategy",
    "TradingContext",
    "register_strategy",
    "StrategyFactory",
    "create_strategy_and_qast_engine",
    # Novas Estratégias
    "QualiaTSVFStrategy",
    "CompositeStrategy",
    "MetaQuantumController",
    "AdaptiveLiquidityManager",
    "CoherenceAggregator",
    "EnhancedQuantumMomentumStrategy",
    # As funções legadas do registry (get_strategy_by_name, etc.) foram removidas de __all__.
    # Os consumidores devem usar StrategyFactory.
]

# Opcional: Chamar uma função para registrar todas as estratégias baseadas em arquivos
# se o seu sistema de registro depender disso e não apenas de importações.
# Por exemplo, StrategyFactory().discover_strategies() poderia ser chamado aqui
# se implementado para varrer o diretório `strategies`.
# Atualmente, o registro depende da importação da classe da estratégia que usa o decorador.
