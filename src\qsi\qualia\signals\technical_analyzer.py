"""Technical analysis utilities for signal generation."""

from __future__ import annotations

import logging
from typing import Dict, List

import numpy as np

from .. import indicators as shared_indicators


class TechnicalSignalAnalyzer:
    """Analyzer for technical indicators."""

    def __init__(self) -> None:
        self.logger = logging.getLogger(__name__)

    async def analyze(self, market_data: Dict) -> List[Dict]:
        """Analyze technical indicators to generate signals."""
        signals = []

        try:
            prices = market_data.get("prices", [])
            volumes = market_data.get("volumes", [])

            if len(prices) < 20:
                return signals

            # Moving average crossover signals
            ma_signals = self._analyze_moving_averages(prices)
            signals.extend(ma_signals)

            # RSI signals
            rsi_signals = self._analyze_rsi(prices)
            signals.extend(rsi_signals)

            # Volume signals
            if len(volumes) > 0:
                volume_signals = self._analyze_volume(prices, volumes)
                signals.extend(volume_signals)

            # Support/resistance signals
            sr_signals = self._analyze_support_resistance(prices)
            signals.extend(sr_signals)

            return signals

        except Exception as e:
            self.logger.error("Error analyzing technical data: %s", e)
            return []

    def _analyze_moving_averages(self, prices: np.ndarray) -> List[Dict]:
        """Analyze moving average crossovers."""
        signals = []

        try:
            if len(prices) < 50:
                return signals

            # Calculate EMAs
            ema_12 = self._calculate_ema(prices, 12)
            ema_26 = self._calculate_ema(prices, 26)

            # Check for crossover
            if len(ema_12) >= 2 and len(ema_26) >= 2:
                current_diff = ema_12[-1] - ema_26[-1]
                previous_diff = ema_12[-2] - ema_26[-2]

                # Bullish crossover
                if previous_diff <= 0 and current_diff > 0:
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": 0.7,
                            "strength": min(1.0, abs(current_diff) / prices[-1] * 100),
                            "indicators": ["ema_crossover"],
                            "metadata": {
                                "crossover_type": "bullish",
                                "ema_diff": current_diff,
                            },
                        }
                    )

                # Bearish crossover
                elif previous_diff >= 0 and current_diff < 0:
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": 0.7,
                            "strength": min(1.0, abs(current_diff) / prices[-1] * 100),
                            "indicators": ["ema_crossover"],
                            "metadata": {
                                "crossover_type": "bearish",
                                "ema_diff": current_diff,
                            },
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing moving averages: %s", e)
            return []

    def _analyze_rsi(self, prices: np.ndarray) -> List[Dict]:
        """Analyze RSI for overbought/oversold conditions."""
        signals = []

        try:
            if len(prices) < 15:
                return signals

            rsi = self._calculate_rsi(prices, 14)

            if len(rsi) > 0:
                current_rsi = rsi[-1]

                # Oversold condition
                if current_rsi < 30:
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": min(1.0, (30 - current_rsi) / 10),
                            "strength": (30 - current_rsi) / 30,
                            "indicators": ["rsi_oversold"],
                            "metadata": {"rsi_value": current_rsi},
                        }
                    )

                # Overbought condition
                elif current_rsi > 70:
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": min(1.0, (current_rsi - 70) / 10),
                            "strength": (current_rsi - 70) / 30,
                            "indicators": ["rsi_overbought"],
                            "metadata": {"rsi_value": current_rsi},
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing RSI: %s", e)
            return []

    def _analyze_volume(self, prices: np.ndarray, volumes: np.ndarray) -> List[Dict]:
        """Analyze volume patterns."""
        signals = []

        try:
            if len(volumes) < 20:
                return signals

            # Volume spike analysis
            volume_ma = np.mean(volumes[-20:])
            current_volume = volumes[-1]

            if current_volume > volume_ma * 2:  # Volume spike
                # Determine direction from price movement
                if len(prices) >= 2:
                    price_change = (prices[-1] - prices[-2]) / prices[-2]

                    if abs(price_change) > 0.01:  # Significant price movement
                        action = "buy" if price_change > 0 else "sell"

                        signals.append(
                            {
                                "action": action,
                                "confidence": 0.6,
                                "strength": min(1.0, current_volume / volume_ma / 5),
                                "indicators": ["volume_spike"],
                                "metadata": {
                                    "volume_ratio": current_volume / volume_ma,
                                    "price_change": price_change,
                                },
                            }
                        )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing volume patterns: %s", e)
            return []

    def _analyze_support_resistance(self, prices: np.ndarray) -> List[Dict]:
        """Analyze support and resistance levels."""
        signals = []

        try:
            if len(prices) < 50:
                return signals

            current_price = prices[-1]

            # Find recent highs and lows
            highs = []
            lows = []

            for i in range(5, len(prices) - 5):
                if all(prices[i] >= prices[i - j] for j in range(1, 6)) and all(
                    prices[i] >= prices[i + j] for j in range(1, 6)
                ):
                    highs.append(prices[i])

                if all(prices[i] <= prices[i - j] for j in range(1, 6)) and all(
                    prices[i] <= prices[i + j] for j in range(1, 6)
                ):
                    lows.append(prices[i])

            # Check if current price is near support/resistance
            if lows:
                nearest_support = min(lows, key=lambda x: abs(x - current_price))
                support_distance = abs(current_price - nearest_support) / current_price

                if support_distance < 0.02 and current_price > nearest_support:
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": 0.65,
                            "strength": 1.0 - support_distance * 50,
                            "indicators": ["support_bounce"],
                            "target_price": nearest_support * 1.05,
                            "metadata": {"support_level": nearest_support},
                        }
                    )

            if highs:
                nearest_resistance = min(highs, key=lambda x: abs(x - current_price))
                resistance_distance = (
                    abs(current_price - nearest_resistance) / current_price
                )

                if resistance_distance < 0.02 and current_price < nearest_resistance:
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": 0.65,
                            "strength": 1.0 - resistance_distance * 50,
                            "indicators": ["resistance_rejection"],
                            "target_price": nearest_resistance * 0.95,
                            "metadata": {"resistance_level": nearest_resistance},
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing support/resistance: %s", e)
            return []

    def _calculate_ema(self, prices: np.ndarray, period: int) -> np.ndarray:
        """Calculate Exponential Moving Average."""
        return np.asarray(shared_indicators.ema(prices, period), dtype=float)

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """Calculate Relative Strength Index."""
        return np.asarray(shared_indicators.rsi(prices, period), dtype=float)
