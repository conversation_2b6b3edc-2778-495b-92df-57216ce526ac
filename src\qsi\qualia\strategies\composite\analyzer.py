"""Utility functions for CompositeStrategy market analysis."""

from __future__ import annotations

from typing import Any, Dict, Optional, TYPE_CHECKING

import numpy as np
import pandas as pd

from ...utils.logger import get_logger
from ...indicators import atr, ema, rsi

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from .strategy import CompositeStrategy

logger = get_logger(__name__)


def analyze_market(
    strategy: "CompositeStrategy",
    market_data: pd.DataFrame,
    common_indicators: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """Perform common indicator calculations and sub strategy analysis."""
    if not strategy.is_initialized:
        raise RuntimeError(f"{strategy.name} não inicializada.")
    if market_data.empty:
        logger.warning(f"{strategy.name}: Dados de mercado vazios recebidos.")
        return {"error": "Market data is empty", "common": {}, "sub_analyses": {}}

    results: Dict[str, Any] = {"common": {}, "sub_analyses": {}}

    current_hash = hash(
        market_data.index[-1].to_pydatetime().isoformat()
        + str(market_data["open"].iloc[-1])
        + str(market_data["high"].iloc[-1])
        + str(market_data["low"].iloc[-1])
        + str(market_data["close"].iloc[-1])
        + str(market_data["volume"].iloc[-1] if "volume" in market_data.columns else "")
    )

    if strategy.last_market_data_hash == current_hash and strategy.indicators_cache:
        results["common"] = strategy.indicators_cache
        logger.debug(f"{strategy.name}: Indicadores comuns recuperados do cache.")
    else:
        close = market_data["close"].to_numpy()
        high = market_data["high"].to_numpy()
        low = market_data["low"].to_numpy()

        min_len_rsi = strategy.common_params.get("rsi_period", 14)
        min_len_ema_long = strategy.common_params.get("ema_long", 26)
        min_len_atr = strategy.common_params.get("atr_period", 14)
        required_len = max(min_len_rsi, min_len_ema_long, min_len_atr, 1)

        if len(close) < required_len:
            logger.warning(
                f"{strategy.name}: Dados insuficientes ({len(close)} barras) para indicadores comuns (necessário: {required_len})."
            )
            results["common"] = {
                "rsi": np.array([np.nan] * len(close)),
                "ema_short": np.array([np.nan] * len(close)),
                "ema_long": np.array([np.nan] * len(close)),
                "atr": np.array([np.nan] * len(close)),
                "last_close": close[-1] if len(close) > 0 else np.nan,
                "last_atr": np.nan,
            }
        else:
            results["common"]["rsi"] = rsi(close, strategy.common_params["rsi_period"])
            results["common"]["ema_short"] = ema(
                close, strategy.common_params["ema_short"]
            )
            results["common"]["ema_long"] = ema(
                close, strategy.common_params["ema_long"]
            )
            results["common"]["atr"] = atr(
                high, low, close, strategy.common_params["atr_period"]
            )
            results["common"]["last_close"] = close[-1]
            results["common"]["last_atr"] = (
                results["common"]["atr"][-1]
                if len(results["common"]["atr"]) > 0
                and not np.isnan(results["common"]["atr"][-1])
                else 0.0
            )

        strategy.indicators_cache = results["common"]
        strategy.last_market_data_hash = current_hash
        logger.debug(
            f"{strategy.name}: Indicadores comuns recalculados e armazenados no cache."
        )

    results["market_data_current_timestamp"] = market_data.index[-1]

    for strat_instance in strategy.strategies:
        common_params_for_sub = strategy.common_params.copy()
        indicators_calculated = results.get("common", {}).copy()
        sub_analysis_context = {**common_params_for_sub, **indicators_calculated}
        name = strat_instance.name
        try:
            results["sub_analyses"][name] = strat_instance.analyze_market(
                market_data, common_indicators=sub_analysis_context
            )
        except Exception as e:  # pragma: no cover - defensive
            error_msg = f"Erro ao analisar sub-estratégia {name}: {str(e)}. Impacto: score será 0.0."
            logger.error(error_msg, exc_info=True)
            results["sub_analyses"][name] = {
                "error": error_msg,
                "last_price": results["common"].get("last_close", np.nan),
            }

    return results
