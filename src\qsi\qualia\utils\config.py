"""Configuration loading utilities for QUALIA."""

try:
    import yaml
except ModuleNotFoundError:
    # Create a dummy yaml module if PyYAML is not installed.
    # This allows the system to run with default settings even if
    # the config dependency is missing, increasing resilience for testing.
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
import os
from pathlib import Path
from typing import Dict, Any

from .logger import get_logger


logger = get_logger(__name__)


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file."""
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            # Try relative to project root
            config_file = Path(__file__).parent.parent.parent.parent / config_path

        with open(config_file, "r") as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        return {}
    except yaml.YAMLError as exc:
        logger.error("Erro ao analisar YAML", exc_info=exc)
        return {}


def get_env_var(name: str, default: str | None = None) -> str | None:
    """Get environment variable with optional default."""
    return os.environ.get(name, default)
