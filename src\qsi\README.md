# Quantum Synapse Integrator (QSI)

O QSI é um conjunto de ferramentas para avaliação de repositórios de código com enfoque em relevância e compatibilidade quântica. Este projeto demonstra uma arquitetura modular composta por analisadores, validadores e ressonadores geométricos.

## Instalação

```bash
pip install -r requirements.txt
pip install numpy scipy pytest hypothesis
```

## Configuração de Logs

O diretório utilizado para armazenar os arquivos de log pode ser alterado
definindo a variável de ambiente `QUALIA_LOG_DIR`.

```bash
export QUALIA_LOG_DIR=/caminho/para/logs
```

## Visão Geral dos Componentes

### `qsi.system.QSI`

Classe principal que orquestra o fluxo de trabalho do QSI. Responsável por coordenar a análise do repositório, validar o código e aplicar ressonância geométrica.

```python
from qsi.system import QSI

system = QSI(dimensions=8, phi1=1.618, phi2=2.618)
resultado = system.evaluate(repo_url="https://github.com/exemplo/repo")
print(resultado)
```

### `qsi.analyzer.RepoAnalyzer`

Realiza a coleta e a análise inicial de um repositório Git. As etapas incluem download, extração e cálculo de métricas como relevância quântica e qualidade do código.

Métodos principais:
- `analyze()` – executa toda a análise e retorna um dicionário de métricas.

### `qsi.validator.QuantumCompatibilityValidator`

Valida o código analisado em múltiplas dimensões, incluindo verificação de sintaxe, princípios quânticos e testes em um sandbox controlado.

Uso básico:
```python
from qsi.validator import QuantumCompatibilityValidator

validator = QuantumCompatibilityValidator(analysis_data)
validator.validate("caminho/do/codigo")
```

### `qsi.resonator.GeometricQSIResonator`

Aplica transformações unitárias inspiradas na geometria do número áureo para evoluir estados quânticos. Utilizado internamente pelo QSI.

### Funções utilitárias

- `qsi.metrics.calculate_geometric_metrics(state, resonance_matrix)` – gera métricas de alinhamento geométrico a partir de um estado final.
- `qsi.geometry.build_phi_unitary(dim, phi)` – constrói matrizes unitárias baseadas na proporção áurea. A função utiliza cache local para acelerar chamadas repetidas.
- `qsi.validation.validate_quantum_state` – decorador que valida vetores de estado quântico antes de serem processados.

## Exemplo Completo

Para executar o fluxo completo de avaliação, utilize o script `main.py`:

```bash
python main.py
```

O resultado exibirá o veredito final e as métricas calculadas para o repositório analisado.

## Testes

Execute a suíte de testes com:

```bash
pytest -q
```

Os testes incluem propriedades para as transformações geométricas e validações de estados quânticos.



## Dashboard Web

Um pequeno dashboard em Flask permite executar o QSI a partir de um navegador e visualizar as métricas obtidas.

Execute com:

```bash
python dashboard.py
```

Acesse `http://localhost:5000` e informe a URL do repositório a ser avaliado.

## CLI de Análises em Lote

Para processar uma lista de repositórios com o QSI utilize o script `batch_cli.py`:

```bash
python batch_cli.py repos.txt --dimensions 8 --output resultados.json
```

O arquivo `repos.txt` deve conter uma URL por linha. Os resultados serão impressos na tela e, opcionalmente, salvos no caminho indicado em `--output`.

