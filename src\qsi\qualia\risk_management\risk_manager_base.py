from __future__ import annotations

"""Interface base para gerenciadores de risco do QUALIA."""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple, TypedDict

from ..memory.event_bus import SimpleEventBus

from ..utils.logger import get_logger


class PositionSizingResult(TypedDict, total=False):
    """Information returned by :meth:`calculate_position_size`."""

    position_allowed: bool
    reason: str
    position_size: float
    quantity: float
    risk_amount: float


class CapitalUpdateResult(TypedDict, total=False):
    """Information returned by :meth:`update_capital` and :meth:`process_trade_result`."""

    current_capital: float
    drawdown_pct: float


logger = get_logger(__name__)


class QUALIARiskManagerBase(ABC):
    """Define a interface mínima para gerenciadores de risco."""

    def __init__(
        self,
        initial_capital: float,
        risk_profile: str = "balanced",
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Initialize the risk manager interface.

        Parameters
        ----------
        initial_capital
            Amount of capital available when trading starts.
        risk_profile
            Qualitative label describing the risk appetite.
        event_bus
            Optional event bus used to emit ``risk.manager_created``.
        """

        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_profile = risk_profile
        self.event_bus = event_bus

        if self.event_bus:
            from ..events import RiskManagerCreatedEvent

            self.event_bus.publish(
                "risk.manager_created",
                RiskManagerCreatedEvent(
                    manager_class=self.__class__.__name__,
                    initial_capital=initial_capital,
                    risk_profile=risk_profile,
                ),
            )

    @abstractmethod
    def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        stop_loss_price: float,
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        volume: Optional[float] = None,
        min_lot_size: Optional[float] = None,
        informational_mass: Optional[float] = None,
        initial_informational_mass: Optional[float] = None,
        lambda_factor: Optional[float] = None,
        *,
        trace_id: Optional[str] = None,
    ) -> PositionSizingResult:
        """Calcula o tamanho de posição recomendado.

        Parameters
        ----------
        symbol
            Identificador do ativo.
        current_price
            Preço atual de mercado.
        stop_loss_price
            Nível de stop loss utilizado no cálculo.
        confidence
            Confiança do sinal de trade.
        volatility
            Volatilidade observada para o ativo.
        volume
            Volume negociado no período atual.
        min_lot_size
            Lote mínimo exigido pela exchange.
        informational_mass
            Massa informacional utilizada em ajustes quânticos.
        initial_informational_mass
            Valor de referência para ``informational_mass``.
        lambda_factor
            Fator de ajuste externo aplicado ao risco.

        Returns
        -------
        PositionSizingResult
            Dicionário com informações de sizing.
        """

    @abstractmethod
    def update_capital(self, new_capital: float) -> CapitalUpdateResult:
        """Atualiza o capital interno do gerenciador."""

    @abstractmethod
    def can_open_new_position(
        self, current_positions: int, *, trace_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """Indica se uma nova posição pode ser aberta."""

    @abstractmethod
    def process_trade_result(self, trade_info: Dict[str, Any]) -> CapitalUpdateResult:
        """Processa o resultado de uma operação finalizada."""


__all__ = [
    "QUALIARiskManagerBase",
    "PositionSizingResult",
    "CapitalUpdateResult",
]
