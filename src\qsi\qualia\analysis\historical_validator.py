from __future__ import annotations

"""Historical validation for NEXUS alerts.

Compares recorded ``nexus.alert`` events contra uma lista de marcos históricos
(fornecida em JSON) e calcula métricas simples:
 • lead_time – dias entre primeiro alerta (>= threshold) e data oficial
 • detected – se houve alerta até N dias antes do evento

Uso CLI:
    python -m src.qualia.analysis.historical_validator \
        --history nexus_coherence_history.jsonl \
        --events data/historical_events.json \
        --window 30
"""

import argparse
import json
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

_DATE_FMT = "%Y-%m-%d"


def _load_events(path: str | Path) -> List[Dict[str, str]]:
    with open(path, "r", encoding="utf-8") as fh:
        events = json.load(fh)
    for ev in events:
        if "date" not in ev or "name" not in ev:
            raise ValueError("Each event must have 'name' and 'date'")
    return events


def _load_alerts(history_path: str | Path) -> List[datetime]:
    alerts: List[datetime] = []
    with open(history_path, "r", encoding="utf-8") as fh:
        for line in fh:
            rec = json.loads(line)
            if rec.get("type") != "alert":
                continue
            ts = float(rec["timestamp"])
            alerts.append(datetime.fromtimestamp(ts, tz=timezone.utc))
    return sorted(alerts)


def validate(history_path: str | Path, events_path: str | Path, window_days: int = 30):
    alerts = _load_alerts(history_path)
    if not alerts:
        logger.warning("Nenhum alerta encontrado em %s", history_path)
        return []
    events = _load_events(events_path)
    window = timedelta(days=window_days)
    results = []
    for ev in events:
        ev_date = datetime.strptime(ev["date"], _DATE_FMT).replace(tzinfo=timezone.utc)
        # encontrar primeiro alerta dentro da janela
        prior_alerts = [a for a in alerts if ev_date - window <= a <= ev_date]
        if prior_alerts:
            first = prior_alerts[0]
            lead = (ev_date - first).days
            detected = True
        else:
            lead = None
            detected = False
        results.append({"name": ev["name"], "date": ev["date"], "detected": detected, "lead_days": lead})
    return results


def _cli():  # pragma: no cover
    ap = argparse.ArgumentParser(description="Validate NEXUS alerts historically.")
    ap.add_argument("--history", required=True, help="Path to history JSONL file")
    ap.add_argument("--events", required=True, help="Path to events JSON file")
    ap.add_argument("--window", type=int, default=30, help="Days window before event")
    args = ap.parse_args()
    res = validate(args.history, args.events, args.window)
    print(json.dumps(res, indent=2))


if __name__ == "__main__":  # pragma: no cover
    _cli()

# -------------------- Metrics -----------------------------


def prf1(results: List[Dict[str, object]]):
    """Return precision, recall and F1 from validation results.

    Parameters
    ----------
    results
        Output list from ``validate``.
    """

    tp = sum(1 for r in results if r.get("detected"))
    fn = sum(1 for r in results if not r.get("detected"))
    # In our framing we don't have true negatives easily (days without event but with alert).
    # Assume fp = total alerts mapped to non-events (optional).
    precision = tp / (tp + 0.0) if tp else 0.0
    recall = tp / (tp + fn) if (tp + fn) else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) else 0.0
    return {"precision": precision, "recall": recall, "f1": f1}

__all__ = ["validate", "prf1"] 