from __future__ import annotations
import os
from .analyzer import MetacogClusterAnalyzer, ClusterInsights

"""Utility functions for metacognitive metrics management."""

from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional


@dataclass
class Metric:
    """Internal container for a metacognitive metric."""

    name: str
    value: float
    timestamp: datetime


_METRICS: List[Metric] = []


def push_metric(
    name: str, value: float, *, timestamp: Optional[datetime] = None
) -> None:
    """Store a metacognitive metric.

    Parameters
    ----------
    name : str
        Metric identifier.
    value : float
        Numeric value of the metric.
    timestamp : datetime, optional
        Explicit timestamp of when the metric was observed.
        If ``None``, the current UTC time is used.

    Examples
    --------
    >>> push_metric("coherence", 0.8)
    >>> push_metric("risk", 0.6, timestamp=datetime.now(timezone.utc))
    """

    ts = timestamp or datetime.now(timezone.utc)
    _METRICS.append(Metric(name, float(value), ts))
    if len(_METRICS) > 1000:
        _METRICS.pop(0)


def get_focus(window: int = 10) -> float:
    """Return the average metric value as focus level.

    Parameters
    ----------
    window : int, default 10
        Number of most recent metrics considered.

    Returns
    -------
    float
        Focus level between ``0`` and ``1``.

    Examples
    --------
    >>> push_metric("coherence", 0.7)
    >>> push_metric("risk", 0.5)
    >>> get_focus()
    0.6
    """

    if not _METRICS:
        return 0.0
    recent = _METRICS[-window:]
    mean_val = sum(m.value for m in recent) / len(recent)
    return max(0.0, min(1.0, mean_val))


def snapshot() -> Dict[str, Any]:
    """Return a snapshot of the stored metrics.

    Returns
    -------
    dict
        Dictionary containing ``focus`` and the list of metrics.

    Examples
    --------
    >>> push_metric("coherence", 0.9)
    >>> snapshot()
    {'focus': 0.9, 'metrics': [...]}  # doctest: +ELLIPSIS
    """

    return {
        "focus": get_focus(len(_METRICS)),
        "metrics": [asdict(m) for m in _METRICS],
    }


async def get_insights(payload: List[str]) -> Optional[ClusterInsights]:
    """Return cluster insights if clustering is enabled."""
    flag = os.getenv("QUALIA_ENABLE_CLUSTERING", "false").lower()
    if flag not in {"1", "true", "yes", "y"}:
        return None
    analyzer = MetacogClusterAnalyzer()
    return await analyzer.analyze(payload)


def explain_confidence(confidence: float) -> str:
    """Return a textual explanation for the metacognitive confidence level.

    Parameters
    ----------
    confidence : float
        Value between ``0`` and ``1`` representing system self-confidence.

    Returns
    -------
    str
        Explanation describing the confidence band.

    Examples
    --------
    >>> explain_confidence(0.2)
    'confiança baixa'
    >>> explain_confidence(0.8)
    'confiança alta'
    """

    if not 0.0 <= confidence <= 1.0:
        raise ValueError("confidence must be between 0 and 1")

    if confidence < 0.3:
        return "confiança baixa"
    if confidence < 0.7:
        return "confiança moderada"
    return "confiança alta"


__all__ = ["push_metric", "get_focus", "snapshot", "explain_confidence", "get_insights"]
