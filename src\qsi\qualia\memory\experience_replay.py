"""Experience replay memory for reinforcement learning agents.

Exports
-------
ExperienceReplay
"""

from __future__ import annotations

from ..utils.logger import get_logger
from ..memory.base_memory import BaseMemory
from ..memory.locking import with_lock
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
import random
from collections import deque
import threading
from typing import Any, List, Optional, Dict

logger = get_logger(__name__)

__all__ = ["ExperienceReplay"]


class ExperienceReplay(BaseMemory):
    """Fixed-size buffer that stores transitions for training.

    When ``thread_safe`` is ``True`` calls to :meth:`store`, :meth:`sample` and
    :meth:`clear` are serialized with a :class:`threading.Lock`.
    """

    def __init__(
        self,
        capacity: int,
        seed: int | None = None,
        *,
        thread_safe: bool = False,
        risk_manager: Optional[QUALIARiskManagerBase] = None,
        num_symbols: int = 1,
        num_timeframes: int = 1,
    ) -> None:
        """Create a new replay buffer.

        Parameters
        ----------
        capacity : int
            Base capacity. If adaptive, will be scaled by num_symbols * num_timeframes * 150.
        seed : int, optional
            Seed for deterministic sampling.
        thread_safe : bool, optional
            When ``True`` operations are guarded by a ``threading.Lock``.
        num_symbols : int, optional
            Number of trading symbols (for adaptive capacity).
        num_timeframes : int, optional
            Number of timeframes (for adaptive capacity).
        """
        if capacity <= 0:
            raise ValueError("capacity must be positive")

        # Capacidade adaptativa baseada no número de símbolos e timeframes
        self._base_capacity = capacity
        self._adaptive_capacity = max(capacity, num_symbols * num_timeframes * 150)
        self._capacity = self._adaptive_capacity
        self._memory: deque[Any] = deque(maxlen=self._capacity)
        self._random = random.Random(seed)
        self._lock: Optional[threading.Lock] = threading.Lock() if thread_safe else None
        self.risk_manager = risk_manager
        
        # Configurações de shrink
        self._shrink_threshold = 0.9  # Shrink quando 90% cheio
        self._shrink_factor = 0.1      # Remove 10% dos elementos
        
        logger.info(
            "ExperienceReplay initialized with capacity=%s (base=%s, symbols=%s, timeframes=%s)",
            self._capacity, self._base_capacity, num_symbols, num_timeframes
        )

    def _require_risk_manager(self) -> None:
        """Raise error if ``risk_manager`` is missing."""

        if not isinstance(self.risk_manager, QUALIARiskManagerBase):
            logger.error("RiskManager instance not configured")
            raise RuntimeError("RiskManager instance required")

    @with_lock
    def store(
        self,
        key: Any,
        value: Optional[Any] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Store a transition in the buffer.

        Parameters
        ----------
        key : Any
            Identifier ignored by :class:`ExperienceReplay`. When called with a
            single positional argument the value is treated as the transition.
        value : Any, optional
            Explicit transition to store. If ``None``, ``key`` is used as the
            transition.
        metadata : dict, optional
            Unused metadata parameter present for :class:`BaseMemory` compliance.
        """

        self._require_risk_manager()
        transition = key if value is None else value
        self._memory.append(transition)
        size = len(self._memory)
        logger.debug("Stored transition; size=%s", size)
        
        # Auto-shrink quando próximo do limite
        if size > self._capacity * self._shrink_threshold:
            self._auto_shrink()

    @with_lock
    def sample(self, batch_size: int) -> List[Any]:
        """Return a random batch of transitions without replacement."""

        self._require_risk_manager()

        if batch_size <= 0:
            raise ValueError("batch_size must be positive")
        if batch_size > len(self._memory):
            raise ValueError("Not enough transitions to sample")
        return self._random.sample(self._memory, batch_size)

    def size(self) -> int:
        """Return the number of stored transitions."""

        return len(self._memory)

    @with_lock
    def retrieve(self, key: int | str) -> Optional[Any]:
        """Retrieve a stored transition by index."""
        self._require_risk_manager()
        try:
            index = int(key)
        except (TypeError, ValueError):
            return None

        if index < 0:
            return None

        try:
            return self._memory[index]
        except IndexError:
            return None

    @with_lock
    def delete(self, key: int | str) -> bool:
        """Delete a transition by index."""
        self._require_risk_manager()
        try:
            index = int(key)
        except (TypeError, ValueError):
            return False

        if 0 <= index < len(self._memory):
            del self._memory[index]
            return True
        return False

    @with_lock
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """Return a list of transition indexes."""

        indices = [str(i) for i in range(len(self._memory))]
        if prefix:
            indices = [i for i in indices if i.startswith(prefix)]
        return indices

    @with_lock
    def clear(self) -> None:
        """Remove all transitions from the buffer."""

        self._require_risk_manager()
        self._memory.clear()
        logger.debug("Cleared experience replay buffer")

    @with_lock
    def shrink(self, factor: Optional[float] = None) -> int:
        """Remove a fraction of stored transitions randomly.
        
        Parameters
        ----------
        factor : float, optional
            Fraction of elements to remove (0.0 to 1.0).
            If None, uses default _shrink_factor.
            
        Returns
        -------
        int
            Number of elements removed.
        """
        if factor is None:
            factor = self._shrink_factor
            
        if not 0.0 <= factor <= 1.0:
            raise ValueError("shrink factor must be between 0.0 and 1.0")
            
        current_size = len(self._memory)
        num_to_remove = int(current_size * factor)
        
        if num_to_remove > 0:
            # Remove elementos aleatórios
            indices_to_remove = sorted(
                self._random.sample(range(current_size), num_to_remove),
                reverse=True
            )
            
            # Remove de trás para frente para não invalidar índices
            for idx in indices_to_remove:
                del self._memory[idx]
            
            logger.info(
                "Shrink completed: removed %s elements (%.1f%%), new size=%s",
                num_to_remove, factor * 100, len(self._memory)
            )
            
        return num_to_remove

    def _auto_shrink(self) -> None:
        """Automatically shrink when above threshold."""
        if self._lock:
            # Se já estamos dentro do lock, não tentar adquirir novamente
            self.shrink()
        else:
            # Se não há lock, podemos chamar direto
            self.shrink()
            
    def set_adaptive_capacity(self, num_symbols: int, num_timeframes: int) -> None:
        """Update adaptive capacity based on trading configuration.
        
        Parameters
        ----------
        num_symbols : int
            Number of trading symbols.
        num_timeframes : int
            Number of timeframes.
        """
        new_capacity = max(self._base_capacity, num_symbols * num_timeframes * 150)
        if new_capacity != self._capacity:
            self._capacity = new_capacity
            # Recreate deque with new maxlen
            old_memory = list(self._memory)
            self._memory = deque(old_memory[-new_capacity:], maxlen=new_capacity)
            logger.info(
                "Adaptive capacity updated: %s (symbols=%s, timeframes=%s)",
                new_capacity, num_symbols, num_timeframes
            )

    @with_lock
    def get_info(self) -> dict[str, Any]:
        """Return summary information about the buffer."""

        return {
            "type": "ExperienceReplay",
            "size": len(self._memory),
            "capacity": self._capacity,
            "base_capacity": self._base_capacity,
            "occupancy": f"{(len(self._memory) / self._capacity) * 100:.1f}%",
        }
