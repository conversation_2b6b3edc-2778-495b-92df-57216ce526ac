"""API routes for interacting with QUALIA components."""

from __future__ import annotations

import json
from datetime import datetime, timezone

import numpy as np
from flask import Blueprint, jsonify, request, current_app
from typing import Any

from ...utils.logger import get_logger
from ...ui.initialize import get_qualia_state, initialize_qualia_system
from ...market.quantum_metrics_calculator import QuantumMetricsCalculator
from ...monitoring.metrics import get_collector

logger = get_logger(__name__)


_quantum_metrics_calculator: QuantumMetricsCalculator | None = None
QUANTUM_METRICS_AVAILABLE: bool = False


def get_quantum_metrics_calculator() -> QuantumMetricsCalculator | None:
    """Return an initialized :class:`QuantumMetricsCalculator` instance.

    The calculator is created lazily when first requested. Any errors during
    initialization are logged and the availability flag updated accordingly.
    """

    global _quantum_metrics_calculator, QUANTUM_METRICS_AVAILABLE
    if _quantum_metrics_calculator is None:
        try:
            with current_app.app_context():
                qualia_state = get_qualia_state()
                if getattr(qualia_state, "universe", None) is None:
                    initialize_qualia_system(qualia_state)
                universe = qualia_state.universe
                _quantum_metrics_calculator = QuantumMetricsCalculator(universe)
                QUANTUM_METRICS_AVAILABLE = True
        except (ImportError, AttributeError, RuntimeError) as exc:
            QUANTUM_METRICS_AVAILABLE = False
            _quantum_metrics_calculator = None
            logger.exception(
                "QuantumMetricsCalculator não disponível: %s. Certifique-se de instalar 'qiskit' e 'qiskit-aer'.",
                exc,
            )
    return _quantum_metrics_calculator


# Provide direct attribute for backward compatibility
quantum_metrics_calculator = None

api_bp = Blueprint("api", __name__, url_prefix="/api")


def _convert_to_json_serializable(obj):
    if isinstance(obj, dict):
        return {key: _convert_to_json_serializable(value) for key, value in obj.items()}
    if isinstance(obj, (list, tuple)):
        return [_convert_to_json_serializable(item) for item in obj]
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    if isinstance(obj, np.integer):
        return int(obj)
    if isinstance(obj, np.floating):
        return float(obj)
    if hasattr(obj, "to_dict") and callable(getattr(obj, "to_dict")):
        return _convert_to_json_serializable(obj.to_dict())
    if hasattr(obj, "__dict__"):
        return _convert_to_json_serializable(obj.__dict__)
    try:
        json.dumps(obj)
        return obj
    except (TypeError, OverflowError):
        return str(obj)


def _record_quantum_metrics(metrics: dict) -> None:
    """Send numeric quantum metrics to the global collector."""
    collector = get_collector()
    for name, value in metrics.items():
        if isinstance(value, (int, float)):
            collector.record_metric(f"quantum.{name}", float(value))


@api_bp.route("/consciousness/state")
def get_consciousness_state():
    """Return the current QUALIA consciousness state."""
    qualia_state = get_qualia_state()
    if qualia_state.consciousness is None:
        initialize_qualia_system(qualia_state)

    with qualia_state.locked():
        state_data = {
            "parameters": {
                "n_qubits": qualia_state.consciousness.n_qubits,
                "perception_depth": qualia_state.consciousness.perception_depth,
                "entropy_sensitivity": qualia_state.consciousness.entropy_sensitivity,
                "thermal_coefficient": qualia_state.consciousness.thermal_coefficient,
                "self_reflection": qualia_state.consciousness.self_reflection_enabled,
            },
            "qast_cycles": qualia_state.consciousness.get_qast_history() or [],
            "metrics": qualia_state.consciousness.get_metrics_dict() or {},
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    quantum_state = []
    for i in range(2 ** state_data["parameters"]["n_qubits"]):
        binary = format(i, f'0{state_data["parameters"]["n_qubits"]}b')
        prob = np.random.random() ** 2
        phase = np.random.uniform(0, 2 * np.pi)
        quantum_state.append(
            {
                "basis": binary,
                "probability": float(prob),
                "phase": float(phase),
                "decimal": i,
            }
        )

    total = sum(s["probability"] for s in quantum_state)
    for state in quantum_state:
        state["probability"] /= total if total > 0 else 1

    state_data["quantum_state"] = quantum_state[:16]
    return jsonify(state_data)


@api_bp.route("/consciousness/process", methods=["POST"])
def process_consciousness_cycle():
    """Execute one processing cycle of QUALIA consciousness."""
    qualia_state = get_qualia_state()
    if qualia_state.consciousness is None:
        initialize_qualia_system(qualia_state)

    result = qualia_state.consciousness.process_qast_cycle()
    with qualia_state.locked():
        qualia_state.qast_cycles = qualia_state.consciousness.get_qast_history() or []
        qualia_state.update_timestamp()

    return jsonify(
        {"success": True, "cycle_result": result, "timestamp": qualia_state.last_update}
    )


@api_bp.route("/symbolic/process", methods=["POST"])
def process_symbolic_data():
    """Process symbolic data through the QUALIA symbolic processor."""
    qualia_state = get_qualia_state()
    if qualia_state.symbolic_processor is None:
        initialize_qualia_system(qualia_state)

    data = request.json or {}
    symbols = data.get("symbols", "")

    if qualia_state.symbolic_processor:
        results = qualia_state.symbolic_processor.process_symbols(symbols)
        entropic_analysis = qualia_state.symbolic_processor.symbolic_entropic_analysis(
            list(symbols)
        )
    else:
        results = {"patterns": [], "semantic_fields": {}}
        entropic_analysis = {"shannon_entropy": 0, "symbolic_diversity": 0}

    return jsonify(
        {
            "success": True,
            "patterns": results.get("patterns", []),
            "semantic_fields": results.get("semantic_fields", {}),
            "entropic_analysis": entropic_analysis,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    )


@api_bp.route("/environment/perceive", methods=["POST"])
def perceive_environment():
    """Allow QUALIA to perceive external data."""
    qualia_state = get_qualia_state()
    if qualia_state.consciousness is None:
        initialize_qualia_system(qualia_state)

    data = request.json or {}
    perception_type = data.get("type", "general")
    perception_data = data.get("data", {})
    result = qualia_state.consciousness.process_perception(
        perception_type, perception_data
    )

    with qualia_state.locked():
        qualia_state.perception_data[perception_type] = perception_data
        qualia_state.update_timestamp()

    return jsonify(
        {
            "success": True,
            "perception_result": result,
            "timestamp": qualia_state.last_update,
        }
    )


@api_bp.route("/reflection/execute", methods=["POST"])
def execute_reflection():
    """Execute a self-reflection process."""
    qualia_state = get_qualia_state()
    if qualia_state.consciousness is None:
        initialize_qualia_system(qualia_state)

    data = request.json or {}
    topic = data.get("topic", "Qual é a natureza dos padrões emergentes detectados?")
    qualia_state.consciousness._log_self_reflection(f"Reflexão sobre: {topic}")
    reflection_result = qualia_state.consciousness._execute_self_reflection()

    return jsonify(
        {
            "success": True,
            "reflection_topic": topic,
            "reflection_result": reflection_result,
            "reflection_log": qualia_state.consciousness.state.get(
                "self_reflection_log", []
            ),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    )


@api_bp.route("/quantum/metrics", methods=["POST"])
def calculate_quantum_metrics():
    """Calculate quantum metrics from provided market data."""
    qualia_state = get_qualia_state()

    qmc = get_quantum_metrics_calculator()
    if not qmc:
        return jsonify(
            {
                "success": False,
                "message": "Calculador de métricas quânticas não disponível",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    data = request.json or {}
    market_data = data.get("market_data", {})
    if not market_data:
        return jsonify(
            {
                "success": False,
                "message": "Dados de mercado não fornecidos ou inválidos",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    try:
        metrics = qmc.calculate_metrics(market_data)
        if qualia_state.universe is not None:
            try:
                if hasattr(qualia_state.universe, "get_metrics"):
                    raw_metrics = qualia_state.universe.get_metrics()
                elif hasattr(qualia_state.universe, "metrics"):
                    raw_metrics = qualia_state.universe.metrics
                else:
                    raw_metrics = {
                        "n_qubits": qualia_state.universe.n_qubits,
                        "thermal": getattr(qualia_state.universe, "thermal", None),
                        "retro_mode": getattr(
                            qualia_state.universe, "retro_mode", None
                        ),
                    }
                metrics["qualia_universe_metrics"] = _convert_to_json_serializable(
                    raw_metrics
                )
            except (AttributeError, RuntimeError) as exc:  # pragma: no cover - optional
                logger.exception(
                    "Não foi possível obter métricas do universo QUALIA: %s", exc
                )
        _record_quantum_metrics(metrics)
        return jsonify(
            {
                "success": True,
                "metrics": metrics,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )
    except (RuntimeError, ValueError) as exc:  # pragma: no cover - optional
        return jsonify(
            {
                "success": False,
                "message": f"Erro ao calcular métricas quânticas: {str(exc)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )


@api_bp.route("/quantum/risk_analysis", methods=["POST"])
def calculate_quantum_risk():  # pragma: no cover - heavy calc
    """Perform quantum risk analysis."""

    qmc = get_quantum_metrics_calculator()
    if not qmc:
        return jsonify(
            {
                "success": False,
                "message": "Calculador de métricas quânticas não disponível",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    data = request.json or {}
    market_data = data.get("market_data", {})
    positions = data.get("positions", [])
    if not market_data:
        return jsonify(
            {
                "success": False,
                "message": "Dados de mercado não fornecidos ou inválidos",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    try:
        metrics = qmc.calculate_metrics(market_data)
        entropy = metrics.get("quantum_entropy", 0)
        coherence = metrics.get("quantum_coherence", 0)
        avg_volatility = (
            float(np.mean([pos.get("volatility", 0) for pos in positions]))
            if positions
            else 0
        )
        risk_metrics = {"quantum_metrics": metrics}
        risk_metrics["quantum_metrics"]["quantum_instability"] = float(
            entropy * (1 - coherence)
        )
        direction_confidence = abs(
            risk_metrics["quantum_metrics"].get("quantum_direction", 0.0)
        )
        entropy_score = entropy * 100
        instability_score = risk_metrics["quantum_metrics"]["quantum_instability"] * 100
        direction_risk_score = (
            100 * (1 - direction_confidence) if direction_confidence > 0 else 50
        )
        volatility_score = min(100, avg_volatility * 1000)
        risk_weights = [0.35, 0.30, 0.25, 0.10]
        risk_components = [
            entropy_score,
            instability_score,
            direction_risk_score,
            volatility_score,
        ]
        weighted_scores = [
            score * weight for score, weight in zip(risk_components, risk_weights)
        ]
        risk_level = sum(weighted_scores) / sum(risk_weights)
        if risk_level < 25:
            risk_category = "BAIXO"
            risk_action = "Manter estratégia normal"
            alert_level = "info"
        elif risk_level < 50:
            risk_category = "MODERADO"
            risk_action = "Monitorar de perto, sem ajustes imediatos"
            alert_level = "info"
        elif risk_level < 75:
            risk_category = "ELEVADO"
            risk_action = "Considerar redução de tamanho de posições em 25-50%"
            alert_level = "warning"
        elif risk_level < 90:
            risk_category = "ALTO"
            risk_action = "Reduzir exposição em 50-75% e aumentar stops"
            alert_level = "danger"
        else:
            risk_category = "EXTREMO"
            risk_action = "Considerar fechamento de todas as posições imediatamente"
            alert_level = "danger"
        risk_metrics.update(
            {
                "risk_level": float(risk_level),
                "risk_category": risk_category,
                "recommended_action": risk_action,
                "alert_level": alert_level,
            }
        )
        risk_alerts = []
        if entropy > 0.8:
            risk_alerts.append(
                {
                    "type": "quantum_entropy",
                    "message": f"Alta entropia quântica ({entropy:.4f}) indica mercado caótico e imprevisível",
                    "severity": "high" if entropy > 0.9 else "medium",
                }
            )
        if coherence < 0.3 and entropy > 0.6:
            risk_alerts.append(
                {
                    "type": "quantum_coherence",
                    "message": f"Baixa coerência quântica ({coherence:.4f}) com alta entropia indica instabilidade",
                    "severity": "high" if coherence < 0.2 else "medium",
                }
            )
        if avg_volatility > 0.05:
            risk_alerts.append(
                {
                    "type": "volatility",
                    "message": f"Volatilidade elevada ({avg_volatility:.4f}) detectada no mercado",
                    "severity": "high" if avg_volatility > 0.1 else "medium",
                }
            )
        risk_metrics["risk_alerts"] = risk_alerts
        _record_quantum_metrics({"risk_level": risk_level, **metrics})
        return jsonify(
            {
                "success": True,
                "risk_analysis": risk_metrics,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )
    except (RuntimeError, ValueError) as exc:  # pragma: no cover - optional
        return jsonify(
            {
                "success": False,
                "message": f"Erro ao calcular análise de risco quântico: {str(exc)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )


@api_bp.route("/hud/metrics", methods=["POST"])
def receive_hud_metrics() -> Any:
    """Receive client-side HUD metrics and store/forward them."""

    data = request.json or {}
    avg_fps = data.get("avgFPS")
    latency = data.get("latency") or data.get("serverLatency")

    statsd = current_app.extensions.get("statsd")
    if statsd:
        if isinstance(avg_fps, (int, float)):
            statsd.gauge("hud.avg_fps", avg_fps)
        if isinstance(latency, (int, float)):
            statsd.timing("hud.server_latency_ms", latency)

    if isinstance(avg_fps, (int, float)):
        from qualia.monitor import record_performance

        record_performance(avg_fps, float(latency) if latency is not None else None)

    return jsonify({"success": True})


@api_bp.route("/hud/metrics", methods=["GET"])
def get_hud_metrics() -> Any:
    """Return recorded HUD performance metrics."""

    from qualia.monitor import get_records

    records = [
        {
            "timestamp": r.timestamp.isoformat(),
            "avg_fps": r.avg_fps,
            "latency_ms": r.latency_ms,
        }
        for r in get_records()
    ]
    return jsonify({"success": True, "records": records})


@api_bp.route("/nexus/coherence", methods=["GET"])
def get_nexus_coherence():
    """Return the latest cross-modal coherence value (0–1)."""
    try:
        from qualia.analysis.coherence_monitor import LATEST_COHERENCE
    except ImportError:  # pragma: no cover – module missing
        LATEST_COHERENCE = None  # type: ignore
    return jsonify({"success": True, "coherence": LATEST_COHERENCE})


@api_bp.route("/nexus/threshold", methods=["GET"])
def get_nexus_threshold():
    """Return current adaptive coherence threshold."""
    try:
        from flask import current_app

        # store calibrator in app context if created elsewhere, else None
        calibrator = getattr(current_app, "nexus_threshold_calib", None)
        threshold = calibrator.get_threshold() if calibrator else None
    except AttributeError:  # pragma: no cover
        logger.exception("Falha ao obter threshold do nexus")
        threshold = None
    return jsonify({"success": True, "threshold": threshold})


@api_bp.route("/nexus/ga/state", methods=["GET"])
def get_ga_state():
    """Return last GA generation metrics."""
    try:
        from qualia.analysis.ga_threshold_evolver import LATEST_GEN_INFO

        return jsonify({"success": True, "state": LATEST_GEN_INFO})
    except (ImportError, RuntimeError) as exc:  # pragma: no cover
        logger.exception("GA state unavailable: %s", exc)
        return jsonify({"success": False, "state": None}), 503


@api_bp.route("/nexus/ga/pause", methods=["POST"])
def pause_ga():
    from qualia.analysis.ga_threshold_evolver import GLOBAL_EVENT_BUS

    if GLOBAL_EVENT_BUS:
        from qualia.events import GAPauseEvent

        GLOBAL_EVENT_BUS.publish("nexus.ga.pause", GAPauseEvent())
    return jsonify({"success": True})


@api_bp.route("/nexus/ga/resume", methods=["POST"])
def resume_ga():
    from qualia.analysis.ga_threshold_evolver import GLOBAL_EVENT_BUS

    if GLOBAL_EVENT_BUS:
        from qualia.events import GAResumeEvent

        GLOBAL_EVENT_BUS.publish("nexus.ga.resume", GAResumeEvent())
    return jsonify({"success": True})
