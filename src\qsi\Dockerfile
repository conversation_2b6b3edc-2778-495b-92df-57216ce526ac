FROM python:3.11-slim-bullseye

# Instalação de dependências fundamentais
RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    libopenblas-dev \
    libomp-dev \
    && rm -rf /var/lib/apt/lists/*

# Configuração do ambiente quântico (Placeholder)
# Em uma implementação real, aqui seriam copiados ou instalados SDKs quânticos
ENV QHOME=/opt/q \
    QLIC=/opt/q/lic \
    PATH=$PATH:/opt/q/l64

WORKDIR /app 