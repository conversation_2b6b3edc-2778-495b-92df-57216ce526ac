"""Storage for HUD performance metrics.

This module mantém um buffer circular de valores de FPS médio e latência
informados pelo frontend. Esses dados são úteis para depuração da HUD e para
análises posteriores de desempenho. As funções de acesso fornecem uma
interface simples para gravação e consulta das métricas.
"""

from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Deque, List, Optional

from collections import deque
from threading import Lock

from ..monitoring.metrics import MetricsCollector, get_collector
from ..memory.event_bus import SimpleEventBus
from ..events import PerformanceRecordedEvent


@dataclass
class PerformanceRecord:
    """Registro de métrica de desempenho.

    Parameters
    ----------
    timestamp:
        Momento em que o dado foi coletado.
    avg_fps:
        Média de quadros por segundo observada.
    latency_ms:
        Latência de renderização em milissegundos, se disponível.
    """

    timestamp: datetime
    avg_fps: float
    latency_ms: Optional[float] = None


# Número máximo padrão de amostras mantidas em ``PerformanceMonitor``
DEFAULT_MAX_RECORDS = 10_000


class PerformanceMonitor:
    """Container thread-safe para métricas de desempenho.

    Esta classe mantém uma fila ``deque`` com capacidade limitada, preservando
    apenas os registros mais recentes. Opcionalmente, as amostras podem ser
    encaminhadas a um ``MetricsCollector`` para integração com serviços de
    telemetria.
    """

    def __init__(
        self,
        maxlen: int = DEFAULT_MAX_RECORDS,
        *,
        metrics: MetricsCollector | None = None,
        event_bus: SimpleEventBus | None = None,
    ) -> None:
        """Inicializar o monitor.

        Parameters
        ----------
        maxlen:
            Capacidade máxima do buffer de registros.
        metrics:
            Instância opcional de ``MetricsCollector`` para registro externo das
            métricas.
        """

        self._lock = Lock()
        self._records: Deque[PerformanceRecord] = deque(maxlen=maxlen)
        self._metrics = metrics
        self._event_bus = event_bus

    def set_buffer_maxlen(self, maxlen: int) -> None:
        """Definir a capacidade do buffer de registros.

        Parameters
        ----------
        maxlen:
            Quantidade máxima de ``PerformanceRecord`` mantidos.
        """
        if maxlen <= 0:
            raise ValueError("maxlen must be positive")
        with self._lock:
            self._records = deque(self._records, maxlen=maxlen)

    def record_performance(self, avg_fps: float, latency_ms: Optional[float]) -> None:
        """Registrar uma nova amostra de desempenho.

        Parameters
        ----------
        avg_fps:
            Média de quadros por segundo enviada pelo frontend.
        latency_ms:
            Latência de processamento em milissegundos, se disponível.
        """
        record = PerformanceRecord(
            datetime.now(timezone.utc), float(avg_fps), latency_ms
        )
        with self._lock:
            self._records.append(record)
        if self._metrics is not None:
            self._metrics.record_metric("hud.avg_fps", float(avg_fps))
            if latency_ms is not None:
                self._metrics.record_metric("hud.latency_ms", float(latency_ms))
        if self._event_bus is not None:
            self._event_bus.publish(
                "monitor.performance",
                PerformanceRecordedEvent(avg_fps=float(avg_fps), latency_ms=latency_ms),
            )

    def get_records(self) -> List[PerformanceRecord]:
        """Obter cópia dos registros atuais.

        Returns
        -------
        list of PerformanceRecord
            Lista ordenada das amostras armazenadas no buffer.
        """
        with self._lock:
            return list(self._records)

    def clear_records(self) -> None:
        """Descartar todas as amostras gravadas."""
        with self._lock:
            self._records.clear()


_collector = get_collector()
performance_monitor = PerformanceMonitor(
    metrics=_collector,
    event_bus=_collector.event_bus,
)


def set_buffer_maxlen(maxlen: int) -> None:
    """Definir a quantidade máxima de registros mantidos."""
    performance_monitor.set_buffer_maxlen(maxlen)


def record_performance(avg_fps: float, latency_ms: Optional[float]) -> None:
    """Adicionar nova métrica ao buffer global."""
    performance_monitor.record_performance(avg_fps, latency_ms)


def get_records() -> List[PerformanceRecord]:
    """Recuperar todos os registros de desempenho."""
    return performance_monitor.get_records()


def clear_records() -> None:
    """Limpar completamente o buffer global."""
    performance_monitor.clear_records()


def set_event_bus(event_bus: SimpleEventBus) -> None:
    """Associar um ``SimpleEventBus`` ao ``performance_monitor`` global."""

    performance_monitor._event_bus = event_bus
