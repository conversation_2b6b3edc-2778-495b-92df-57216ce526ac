"""Risk management utilities for :mod:`nova_estrategia_qualia`.

This module caches :class:`DynamicRiskController` instances using
``functools.lru_cache`` to avoid repeatedly constructing controllers with the
same configuration.  The cache size is bounded by :data:`DRC_CACHE_MAXSIZE` to
prevent uncontrolled growth during long backtests or trading sessions.
"""

from __future__ import annotations

from typing import Any, Dict, Tuple
import json
from functools import lru_cache
import numpy as np
import pandas as pd
from scipy.stats import zscore

from ...market.dynamic_risk_controller import DynamicRiskController
from ...utils.logger import get_logger

DRC_CACHE_MAXSIZE = 32

logger = get_logger(__name__)


def _config_to_key(config: Dict[str, Any]) -> str:
    """Serialize configuration into a stable cache key."""

    return json.dumps(config, sort_keys=True)


@lru_cache(maxsize=DRC_CACHE_MAXSIZE)
def _get_dynamic_risk_controller(
    config_key: str, risk_profile: str
) -> DynamicRiskController:
    """Create or return a cached :class:`DynamicRiskController`."""

    return DynamicRiskController(
        config=json.loads(config_key),
        risk_profile=risk_profile,
    )


def get_cached_dynamic_risk_controller(
    config: Dict[str, Any], risk_profile: str
) -> DynamicRiskController:
    """Return a cached :class:`DynamicRiskController` instance."""

    return _get_dynamic_risk_controller(_config_to_key(config), risk_profile)


def clear_drc_cache() -> None:
    """Clear cached :class:`DynamicRiskController` instances."""

    _get_dynamic_risk_controller.cache_clear()


def get_drc_cache_size() -> int:
    """Return the current size of the DynamicRiskController cache."""

    return _get_dynamic_risk_controller.cache_info().currsize


def adjust_risk_with_uncertainty(
    uncertainty: float,
    max_position_size: float,
    stop_loss_pct: float,
    *,
    threshold: float = 0.8,
    position_factor: float = 0.5,
    stop_loss_factor: float = 1.2,
) -> Tuple[float, float]:
    """Adjust risk parameters based on metacognitive uncertainty."""
    if uncertainty >= threshold:
        logger.debug(
            "Nível de incerteza %.2f acima do limiar %.2f – reduzindo posição e aumentando stop loss",
            uncertainty,
            threshold,
        )
        return (
            max_position_size * position_factor,
            stop_loss_pct * stop_loss_factor,
        )
    return max_position_size, stop_loss_pct


def update_rolling_sharpe_ratios(strategy: Any, periods: int) -> None:
    """Update rolling Sharpe ratios for each sub-strategy."""
    for strat_id in ["s1", "s2", "s3"]:
        returns_series = pd.Series(strategy.sub_strategy_returns[strat_id])
        if len(returns_series) >= periods:
            window_returns = returns_series.iloc[-periods:]
            mean_ret = window_returns.mean()
            std_ret = window_returns.std()
            annual_factor = np.sqrt(8760)
            sharpe = (mean_ret / std_ret) * annual_factor if std_ret > 1e-9 else 0.0
            strategy.rolling_sharpe_ratios[strat_id] = (
                sharpe if not pd.isna(sharpe) else 0.0
            )
        else:
            strategy.rolling_sharpe_ratios[strat_id] = 0.0


def calculate_dynamic_weights(strategy: Any) -> None:
    """Allocate weights to sub-strategies based on positive Sharpe ratios."""
    sum_positive = sum(max(sr, 0) for sr in strategy.rolling_sharpe_ratios.values())
    if sum_positive < 1e-9:
        strategy.weights = {k: 0.0 for k in strategy.weights}
    else:
        for strat_id in strategy.weights:
            strategy.weights[strat_id] = (
                max(strategy.rolling_sharpe_ratios[strat_id], 0) / sum_positive
            )


def update_entropy_history(strategy: Any, entropy_value: float) -> None:
    """Append the entropy value to the history window."""
    if np.isfinite(entropy_value):
        clipped = float(np.clip(entropy_value, 0.0, 1.0))
        strategy.entropy_history.append(clipped)
        if len(strategy.entropy_history) > strategy.entropy_history_window:
            strategy.entropy_history.pop(0)


def detect_pulso_transcendencia(strategy: Any) -> bool:
    """Detect abrupt drops in entropy using z-score."""
    if len(strategy.entropy_history) < strategy.entropy_history_window:
        return False
    window = np.array(
        strategy.entropy_history[-strategy.entropy_history_window :], dtype=float
    )
    if window.size < 3:
        return False
    z_scores = zscore(window)
    last_z = z_scores[-1]
    if np.isnan(last_z):
        return False
    return bool(last_z < -3)
