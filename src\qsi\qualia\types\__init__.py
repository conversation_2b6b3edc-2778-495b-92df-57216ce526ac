from __future__ import annotations

"""Data types used across QUALIA modules."""

from dataclasses import dataclass
from typing import Optional


@dataclass
class SymbolMetrics:
    """Metrics for a trading symbol used in risk assessment."""

    rsi: Optional[float] = None
    volume_ratio: Optional[float] = None
    volatility: Optional[float] = None
    close: float = 0.0


__all__ = ["SymbolMetrics"]
