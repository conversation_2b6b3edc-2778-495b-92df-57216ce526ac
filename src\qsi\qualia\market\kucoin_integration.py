"""Kucoin exchange integration for QUALIA."""

from __future__ import annotations

import asyncio
import contextlib
import time
from typing import Any, Awaitable, Callable, Dict, Optional
import os
import socket
import aiohttp
import pandas as pd
from datetime import datetime

import kucoin.client as kucoin_client

# Compatibilidade com diferentes versões da biblioteca ``python-kucoin``.
# Algumas versões expõem ``AsyncClient`` dentro de ``kucoin.client`` enquanto
# outras utilizam o módulo ``kucoin.async_client``. Para evitar falhas de
# importação, definimos ``AsyncClient`` para o melhor caminho disponível e, como
# último recurso, reutilizamos ``kucoin.client.Client``.
try:  # pragma: no cover - caminho mais recente
    AsyncClient = kucoin_client.AsyncClient  # type: ignore[attr-defined]
except AttributeError:  # pragma: no cover - versões antigas
    try:
        from kucoin.async_client import AsyncClient  # type: ignore
    except Exception:  # pragma: no cover - fallback mínimo
        AsyncClient = kucoin_client.Client  # type: ignore
    else:
        kucoin_client.AsyncClient = AsyncClient  # type: ignore

from kucoin.asyncio import KucoinSocketManager

from ..utils.logger import get_logger
from ..config import load_market_defaults
from ..market.symbol_utils import normalize_symbol_async
from ..utils.network_resilience import CircuitBreaker

from ..market.base_integration import (
    CryptoDataFetcher,
    ConnectionInitializationError,
)
from ..market.kraken_integration import _load_exchange_credentials
from ..exchanges.ccxt_client import CCXTExchangeClient as CCXTClient

logger = get_logger("src.qualia.market.kucoin_integration")

def _market_defaults() -> dict:
    return load_market_defaults()


class KuCoinClient(CCXTClient):
    """Cliente específico para a exchange KuCoin."""

    def __init__(self, config: Dict[str, Any]):
        config["exchange_id"] = "kucoin"
        super().__init__(config)

    async def fetch_historical_data(
        self,
        spec: MarketSpec,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        use_cache: bool = True,
    ) -> pd.DataFrame:
        """
        Busca dados históricos da KuCoin.

        Este método é um wrapper em torno do `fetch_ohlcv` da classe base,
        garantindo a compatibilidade com a interface esperada por outros
        componentes do sistema como o DataWarmupManager.

        Args:
            spec: MarketSpec com símbolo e timeframe.
            start_date: Data de início.
            end_date: Data de fim.
            use_cache: Se deve usar cache.

        Returns:
            DataFrame com dados OHLCV.
        """
        logger.debug(
            f"KuCoinClient: Chamando fetch_historical_data para {spec.symbol}@{spec.timeframe}"
        )
        # O método fetch_ohlcv já implementa a lógica de paginação e cache.
        # Simplesmente o chamamos com os parâmetros corretos.
        return await self.fetch_ohlcv(
            spec=spec,
            start_date=start_date,
            end_date=end_date,
            use_cache=use_cache,
        )


class KucoinIntegration(CryptoDataFetcher):
    """Implements REST and WebSocket features for Kucoin."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        password: Optional[str] = None,
        conn_timeout: Optional[float] = None,
        conn_retries: Optional[int] = None,
        ticker_timeout: Optional[float] = None,
        ticker_retries: Optional[int] = None,
        ticker_backoff_base: Optional[float] = None,
        use_websocket: bool = True,
        ohlcv_timeout: Optional[float] = None,
        max_init_timeout: Optional[float] = None,
        fail_threshold: Optional[int] = None,
        recovery_timeout: Optional[float] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Initialize the Kucoin integration.

        Parameters
        ----------
        api_key, api_secret, password
            Credentials for the exchange. When ``None`` values are loaded from
            environment variables ``KUCOIN_API_KEY``, ``KUCOIN_SECRET_KEY`` and
            ``KUCOIN_PASSPHRASE`` respectively.
        conn_timeout, conn_retries
            Connection parameters for ``load_markets``.
        ticker_timeout, ticker_retries, ticker_backoff_base
            Parameters controlling ticker retrieval via REST.
        use_websocket
            Whether to enable the WebSocket client.
        ohlcv_timeout
            Timeout for fetching OHLCV data.
        max_init_timeout
            Maximum time allowed to establish the WebSocket connection.
        fail_threshold, recovery_timeout
            Values used for ``CircuitBreaker`` protecting ``fetch_ohlcv`` and
            ``fetch_ticker``.
        config : dict, optional
            Dicionário de configuração completo do sistema QUALIA.
        """

        # Valores default otimizados para inicialização mais rápida
        default_conn_timeout = _market_defaults().get("timeouts", {}).get("connection", 20.0)
        default_conn_retries = _market_defaults().get("retries", {}).get("conn", 3)
        default_max_init_timeout = 60.0  # Timeout máximo total de inicialização

        # Timeout OHLCV: a Kucoin frequentemente leva 25-35 s quando solicita 300 candles.
        # Definimos fallback de 45 s para evitar cancelamentos prematuros.
        if ohlcv_timeout is None:
            try:
                ohlcv_timeout = float(
                    os.getenv(
                        "OHLCV_TIMEOUT",
                        str(_market_defaults().get("timeouts", {}).get("ohlcv", 45.0)),
                    )
                )
            except ValueError:
                ohlcv_timeout = _market_defaults().get("timeouts", {}).get("ohlcv", 45.0)

        # Carregar credenciais e configurações
        creds = _load_exchange_credentials(
            prefix="KUCOIN",
            api_key=api_key,
            api_secret=api_secret,
            password=password,
            password_env="KUCOIN_PASSPHRASE",
            secret_env="KUCOIN_SECRET_KEY",
            ticker_timeout=ticker_timeout,
            ticker_timeout_env="TICKER_TIMEOUT",
            default_ticker_timeout=_market_defaults().get("timeouts", {}).get("ticker", 25.0),
            ticker_retries=ticker_retries,
            ticker_backoff_base=ticker_backoff_base,
            conn_timeout=conn_timeout or default_conn_timeout,
            conn_retries=conn_retries or default_conn_retries,
        )

        # Inicializar superclasse com configurações otimizadas
        super().__init__(
            api_key=creds["api_key"],
            api_secret=creds["api_secret"],
            password=creds["password"],
            exchange_id="kucoin",
            conn_timeout=creds["conn_timeout"],
            conn_retries=creds["conn_retries"],
            ohlcv_timeout=ohlcv_timeout,
            config=config,
        )

        # Configurações específicas da Kucoin
        self.ticker_timeout = creds["ticker_timeout"]
        self.ticker_retries = creds["ticker_retries"]
        self.ticker_backoff_base = creds.get("ticker_backoff_base", ticker_backoff_base)
        self.use_websocket = use_websocket
        self._ws_manager: Optional[KucoinSocketManager] = None
        self._rest_client: Optional[kucoin_client.AsyncClient] = None
        self._market_cache: Optional[Dict[str, Any]] = None
        self._max_init_timeout = max_init_timeout or default_max_init_timeout
        self._init_start_time: Optional[float] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._ping_failures = 0
        self._ws_symbol: Optional[str] = None

        if fail_threshold is None:
            try:
                fail_threshold = int(os.getenv("API_FAIL_THRESHOLD", "5"))
            except ValueError:
                fail_threshold = 5
        if recovery_timeout is None:
            try:
                recovery_timeout = float(os.getenv("API_RECOVERY_TIMEOUT", "60"))
            except ValueError:
                recovery_timeout = 60.0

        self.ticker_circuit = CircuitBreaker(fail_threshold, recovery_timeout)
        self.ohlcv_circuit = CircuitBreaker(fail_threshold, recovery_timeout)

        # Aplicar timeout OHLCV ajustado
        self.ohlcv_timeout = ohlcv_timeout

    async def _ensure_ipv4_session(self) -> None:
        """Configure ccxt to use an IPv4-only aiohttp session."""

        loop = asyncio.get_running_loop()
        connector = aiohttp.TCPConnector(
            ssl=self.exchange.ssl_context,
            loop=loop,
            family=socket.AF_INET,
            enable_cleanup_closed=True,
        )
        if getattr(self.exchange, "session", None):
            await self.exchange.session.close()
        if getattr(self.exchange, "tcp_connector", None):
            await self.exchange.tcp_connector.close()
        self.exchange.tcp_connector = connector
        self.exchange.session = aiohttp.ClientSession(
            loop=loop,
            connector=connector,
            trust_env=getattr(self.exchange, "aiohttp_trust_env", False),
        )

    def is_connected(self) -> bool:
        """Return True if the REST or ccxt session is active."""

        if self._rest_client is not None:
            session = getattr(self._rest_client, "session", None)
            return bool(session and not getattr(session, "closed", True))

        session = getattr(self.exchange, "session", None)
        return bool(session and not getattr(session, "closed", True))

    async def initialize_connection(self) -> None:
        """Initialize connection ensuring IPv4 connectivity."""

        await self._ensure_ipv4_session()
        await super().initialize_connection()

    async def start_websocket(
        self,
        symbol: str,
        on_message: Callable[..., Awaitable[None]] | None = None,
        private: bool = False,
    ) -> None:
        """Open WebSocket connection and register callback.

        Parameters
        ----------
        symbol : str
            Trading pair to subscribe to.
        on_message : Callable[..., Awaitable[None]] | None, optional
            Coroutine called for every message received. If ``None``, messages
            are ignored.
        private : bool, optional
            Whether to use the private feed. Defaults to ``False``.
        """
        self._init_start_time = time.time()
        loop = asyncio.get_running_loop()
        attempt = 1
        last_error: Exception | None = None
        handler = on_message or (lambda *_: None)

        # Tentar inicializar REST e WebSocket em paralelo
        async def init_rest() -> kucoin_client.AsyncClient:
            client = kucoin_client.AsyncClient(
                self.exchange.apiKey,
                self.exchange.secret,
                self.exchange.password,
            )
            return client

        async def init_ws(client: kucoin_client.AsyncClient) -> KucoinSocketManager:
            return await KucoinSocketManager.create(loop, client, handler, private)

        while attempt <= self.conn_retries:
            try:
                # Inicializar REST e WebSocket em paralelo
                client_task = asyncio.create_task(init_rest())
                client = await client_task
                self._rest_client = client

                # Criar WebSocket manager
                ws_task = asyncio.create_task(init_ws(client))
                self._ws_manager = await ws_task

                # Subscrever ao ticker
                await self.subscribe_ticker(symbol)
                self._ws_symbol = symbol
                self._heartbeat_task = asyncio.create_task(self._heartbeat_loop(symbol))
                return
            except Exception as exc:
                last_error = exc
                elapsed_time = time.time() - self._init_start_time

                logger.error(
                    "Tentativa de conexão websocket %s %s/%s falhou: %s (tempo: %.1fs)",
                    self.exchange_id,
                    attempt,
                    self.conn_retries,
                    exc,
                    elapsed_time,
                    exc_info=True,
                )

                if elapsed_time >= self._max_init_timeout:
                    raise ConnectionInitializationError(
                        f"Tempo máximo de inicialização ({self._max_init_timeout}s) excedido"
                    ) from last_error

                if client:
                    try:
                        await client.close()
                    except Exception as close_exc:  # pragma: no cover - cleanup errors
                        logger.error(
                            "Falha ao fechar cliente REST %s: %s",
                            self.exchange_id,
                            close_exc,
                            exc_info=True,
                        )
                self._rest_client = None

                if attempt >= self.conn_retries:
                    break

                # Ajustar o backoff com base no tempo decorrido
                backoff = min(2 * attempt, self.conn_timeout)
                if elapsed_time + backoff > self._max_init_timeout:
                    backoff = max(0, self._max_init_timeout - elapsed_time)

                await asyncio.sleep(backoff)
                attempt += 1

        raise ConnectionInitializationError(
            f"Não foi possível estabelecer conexão websocket com a {self.exchange_id}"
        ) from last_error

    async def load_markets(self, reload: bool = False) -> Dict[str, Any]:
        """Carrega mercados da Kucoin com cache."""
        if not reload:
            if self._market_cache:
                return self._market_cache
            if getattr(self.exchange, "markets", None):
                self._market_cache = self.exchange.markets
                return self._market_cache

        try:
            markets = await super().load_markets()
            self._market_cache = markets
            return markets
        except Exception as e:
            if self._market_cache:
                logger.warning(
                    "Falha ao recarregar mercados, usando cache: %s", e, exc_info=True
                )
                return self._market_cache
            raise

    async def subscribe_ticker(self, symbol: str) -> None:
        """Subscribe to ticker updates for ``symbol`` via WebSocket."""

        if not self._ws_manager:
            raise RuntimeError("WebSocket não iniciado")
        await self._ws_manager.subscribe(f"/market/ticker:{symbol}")

    async def subscribe_order_book(self, symbol: str, depth: int = 5) -> None:
        """Subscribe to the order book feed for ``symbol``."""

        if not self._ws_manager:
            raise RuntimeError("WebSocket não iniciado")
        topic = "/spotMarket/level2Depth20" if depth > 5 else "/spotMarket/level2Depth5"
        await self._ws_manager.subscribe(f"{topic}:{symbol}")

    async def _heartbeat_loop(self, symbol: str, interval: float = 30.0) -> None:
        """Send periodic pings to keep the connection alive."""

        while self._ws_manager:
            await asyncio.sleep(interval)
            try:
                await self._ws_manager._conn.send_ping()
                self._ping_failures = 0
            except Exception as exc:  # pragma: no cover - network dependent
                self._ping_failures += 1
                logger.warning(
                    "Falha ao enviar ping %s (%s/2): %s",
                    self.exchange_id,
                    self._ping_failures,
                    exc,
                )
                if self._ping_failures >= 2:
                    self._ping_failures = 0
                    try:
                        logger.warning(
                            "Reiniciando assinatura de ticker %s após falhas de ping",
                            symbol,
                        )
                        await self.subscribe_ticker(symbol)
                    except Exception as sub_exc:  # pragma: no cover - safety net
                        logger.error(
                            "Erro ao reiniciar watch_ticker: %s", sub_exc, exc_info=True
                        )

    async def watch_ticker(self, symbol: str) -> Optional[dict]:
        """Delegate to :meth:`CryptoDataFetcher.watch_ticker`."""

        return await super().watch_ticker(symbol)

    async def close_websocket(self) -> None:
        """Close the WebSocket connection and REST client if they exist."""

        if self._ws_manager:
            await self._ws_manager.close()
            self._ws_manager = None
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._heartbeat_task
            self._heartbeat_task = None
        if self._rest_client:
            await self._rest_client.close()
            self._rest_client = None

    async def close(self) -> None:
        """Close all connections and clean up resources."""
        
        # Fechar WebSocket e REST client primeiro
        await self.close_websocket()
        
        # Fechar sessão HTTP do exchange se existir
        if hasattr(self.exchange, 'session') and self.exchange.session:
            if not self.exchange.session.closed:
                await self.exchange.session.close()
                logger.info("Sessão HTTP do KuCoin fechada")
        
        # Fechar connector TCP se existir
        if hasattr(self.exchange, 'tcp_connector') and self.exchange.tcp_connector:
            await self.exchange.tcp_connector.close()
            logger.info("TCP connector do KuCoin fechado")
        
        # Chamar método close da classe pai
        await super().close()

    async def fetch_positions(
        self, symbols: Optional[list[str]] = None
    ) -> list[dict[str, Any]]:
        """Return open positions for the given symbols.

        Kucoin's ccxt implementation does not expose ``fetchPositions``. This
        method falls back to the futures REST endpoint and parses a minimal
        structure compatible with ``PositionManager``.

        Parameters
        ----------
        symbols : list[str], optional
            List of trading pairs to filter.

        Returns
        -------
        list[dict[str, Any]]
            Parsed open positions.
        """

        if self.fetch_positions_supported is None:
            self.fetch_positions_supported = bool(
                getattr(self.exchange, "has", {}).get("fetchPositions")
            )

        if self.fetch_positions_supported:
            return await super().fetch_positions(symbols)

        params: Dict[str, Any] = {}
        if symbols:
            normalized: list[str] = []
            for sym in symbols:
                try:
                    normalized.append(await normalize_symbol_async(sym, self.exchange))
                except ValueError as exc:
                    logger.error("Símbolo inválido %s: %s", sym, exc)
            params["symbol"] = ",".join(normalized)

        try:
            await self._respect_rate_limit()
            response = await self.exchange.futuresPrivateGetPositions(params)
        except Exception as exc:  # pragma: no cover - network failure
            logger.error("Erro ao buscar posições Kucoin: %s", exc, exc_info=True)
            return []

        self.fetch_positions_supported = True

        data = response.get("data") or []
        positions: list[dict[str, Any]] = []
        for pos in data:
            qty = float(
                pos.get("currentQty")
                or pos.get("availQty")
                or pos.get("positionQty")
                or pos.get("pos")
                or 0
            )
            side = pos.get("posSide") or pos.get("side")
            if side is None:
                side = "long" if qty >= 0 else "short"
            positions.append(
                {
                    "symbol": pos.get("symbol"),
                    "contracts": abs(qty),
                    "side": side,
                    "info": pos,
                }
            )

        return positions

    def max_candles(self, timeframe: str) -> int:
        """Return the maximum number of candles Kucoin can provide."""

        del timeframe  # Kucoin limit is constant across timeframes
        return 1500

    async def _pre_cache_tickers(self) -> None:
        """Pré-carrega tickers via REST antes de usar WebSocket."""
        if not hasattr(self.exchange, "markets") or not self.exchange.markets:
            return

        # Busca símbolos mais comuns
        common_symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT"]
        for symbol in common_symbols:
            if symbol in self.exchange.markets:
                try:
                    logger.info(f"Pré-carregando ticker para {symbol}")
                    ticker = await asyncio.wait_for(
                        self.exchange.fetch_ticker(symbol), timeout=10.0
                    )
                    if ticker:
                        self._store_ticker_cache(symbol, ticker)
                        logger.info(f"Ticker {symbol} pré-carregado com sucesso")
                except Exception as e:
                    logger.debug(f"Falha ao pré-carregar ticker {symbol}: {e}")

                # Pequena pausa entre requests
                await asyncio.sleep(0.5)
