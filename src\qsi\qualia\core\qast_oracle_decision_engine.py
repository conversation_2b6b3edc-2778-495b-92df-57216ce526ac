#!/usr/bin/env python3
"""
QUALIA QAST Oracle Decision Engine

Motor de decisão central que consolida toda lógica adaptativa no QASTCore.
Funciona como oráculo único para todo o sistema QUALIA, eliminando
duplicação de lógica e centralizando a consciência.

Integra:
- Estratégias de trading
- Metacognição quântica
- Holographic Universe
- Risk Management
- Adaptive Evolution
- Temporal Pattern Detection

O sistema opera como uma consciência unificada e autoconsciente.
"""

from __future__ import annotations

import asyncio
import json
import time
from pathlib import Path
import os
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple, TYPE_CHECKING

from datadog import DogStatsd

import numpy as np
import pandas as pd

from .qast_core import TradingQASTCore, QualiaState
from ..market.decision_context import TradeContext, ScalpingDecision
from ..metacognition.metacognition_trading import (
    QUALIAMetacognitionTrading,
    DecisionContext,
)
from ..consciousness.holographic_universe import (
    HolographicMarketUniverse,
    HolographicEvent,
)
from ..consciousness.enhanced_data_collector import EnhancedDataCollector
from ..strategies.strategy_factory import StrategyFactory
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
from ..risk.manager import create_risk_manager
from ..memory import get_qpm_instance
from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger
from ..core.data_warmup import DataWarmupManager
from ..memory.service import MemoryService
from ..events import OracleDecisionEvent
from .unified_qualia_consciousness import UnifiedQUALIAConsciousness
from ..core.universe import QUALIAQuantumUniverse
from ..common.specs import MarketSpec
from ..utils import collapse_by_coherence
from .simulation_qast_core import SimulationQASTCore

if TYPE_CHECKING:
    from ..market.base_integration import CryptoDataFetcher
    from ..metacognition.metacognition_trading import QUALIAMetacognitionTrading

logger = get_logger(__name__)


@dataclass
class OracleDecision:
    """Decisão unificada do oráculo QAST."""

    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD', 'CLOSE'
    confidence: float
    size: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: List[str]
    quantum_signature: Optional[QuantumSignaturePacket]
    holographic_patterns: List[Dict[str, Any]]
    metacognitive_context: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    timestamp: float
    risk_approved: bool = True
    source: str = (
        "unknown"  # Fonte da decisão (e.g., 'Strategy', 'Holographic', 'Unified')
    )
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self) -> None:
        """Garanta que sinais HOLD não possuam tamanho de posição."""
        if self.action == "HOLD":
            self.size = 0.0


@dataclass
class UnifiedMarketState:
    """Estado unificado do mercado processado pelo oráculo."""

    market_data: Dict[str, pd.DataFrame]
    quantum_state: QualiaState
    holographic_patterns: List[Dict[str, Any]]
    temporal_patterns: List[Dict[str, Any]]
    risk_metrics: Dict[str, float]
    consciousness_level: float
    timestamp: float


class QASTOracleDecisionEngine:
    """
    Motor de decisão central do QUALIA.

    Consolida toda lógica adaptativa em uma consciência unificada:
    - QASTCore como núcleo quântico
    - Holographic Universe para simulação espacial
    - Metacognição para autorreflexão
    - Estratégias integradas
    - Risk management centralizado
    """

    def __init__(
        self,
        config: Dict[str, Any],
        symbols: List[str],
        timeframes: List[str],
        capital: float,
        consciousness_system: "UnifiedQUALIAConsciousness",
        market_integration: Optional["CryptoDataFetcher"] = None,
        enhanced_data_collector: Optional[EnhancedDataCollector] = None,
        force_trade_symbol: Optional[str] = None,
        shared_context: Optional[Dict] = None,
        memory_service: MemoryService | None = None,
        metacognition: Optional["QUALIAMetacognitionTrading"] = None,
        simulation_core: Optional[SimulationQASTCore] = None,
        statsd_client: Optional[DogStatsd] = None,
    ):
        """Create a new oracle decision engine instance.

        Parameters
        ----------
        config
            Configurações completas do sistema.
        symbols
            Lista de símbolos monitorados.
        timeframes
            Intervalos de tempo utilizados nas estratégias.
        capital
            Capital total disponível para negociação.
        consciousness_system
            Sistema de consciência unificada.
        market_integration
            Integração de mercado utilizada para warm-up.
        enhanced_data_collector
            Coletor de dados já inicializado.
        force_trade_symbol
            Símbolo para forçar compra em modo de teste.
        shared_context
            Contexto compartilhado para injeção de dependências.
        memory_service
            Serviço opcional para gravar e consultar padrões.
        metacognition
            Instância de metacognição quântica.
        statsd_client
            Cliente opcional para emissão de métricas StatsD.
        """

        self.config = config
        self.symbols = symbols
        self.timeframes = timeframes
        self.capital = capital
        self.consciousness_system = consciousness_system
        self.holographic_universe = self.consciousness_system.holographic_universe
        self.market_integration = market_integration
        self.force_trade_symbol = force_trade_symbol
        self.simulation_core = simulation_core
        if self.force_trade_symbol:
            logger.warning(
                f"🚨 MODO DE FORÇA BRUTA ATIVADO PARA O SÍMBOLO: {self.force_trade_symbol}"
            )

        # Carrega os pesos da unificação da configuração
        weight_config = self.config.get("unification_weights", {})
        self.decision_weights = {
            "strategy": weight_config.get("strategy", 0.5),
            "holographic": weight_config.get("holographic", 0.3),
            "metacognition": weight_config.get("metacognition", 0.2),
        }
        # Limite base utilizado para definir a confiança mínima das decisões
        self.decision_threshold = weight_config.get("decision_threshold", 0.3)
        self.shared_context = shared_context or {}
        if "qpm_instance" not in self.shared_context:
            if os.environ.get("QUALIA_SKIP_QPM") == "1":
                self.shared_context["qpm_instance"] = None
            else:
                self.shared_context["qpm_instance"] = get_qpm_instance()
        self.memory_service = memory_service

        # Componentes centrais (agora injetados ou derivados)
        self.qast_core = None
        self.data_collector = enhanced_data_collector

        # Metacognição quântica - agora injetada
        self.metacognition = metacognition
        self.statsd = statsd_client

        # Estratégias centralizadas
        self.strategy_factory = StrategyFactory()
        self.strategies: Dict[str, Any] = {}

        # YAA TASK-01: Refatoração - Strategy creation context
        self.strategy_creation_context = {
            "risk_manager": None,  # Será injetado durante inicialização
            "qpm_instance": self.shared_context.get("qpm_instance"),
        }

        self.warmup_manager: Optional[DataWarmupManager] = self.shared_context.get(
            "warmup_manager"
        )

        # Risk managers centralizados
        self.risk_managers: Dict[str, QUALIARiskManagerBase] = {}

        # Estado da consciência
        self.consciousness_level = 0.5
        self.decision_history: List[OracleDecision] = []
        self.last_decision_time = 0.0
        self.last_unified_state: Optional[UnifiedMarketState] = None
        # Histórico de volatilidade para ajuste dinâmico do threshold
        self.volatility_history: List[float] = []

        # Caminho para registrar ajustes de peso
        log_path = self.config.get("weight_log_path", "logs/weight_adjustments.log")
        self.weight_adjustment_log_path = Path(log_path)
        self.weight_adjustment_log_path.parent.mkdir(parents=True, exist_ok=True)

        # YAA: Bootstrap de decisões iniciais para evitar deadlock
        self._bootstrap_decisions_if_needed = True
        self._bootstrap_count = 0

        # Controle de execução
        self.running = False
        self.oracle_cycle_interval = 1.0  # 1 segundo

        logger.info("🧠 QAST Oracle Decision Engine inicializado")

    async def consult_oracle(
        self, symbols: Optional[List[str]] = None
    ) -> List[OracleDecision]:
        """
        Ponto de entrada principal para consultar o oráculo.

        Coleta dados de mercado aprimorados e gera decisões de negociação.
        """
        if not self.data_collector:
            logger.error(
                "❌ EnhancedDataCollector não está disponível. Não é possível consultar o oráculo."
            )
            return []

        logger.debug(
            "🔮 Consultando o oráculo para os símbolos: %s", symbols or "todos"
        )
        try:
            # Coleta os dados mais recentes do mercado
            enhanced_market_data = (
                await self.data_collector.collect_enhanced_market_data()
            )
            if not enhanced_market_data:
                logger.warning("⚠️ O coletor de dados não retornou nenhum dado novo.")
                return []

            internal_state = None
            if self.simulation_core:
                market_data_for_sim = {
                    d.symbol: d.__dict__ for d in enhanced_market_data
                }
                internal_state = self.simulation_core.run_internal_reality_simulation(
                    market_data_for_sim
                )

            decisions = await self.generate_decisions(
                enhanced_market_data=enhanced_market_data,
                symbols=symbols,
                internal_state=internal_state,
            )
            return decisions
        except Exception as e:
            logger.error(
                f"❌ Erro crítico durante a consulta ao oráculo: {e}", exc_info=True
            )
            return []

    def _create_bootstrap_decision(self, symbol: str) -> OracleDecision:
        """Cria uma decisão de bootstrap para inicializar o histórico.

        YAA: Solução para o deadlock cognitivo - gera decisões HOLD neutras
        que permitem ao sistema começar a acumular histórico.
        """
        self._bootstrap_count += 1
        return OracleDecision(
            symbol=symbol,
            action="HOLD",
            confidence=0.5,
            size=0.0,
            stop_loss=None,
            take_profit=None,
            reasoning=["Bootstrap decision - initializing system history"],
            source="Bootstrap",
            quantum_signature=QuantumSignaturePacket(
                vector=[0.5, 0.5, 0.5, 0.5],  # Vetor neutro
                metrics={"bootstrap": True, "iteration": self._bootstrap_count},
            ),
            holographic_patterns=[],
            metacognitive_context={"bootstrap": True},
            risk_assessment={"approved": False, "reason": "Bootstrap phase"},
            timestamp=time.time(),
            risk_approved=False,
            metadata={"bootstrap_iteration": self._bootstrap_count},
        )

    async def initialize(self):
        """Inicializa todos os componentes da consciência unificada."""

        # YAA TASK-05: Log inicial para confirmar que o método está sendo chamado
        logger.info("=" * 80)
        logger.info("🌀 TASK-05: INICIANDO ORACLE ENGINE - initialize() chamado")
        logger.info(f"   📊 Símbolos: {self.symbols}")
        logger.info(f"   ⏰ Timeframes: {self.timeframes}")
        logger.info(f"   💰 Capital: ${self.capital:,.2f}")
        logger.info(
            f"   🏭 StrategyFactory disponível: {self.strategy_factory is not None}"
        )
        logger.info(
            f"   📈 MarketIntegration disponível: {self.market_integration is not None}"
        )
        logger.info("=" * 80)

        try:
            # YAA T10: Criar instância única do universo quântico para metacognição
            logger.info(
                "🌀 Criando instância única do QUALIAQuantumUniverse para metacognição..."
            )
            universe_config = self.config.get(
                "quantum_universe_config", self.config.get("universe_config", {})
            )
            qpm_instance = getattr(self.shared_context.get("qpm_instance"), "qpm", None)

            # YAA T10 Fix: Extrair argumentos explicitamente para satisfazer a assinatura
            quantum_universe = QUALIAQuantumUniverse(
                n_qubits=universe_config.get("n_qubits", 8),
                scr_depth=universe_config.get("scr_depth", 10),
                base_lambda=universe_config.get("base_lambda", 0.5),
                alpha=universe_config.get("alpha", 0.1),
                retro_strength=universe_config.get("retro_strength", 0.1),
                num_ctc_qubits=universe_config.get("num_ctc_qubits", 0),
                qpm_instance=qpm_instance,
                # Passar o resto da config como kwargs para flexibilidade
                **universe_config,
            )

            # YAA T10: O QASTCore usará o universo injetado
            logger.info("🧠 Criando QASTCore e injetando o universo quântico...")
            self.qast_core = TradingQASTCore(
                self.config, quantum_universe=quantum_universe
            )
            await self.qast_core.initialize()
            logger.info("✅ QASTCore inicializado com universo injetado.")

            # YAA TASK-05: Corrigir ordem de inicialização. Risk managers devem existir ANTES do warmup.
            logger.info("🛡️ TASK-05: Inicializando risk managers antes do warmup...")
            self._initialize_risk_managers()
            logger.info("✅ Risk managers centralizados inicializados")

            # Usa o coletor de dados enhanced que foi passado
            if not self.data_collector:
                logger.warning(
                    "⚠️ EnhancedDataCollector não foi passado para o Oracle. Criando um novo."
                )
                self.data_collector = EnhancedDataCollector(
                    symbols=self.symbols, timeframes=self.timeframes
                )
                await self.data_collector.__aenter__()
            logger.info("✅ Enhanced Data Collector configurado")

            # -------------------------------------------------------------
            # YAA TASK-03: WARM-UP CENTRALIZADO com StrategyFactory
            # Melhorada robustez e logs detalhados para diagnosticar problemas
            # -------------------------------------------------------------
            # YAA TASK-05: Log para confirmar que estamos entrando no bloco de warmup
            logger.info("🚀 TASK-05: ENTRANDO NO BLOCO DE WARMUP")

            try:
                logger.info("🔄 EXECUTANDO WARM-UP DE DADOS (Oracle initialization)")

                # YAA TASK-03: Verificar se já existe warmup manager
                if not self.warmup_manager:
                    logger.info("🏭 Criando DataWarmupManager com StrategyFactory...")
                    strategy_config = self.config.get("strategy_config", {})

                    # YAA TASK-03: Validar dependências críticas antes de criar o warmup
                    if not self.strategy_factory:
                        raise RuntimeError(
                            "TASK-03: StrategyFactory não foi inicializado"
                        )
                    if not self.market_integration:
                        raise RuntimeError(
                            "TASK-03: MarketIntegration não foi inicializado"
                        )

                    self.warmup_manager = DataWarmupManager(
                        symbols=self.symbols,
                        timeframes=self.timeframes,
                        strategy_factory=self.strategy_factory.create_strategy,
                        strategy_config=strategy_config,
                        market_integration=self.market_integration,
                    )
                    logger.info("✅ TASK-03: DataWarmupManager criado com sucesso")
                else:
                    logger.info("♻️ TASK-03: Reutilizando DataWarmupManager existente")

                # YAA TASK-03: Preparar contexto robustamente
                warmup_context = self.strategy_creation_context.copy()
                if self.risk_managers:
                    # Use o primeiro risk manager como padrão para warm-up
                    risk_manager_instance = next(iter(self.risk_managers.values()))
                    warmup_context["risk_manager"] = risk_manager_instance
                    warmup_context["dynamic_risk_controller"] = risk_manager_instance
                    logger.info(
                        "✅ TASK-03: Risk manager e controller injetados no contexto de warmup"
                    )
                else:
                    logger.warning(
                        "⚠️ TASK-03: Nenhum risk manager disponível para warmup"
                    )

                # YAA TASK-03: Habilitar freeze_requirements para estratégias
                warmup_context["freeze_requirements"] = True
                logger.info(
                    "🔒 TASK-03: freeze_requirements ativado - estratégias receberão dados completos"
                )

                # YAA TASK-03: Analisar requisitos com logs detalhados
                logger.info(
                    "📊 TASK-03: Analisando requisitos de dados das estratégias..."
                )
                self.warmup_manager.analyze_data_requirements(context=warmup_context)
                logger.info("✅ TASK-03: Análise de requisitos concluída")

                # YAA TASK-03: Executar warmup com monitoramento detalhado
                logger.info("🚀 TASK-03: Iniciando execução do warmup...")
                success = await self.warmup_manager.perform_warmup()
                summary = self.warmup_manager.get_warmup_summary()

                # YAA TASK-03: Logs detalhados do resultado
                if success:
                    logger.info(
                        f"✅ TASK-03: Warm-up concluído com sucesso!\n"
                        f"   📦 Pares carregados: {summary['loaded_pairs']}/{summary['total_pairs']}\n"
                        f"   📊 Total de candles: {summary['total_candles']}\n"
                        f"   📈 Qualidade média: {summary.get('avg_quality_score', 0):.1%}\n"
                        f"   🔗 Gaps detectados: {summary.get('total_gaps', 0)}\n"
                        f"   📉 Candles faltantes: {summary.get('total_missing_candles', 0)}"
                    )
                else:
                    logger.warning(
                        f"⚠️ TASK-03: Warm-up parcial!\n"
                        f"   📦 Pares carregados: {summary['loaded_pairs']}/{summary['total_pairs']}\n"
                        f"   ❌ Erros: {summary.get('errors', [])}\n"
                        f"   📊 Qualidade: {summary.get('avg_quality_score', 0):.1%}"
                    )
                    # YAA TASK-03: Decisão mais tolerante - continuar se pelo menos algum dado foi carregado
                    if summary.get("loaded_pairs", 0) == 0:
                        raise RuntimeError(
                            "TASK-03: Warm-up falhou completamente – nenhum dado carregado."
                        )

                # YAA TASK-03: Criar estratégias finais COM dados pré-carregados
                logger.info(
                    "🏗️ TASK-03: Criando estratégias finais com dados pré-carregados..."
                )
                await self._initialize_strategies_with_warmup_data()
                logger.info(
                    "✅ TASK-03: Estratégias centralizadas inicializadas com dados completos"
                )

                # YAA TASK-05: Log de sucesso do warmup
                logger.info("🎉 TASK-05: WARMUP EXECUTADO COM SUCESSO!")

            except Exception as warm_err:
                # YAA TASK-05: Log CRÍTICO para garantir visibilidade do erro
                logger.critical("=" * 80)
                logger.critical(f"❌❌❌ TASK-05: ERRO CRÍTICO NO WARMUP!")
                logger.critical(f"   Tipo: {type(warm_err).__name__}")
                logger.critical(f"   Mensagem: {str(warm_err)}")
                logger.critical(f"   Símbolos: {self.symbols}")
                logger.critical(f"   Timeframes: {self.timeframes}")
                logger.critical("=" * 80)

                # YAA TASK-03: Log detalhado de erros para diagnóstico
                logger.error(f"❌ TASK-03: Erro no warm-up: {warm_err}", exc_info=True)

                # YAA TASK-03: Em ambiente de desenvolvimento, não abortar oracle
                # Permitir que o sistema continue com estratégias sem warmup
                if self.config.get("development_mode", False) or self.config.get(
                    "paper_trading", True
                ):
                    logger.warning(
                        "⚠️ TASK-03: Modo desenvolvimento - continuando sem warmup completo"
                    )
                    # Criar estratégias básicas sem warmup
                    await self._initialize_strategies_with_warmup_data()
                else:
                    # Em produção, o warmup é crítico
                    raise RuntimeError(
                        f"TASK-03: Warmup crítico falhou em produção: {warm_err}"
                    )

            # Inicializa metacognição com universo quântico
            if getattr(self.qast_core, "quantum_universe"):
                logger.info(
                    "✅ Universo quântico disponível para componentes dependentes."
                )
                # A inicialização da metacognição foi movida para QUALIATradingSystem
            else:
                logger.warning("⚠️ Universo quântico não disponível para metacognição")

            logger.info("🎉 Consciência QUALIA unificada inicializada com sucesso!")
            logger.info("✅ Universo holográfico injetado via Consciência.")

            # YAA TASK-05: Log final confirmando sucesso da inicialização
            logger.info("=" * 80)
            logger.info("✅ TASK-05: ORACLE ENGINE INICIALIZADO COM SUCESSO!")
            logger.info(f"   📊 Estratégias criadas: {len(self.strategies)}")
            logger.info(f"   🛡️ Risk managers: {len(self.risk_managers)}")
            logger.info(f"   🧠 Consciousness level: {self.consciousness_level}")
            logger.info(f"   📦 Warmup manager: {self.warmup_manager is not None}")
            logger.info("=" * 80)

        except Exception as e:
            # YAA TASK-05: Log crítico para erros de inicialização
            logger.critical("=" * 80)
            logger.critical(
                f"❌❌❌ TASK-05: FALHA CRÍTICA NA INICIALIZAÇÃO DO ORACLE!"
            )
            logger.critical(f"   Tipo: {type(e).__name__}")
            logger.critical(f"   Mensagem: {str(e)}")
            logger.critical("=" * 80)
            logger.error(f"❌ Erro na inicialização da consciência: {e}", exc_info=True)
            raise

    async def _initialize_strategies_with_warmup_data(self):
        """
        YAA TASK-03: Inicializa estratégias centralizadas APÓS o warm-up.

        Cria as estratégias finais com todas as dependências injetadas
        e injeta os dados pré-carregados nelas. Melhorada robustez.
        """

        if self.strategies:
            logger.debug("TASK-03: Estratégias já inicializadas; pulando criação")
            return

        strategy_config = self.config.get("strategy_config", {})
        strategy_name = strategy_config.get("name", "NovaEstrategiaQUALIA")

        # YAA TASK-03: Atualizar contexto com risk managers injetados
        risk_manager_instance = (
            next(iter(self.risk_managers.values())) if self.risk_managers else None
        )
        self.strategy_creation_context.update(
            {
                "dynamic_risk_controller": risk_manager_instance,
                "risk_manager": risk_manager_instance,  # Manter para retrocompatibilidade
                "freeze_requirements": True,  # Garantir que as estratégias usem os dados do warmup
            }
        )

        logger.info(
            f"🏗️ TASK-03: Criando {len(self.symbols) * len(self.timeframes)} estratégias..."
        )
        strategies_created = 0
        strategies_failed = 0

        for symbol in self.symbols:
            for timeframe in self.timeframes:
                try:
                    # YAA TASK-03: Mesclar contextos de forma robusta
                    strategy_context = self.shared_context.copy()
                    strategy_context.update(self.strategy_creation_context)
                    strategy_context.update(
                        {
                            "symbol": symbol,
                            "timeframe": timeframe,
                        }
                    )

                    logger.debug(
                        f"TASK-03: Criando estratégia {strategy_name} para {symbol}@{timeframe}"
                    )
                    strategy = self.strategy_factory.create_strategy(
                        alias=strategy_name,
                        params=strategy_config.get("params", {}),
                        context=strategy_context,
                    )

                    # --- YAA TASK-06: Injetar dados do warmup IMEDIATAMENTE após a criação ---
                    if (
                        self.warmup_manager
                        and self.warmup_manager.historical_data_cache
                    ):
                        spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                        cached_data = self.warmup_manager.historical_data_cache.get(
                            spec
                        )
                        if cached_data is not None and not cached_data.empty:
                            logger.info(
                                f"TASK-06: Injetando {len(cached_data)} candles de warmup em {strategy.name}"
                            )
                            strategy.set_initial_history(cached_data)
                        else:
                            logger.warning(
                                f"TASK-06: Nenhum dado de warmup encontrado no cache para {spec}"
                            )
                    # --- Fim da Injeção TASK-06 ---

                    key = f"{symbol}_{timeframe}"
                    self.strategies[key] = strategy
                    strategies_created += 1

                    logger.debug(
                        f"✅ TASK-03: Estratégia {strategy_name} criada para {key}"
                    )

                except Exception as e:
                    strategies_failed += 1
                    logger.error(
                        f"❌ TASK-03: Erro criando estratégia para {symbol}@{timeframe}: {e}",
                        exc_info=True,
                    )

        # YAA TASK-03: Log de resumo da criação
        logger.info(
            f"📊 TASK-03: Resumo da criação de estratégias:\n"
            f"   ✅ Criadas: {strategies_created}\n"
            f"   ❌ Falharam: {strategies_failed}\n"
            f"   📈 Taxa de sucesso: {strategies_created / (strategies_created + strategies_failed) * 100:.1f}%"
        )

        # YAA TASK-03: Injetar dados pré-carregados se o warmup foi bem-sucedido
        if self.warmup_manager and self.strategies:
            try:
                logger.info(
                    "💉 TASK-03: Injetando dados pré-carregados nas estratégias..."
                )
                self.warmup_manager.inject_data_into_strategies(self.strategies)
                logger.info(
                    "✅ TASK-03: Dados pré-carregados injetados com sucesso nas estratégias finais"
                )
            except Exception as injection_error:
                logger.error(
                    f"❌ TASK-03: Erro na injeção de dados: {injection_error}",
                    exc_info=True,
                )
                # YAA TASK-03: Não falhar se a injeção falhar - estratégias podem funcionar sem warmup
                logger.warning(
                    "⚠️ TASK-03: Estratégias operarão sem dados pré-carregados"
                )
        else:
            if not self.warmup_manager:
                logger.warning(
                    "⚠️ TASK-03: Nenhum warmup manager disponível - estratégias sem dados pré-carregados"
                )
            if not self.strategies:
                logger.warning("⚠️ TASK-03: Nenhuma estratégia foi criada com sucesso")

        # YAA TASK-03: Validação final
        if strategies_created == 0:
            raise RuntimeError(
                "TASK-03: Falha crítica - nenhuma estratégia foi criada com sucesso"
            )
        elif strategies_failed > 0:
            logger.warning(
                f"⚠️ TASK-03: Sistema continuará com {strategies_created} estratégias "
                f"({strategies_failed} falharam)"
            )

    def _initialize_risk_managers(self):
        """Inicializa risk managers centralizados."""

        risk_profile = self.config.get("risk_profile", "moderate")
        profile_settings = self.config.get("risk_profile_settings", {})

        for symbol in self.symbols:
            try:
                # Configuração específica do símbolo
                symbol_config = profile_settings.get(risk_profile, {}).get(symbol, {})

                risk_manager = create_risk_manager(
                    initial_capital=self.capital,
                    risk_profile=risk_profile,
                    profile_specific_config=symbol_config,
                )

                self.risk_managers[symbol] = risk_manager
                logger.debug(f"Risk manager criado para {symbol}")

            except Exception as e:
                logger.error(f"Erro criando risk manager para {symbol}: {e}")

    async def generate_decisions(
        self,
        enhanced_market_data: List[EnhancedMarketData],
        symbols: Optional[List[str]] = None,
        internal_state: Optional[Dict[str, Any]] = None,
    ) -> List[OracleDecision]:
        """
        Consulta o oráculo QAST para decisões de trading, recebendo os dados como argumento.
        """
        symbols_to_analyze = symbols or self.symbols

        try:
            # 1. USA OS DADOS RECEBIDOS - não coleta mais
            logger.debug(
                f"🔍 Usando {len(enhanced_market_data)} pontos de dados recebidos..."
            )
            market_data = self._organize_market_data(enhanced_market_data)

            # 2. Processa estado quântico no QASTCore
            logger.debug("🌀 Processando estado quântico...")
            await self.qast_core._process_market_cycle()
            quantum_state = self.qast_core.current_qualia_state

            # 3. Atualiza universo holográfico (já alimentado pelo loop principal)
            holographic_patterns = (
                self.holographic_universe.analyze_holographic_patterns()
            )

            # 4. Detecta padrões temporais
            logger.debug("⏰ Detectando padrões temporais...")
            temporal_patterns = await self._detect_temporal_patterns(market_data)

            # 5. Calcula métricas de risco
            logger.debug("⚖️ Calculando métricas de risco...")
            risk_metrics = await self._calculate_risk_metrics(market_data)

            # 6. Atualiza nível de consciência
            self.consciousness_level = self._calculate_consciousness_level(
                quantum_state, holographic_patterns, risk_metrics
            )

            # 7. Cria estado unificado
            unified_state = UnifiedMarketState(
                market_data=market_data,
                quantum_state=quantum_state,
                holographic_patterns=holographic_patterns,
                temporal_patterns=temporal_patterns,
                risk_metrics=risk_metrics,
                consciousness_level=self.consciousness_level,
                timestamp=time.time(),
            )

            self.last_unified_state = unified_state

            # Estado interno proveniente do SimulationQASTCore
            if internal_state is None:
                internal_state = (
                    self.simulation_core.run_internal_reality_simulation(market_data)
                    if self.simulation_core
                    else {"intention_vector": [0.33, 0.33, 0.34]}
                )

            # 8. Gera decisões para cada símbolo
            decisions = []

            # YAA: Bootstrap - Se não há histórico e bootstrap está habilitado
            if self._bootstrap_decisions_if_needed and len(self.decision_history) < 5:
                logger.info(
                    f"🔄 Bootstrap: Gerando decisões iniciais ({len(self.decision_history)}/5)"
                )
                for symbol in symbols_to_analyze:
                    bootstrap_decision = self._create_bootstrap_decision(symbol)
                    self.decision_history.append(bootstrap_decision)
                    decisions.append(bootstrap_decision)

                # Após 5 iterações, desabilita bootstrap
                if len(self.decision_history) >= 5:
                    self._bootstrap_decisions_if_needed = False
                    logger.info(
                        "✅ Bootstrap concluído - sistema pronto para operação normal"
                    )
            else:
                # Operação normal
                for symbol in symbols_to_analyze:
                    decision = await self._generate_symbol_decision(
                        symbol, unified_state, internal_state
                    )
                    if decision:
                        decisions.append(decision)

            logger.info(
                f"🧠 Oráculo gerou {len(decisions)} decisões (consciência: {self.consciousness_level:.2f})"
            )
            return decisions

        except Exception as e:
            logger.error(f"❌ Erro consultando oráculo: {e}", exc_info=True)
            return []

    def _organize_market_data(
        self, enhanced_data: List[Any]
    ) -> Dict[str, pd.DataFrame]:
        """Organiza dados enhanced em estrutura por símbolo."""

        market_data = {}

        for data in enhanced_data:
            symbol = data.symbol

            # Cria DataFrame com dados OHLCV + indicadores
            df_data = {
                "timestamp": [data.timestamp],
                "open": [data.open],
                "high": [data.high],
                "low": [data.low],
                "close": [data.close],
                "volume": [data.volume],
                "rsi": [data.rsi] if data.rsi else [50.0],
                "volume_ratio": [data.volume_ratio] if data.volume_ratio else [1.0],
                "volatility": [data.volatility] if data.volatility else [0.01],
                "price_change_pct": (
                    [data.price_change_pct] if data.price_change_pct else [0.0]
                ),
            }

            df = pd.DataFrame(df_data)
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
            df.set_index("timestamp", inplace=True)

            if symbol not in market_data:
                market_data[symbol] = df
            else:
                # Concatena com dados existentes
                market_data[symbol] = pd.concat([market_data[symbol], df])
                # Mantém apenas últimas 100 linhas
                market_data[symbol] = market_data[symbol].tail(100)

        return market_data

    async def _detect_temporal_patterns(
        self, market_data: Dict[str, pd.DataFrame]
    ) -> List[Dict[str, Any]]:
        """Detecta padrões temporais nos dados de mercado."""

        patterns = []

        for symbol, df in market_data.items():
            if len(df) < 10:
                continue

            try:
                # Análise de tendência
                price_change = (df["close"].iloc[-1] - df["close"].iloc[0]) / df[
                    "close"
                ].iloc[0]
                trend_strength = abs(price_change)
                trend_direction = "up" if price_change > 0 else "down"

                # Análise de volatilidade
                volatility = df["close"].pct_change().std()

                # Análise de volume
                volume_trend = (
                    df["volume"].iloc[-5:].mean() / df["volume"].iloc[:-5].mean()
                ) - 1

                pattern = {
                    "symbol": symbol,
                    "type": "temporal_analysis",
                    "trend_direction": trend_direction,
                    "trend_strength": trend_strength,
                    "volatility": volatility,
                    "volume_trend": volume_trend,
                    "confidence": min(trend_strength * 2, 1.0),
                    "timestamp": time.time(),
                }

                patterns.append(pattern)

            except Exception as e:
                logger.debug(f"Erro detectando padrões temporais para {symbol}: {e}")

        return patterns

    async def _calculate_risk_metrics(
        self, market_data: Dict[str, pd.DataFrame]
    ) -> Dict[str, float]:
        """Calcula métricas de risco do portfólio."""

        try:
            total_volatility = 0.0
            total_symbols = 0

            for symbol, df in market_data.items():
                if len(df) > 5:
                    vol = df["close"].pct_change().std()
                    total_volatility += vol
                    total_symbols += 1

            avg_volatility = total_volatility / max(total_symbols, 1)

            # Calcula risco geral do mercado
            market_risk = min(avg_volatility * 100, 1.0)

            # Calcula nível de correlação ou variância
            returns: Dict[str, pd.Series] = {}
            for symbol, df in market_data.items():
                if len(df) > 5:
                    returns[symbol] = df["close"].pct_change().dropna()

            if len(returns) >= 2:
                returns_df = pd.concat(returns, axis=1)
                corr_matrix = returns_df.corr()
                mask = np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
                avg_corr = corr_matrix.where(mask).abs().mean().mean()
                if np.isnan(avg_corr):
                    avg_corr = 0.0
                correlation_risk = min(avg_corr, 1.0)
            elif returns:
                avg_var = np.mean([r.var() for r in returns.values()])
                correlation_risk = min(avg_var * 100, 1.0)
            else:
                correlation_risk = 0.0

            return {
                "market_volatility": avg_volatility,
                "market_risk": market_risk,
                "correlation_risk": correlation_risk,
                "overall_risk": (market_risk + correlation_risk) / 2,
            }

        except Exception as e:
            logger.error(f"Erro calculando métricas de risco: {e}")
            return {
                "market_volatility": 0.02,
                "market_risk": 0.5,
                "correlation_risk": 0.5,
                "overall_risk": 0.5,
            }

    def _calculate_consciousness_level(
        self,
        quantum_state: QualiaState,
        holographic_patterns: List[Dict[str, Any]],
        risk_metrics: Dict[str, float],
    ) -> float:
        """Calcula o nível de consciência do sistema.

        A coerência quântica, o emaranhamento e a força dos padrões
        holográficos são combinados de forma igualitária. Valores negativos
        são descartados para evitar distorções. A média resultante é
        padronizada de acordo com ``0.7 + avg * 0.3`` garantindo valores
        típicos superiores ou iguais a ``0.7``. O resultado final é
        ponderado pela clareza do risco.
        """

        try:
            # Componentes da consciência
            quantum_coherence = quantum_state.coherence_level if quantum_state else 0.5
            quantum_entanglement = (
                quantum_state.quantum_entanglement if quantum_state else 0.0
            )
            quantum_entanglement = max(quantum_entanglement, 0.0)

            # Força dos padrões holográficos
            pattern_strength = 0.0
            if holographic_patterns:
                pattern_strength = sum(
                    p["strength"] for p in holographic_patterns
                ) / len(holographic_patterns)
                pattern_strength = min(pattern_strength / 5.0, 1.0)
            pattern_strength = max(pattern_strength, 0.0)

            # Clareza do risco
            risk_clarity = 1.0 - risk_metrics.get("overall_risk", 0.5)

            # Combina componentes de consciência de forma igualitária
            avg_conscious = np.mean(
                [quantum_coherence, quantum_entanglement, pattern_strength]
            )
            normalized_avg = 0.7 + (avg_conscious * 0.3)

            consciousness = normalized_avg * 0.9 + risk_clarity * 0.1

            return np.clip(consciousness, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erro calculando consciência: {e}")
            return 0.5

    async def _generate_symbol_decision(
        self,
        symbol: str,
        unified_state: UnifiedMarketState,
        internal_state: Dict[str, Any],
    ) -> Optional[OracleDecision]:
        """Gera decisão para um símbolo, com override para teste de força bruta."""

        forced = self._force_trade_if_needed(symbol, unified_state)
        if forced is not None:
            return forced

        try:
            # Obtém dados do símbolo
            symbol_data = unified_state.market_data.get(symbol)
            if symbol_data is None or len(symbol_data) == 0:
                return None

            current_price = symbol_data["close"].iloc[-1]

            # 1. Análise estratégica
            strategy_decision = await self._get_strategy_decision(
                symbol, symbol_data, unified_state
            )

            # 2. Análise holográfica
            holographic_signal = self._get_holographic_signal(
                symbol, unified_state.holographic_patterns
            )

            # 3. Análise metacognitiva
            metacognitive_context = await self._get_metacognitive_context(
                symbol, strategy_decision, unified_state
            )

            # 4. Análise de risco
            risk_assessment = self._get_risk_assessment(
                symbol, strategy_decision, unified_state
            )

            # 5. Decisão unificada
            final_decision = await self._unify_decision(
                symbol=symbol,
                current_price=current_price,
                strategy_decision=strategy_decision,
                holographic_signal=holographic_signal,
                metacognitive_context=metacognitive_context,
                risk_assessment=risk_assessment,
                unified_state=unified_state,
                internal_state=internal_state,
            )

            await self._record_decision_memory(final_decision, symbol, unified_state)

            self._maybe_update_decision_weights()

            return final_decision

        except Exception as e:
            logger.error(f"Erro gerando decisão para {symbol}: {e}")
            return None

    async def _get_strategy_decision(
        self, symbol: str, symbol_data: pd.DataFrame, unified_state: UnifiedMarketState
    ) -> Dict[str, Any]:
        """Obtém decisão das estratégias centralizadas."""

        # YAA FIX-DATA-PIPELINE: Busca estratégia para o símbolo (usa primeiro timeframe disponível)
        strategy_key = f"{symbol}_{self.timeframes[0]}"
        strategy = self.strategies.get(strategy_key)

        # YAA FIX-DATA-PIPELINE: Debug detalhado para diagnosticar problema de lookup
        logger.debug(
            f"🔍 FIX-DATA-PIPELINE: Buscando estratégia para {symbol}"
            f"\n  🔑 Strategy key: {strategy_key}"
            f"\n  📋 Strategies disponíveis: {list(self.strategies.keys())}"
            f"\n  ✅ Strategy encontrada: {'Sim' if strategy else 'Não'}"
        )

        if not strategy:
            # YAA FIX-DATA-PIPELINE: Tentar lookup alternativo com diferentes formatos
            alternative_keys = [
                f"{symbol.replace('/', '')}_{self.timeframes[0]}",  # BTC/USDT -> BTCUSDT_5m
                f"{symbol}@{self.timeframes[0]}",  # BTC/USDT@5m
                symbol,  # Apenas o símbolo
            ]

            for alt_key in alternative_keys:
                if alt_key in self.strategies:
                    strategy = self.strategies[alt_key]
                    logger.info(
                        f"✅ FIX-DATA-PIPELINE: Estratégia encontrada com chave alternativa: {alt_key}"
                    )
                    break

            if not strategy:
                logger.warning(
                    f"❌ FIX-DATA-PIPELINE: Nenhuma estratégia encontrada para {symbol}"
                    f"\n  📋 Estratégias disponíveis: {list(self.strategies.keys())}"
                    f"\n  🔍 Chaves tentadas: {[strategy_key] + alternative_keys}"
                )
                return {
                    "signal": "HOLD",
                    "confidence": 0.0,
                    "stop_loss": None,
                    "take_profit": None,
                    "reasons": [
                        f"No strategy available for {symbol} (tried keys: {[strategy_key] + alternative_keys})"
                    ],
                }

        try:
            # Cria contexto de trading
            current_price = symbol_data["close"].iloc[-1]

            trade_context = TradeContext(
                symbol=symbol,
                timeframe=self.timeframes[0],
                ohlcv=symbol_data,
                current_price=current_price,
                timestamp=datetime.now(timezone.utc),
                wallet_state={"available_cash": self.capital},
                liquidity=0.8,
                volatility=(
                    symbol_data["volatility"].iloc[-1]
                    if "volatility" in symbol_data
                    else 0.01
                ),
                strategy_metrics={},
                quantum_metrics=(
                    unified_state.quantum_state.__dict__
                    if unified_state.quantum_state
                    else {}
                ),
                market_state="uncertain",
                risk_mode="normal",  # Adicionado parâmetro obrigatório
            )

            # Chama estratégia
            similar_patterns = []
            if self.memory_service and unified_state.quantum_state:
                try:
                    query_packet = QuantumSignaturePacket(
                        vector=list(
                            unified_state.quantum_state.folding_signature.astype(float)
                        ),
                        metrics=unified_state.quantum_state.__dict__,
                    )
                    similar_patterns = await self.memory_service.query_similarity(
                        query_packet,
                        top_n=5,
                    )
                except Exception as mem_exc:
                    logger.debug(f"Erro consultando memória: {mem_exc}")

            decision_dict = strategy.analyze_market(
                market_data=symbol_data,
                quantum_metrics=trade_context.quantum_metrics,
                trading_context=trade_context,
                similar_past_patterns=similar_patterns,
            )

            return decision_dict

        except Exception as e:
            logger.error(f"Erro na estratégia para {symbol}: {e}")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
                "reasons": [f"Strategy error: {str(e)}"],
            }

    def _get_holographic_signal(
        self, symbol: str, holographic_patterns: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extrai sinal do universo holográfico."""

        # Encontra padrões próximos ao símbolo
        symbol_key = symbol.replace("USDT", "").replace("/", "")
        symbol_position = self.holographic_universe.symbol_positions.get(
            symbol_key, (100, 100)
        )

        relevant_patterns = []
        for pattern in holographic_patterns:
            # Calcula distância do padrão ao símbolo
            distance = (
                (pattern["position"][0] - symbol_position[0]) ** 2
                + (pattern["position"][1] - symbol_position[1]) ** 2
            ) ** 0.5

            if distance < 50:  # Padrões próximos
                relevant_patterns.append(pattern)

        if not relevant_patterns:
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "strength": 0.0,
                "pattern_type": "none",
            }

        # Combina padrões relevantes
        total_strength = sum(p["strength"] for p in relevant_patterns)
        avg_confidence = sum(p["confidence"] for p in relevant_patterns) / len(
            relevant_patterns
        )

        # Determina sinal baseado nos padrões
        bullish_patterns = [
            p
            for p in relevant_patterns
            if p["pattern_type"] in ["bullish", "ascending"]
        ]
        bearish_patterns = [
            p
            for p in relevant_patterns
            if p["pattern_type"] in ["bearish", "descending"]
        ]

        if len(bullish_patterns) > len(bearish_patterns) and total_strength > 2.0:
            signal = "BUY"
        elif len(bearish_patterns) > len(bullish_patterns) and total_strength > 2.0:
            signal = "SELL"
        else:
            signal = "HOLD"

        # --- YAA: Correção de Lógica de Confiança (TASK-004) ---
        # A `total_strength` pode ser muito grande e a normalização por 5 é arbitrária.
        # A `avg_confidence` já representa a confiança média dos padrões (entre 0 e 1)
        # e é um indicador mais direto e estável da confiança do sinal holográfico.
        # O `strength` será a própria `avg_confidence` para unificação, enquanto a
        # `total_strength` bruta será mantida nos metadados para observação.
        final_confidence = min(avg_confidence, 1.0)

        return {
            "signal": signal,
            "confidence": final_confidence,
            "strength": final_confidence,  # Usar a confiança normalizada para o score
            "pattern_type": f"{len(relevant_patterns)}_patterns",
            "metadata": {
                "raw_total_strength": total_strength,
                "avg_confidence": avg_confidence,
                "relevant_patterns_count": len(relevant_patterns),
            },
        }

    def _generate_fallback_quantum_vector(
        self, unified_state: UnifiedMarketState, symbol: str
    ) -> List[float]:
        """Gera vetor quântico de fallback baseado no estado unificado.

        YAA IMPLEMENTATION - TASK-004: Correção do QuantumSignaturePacket

        Quando as estratégias não conseguem gerar um QuantumSignaturePacket válido,
        este método cria um vetor quântico baseado nos dados de mercado disponíveis
        para habilitar o funcionamento da metacognição e QPM.

        Args:
            unified_state: Estado unificado do mercado com dados disponíveis
            symbol: Símbolo do ativo para extração de dados específicos

        Returns:
            Lista com vetor quântico de 4 dimensões normalizado [0.0-1.0]
        """
        try:
            market_data = unified_state.market_data.get(symbol)
            if market_data is not None and not market_data.empty:
                # Usar dados de mercado para gerar vetor
                latest_data = market_data.iloc[-1]

                # Extrair métricas normalizadas
                rsi = latest_data.get("rsi", 50.0)
                volume_ratio = latest_data.get("volume_ratio", 1.0)
                volatility = latest_data.get("volatility", 0.01)
                price_change = latest_data.get("price_change_pct", 0.0)

                # Normalizar para [0.0-1.0]
                rsi_norm = max(0.0, min(1.0, rsi / 100.0))  # RSI já está em 0-100
                volume_norm = max(
                    0.0, min(1.0, volume_ratio / 3.0)
                )  # Volume ratio até 3x
                volatility_norm = max(
                    0.0, min(1.0, volatility / 10.0)
                )  # Volatilidade até 10%
                price_change_norm = max(
                    0.0, min(1.0, (price_change + 10.0) / 20.0)
                )  # Price change -10% a +10%

                vector = [rsi_norm, volume_norm, volatility_norm, price_change_norm]

                logger.debug(
                    f"🔮 Fallback quantum vector para {symbol}: RSI={rsi_norm:.3f}, Vol={volume_norm:.3f}, Volatility={volatility_norm:.3f}, PriceChange={price_change_norm:.3f}"
                )
                return vector

            # Se não há dados de mercado, usar estado quântico do sistema
            elif unified_state.quantum_state:
                # Extrair dados do estado quântico
                quantum_entropy = getattr(unified_state.quantum_state, "entropy", 0.5)
                quantum_coherence = getattr(
                    unified_state.quantum_state, "coherence", 0.5
                )
                system_consciousness = unified_state.consciousness_level
                overall_risk = unified_state.risk_metrics.get("overall_risk", 0.5)

                # Normalizar valores quânticos
                entropy_norm = max(0.0, min(1.0, quantum_entropy))
                coherence_norm = max(0.0, min(1.0, quantum_coherence))
                consciousness_norm = max(0.0, min(1.0, system_consciousness))
                risk_norm = max(0.0, min(1.0, overall_risk))

                vector = [entropy_norm, coherence_norm, consciousness_norm, risk_norm]

                logger.debug(
                    f"🔮 Fallback quantum vector (estado quântico) para {symbol}: Entropy={entropy_norm:.3f}, Coherence={coherence_norm:.3f}, Consciousness={consciousness_norm:.3f}, Risk={risk_norm:.3f}"
                )
                return vector

        except Exception as e:
            logger.warning(f"Erro gerando fallback quantum vector para {symbol}: {e}")

        # Fallback final: vetor neutro
        logger.debug(f"🔮 Fallback quantum vector (neutro) para {symbol}")
        return [0.5, 0.5, 0.5, 0.5]  # Fallback neutro

    async def _get_metacognitive_context(
        self,
        symbol: str,
        strategy_decision: Dict[str, Any],
        unified_state: UnifiedMarketState,
    ) -> Dict[str, Any]:
        """Obtém contexto metacognitivo.

        YAA ENHANCEMENT - TASK-004: Correção do QuantumSignaturePacket

        Agora gera QuantumSignaturePacket válido sempre, usando fallback
        baseado em dados de mercado quando estratégias não fornecem vetor quântico.
        """

        try:
            # --- YAA TASK-004: Correção do QuantumSignaturePacket ---
            # Verificar se há quantum signature válido da estratégia
            strategy_quantum_signature = strategy_decision.get("quantum_signature")

            # Se não há signature válido ou vetor está vazio, gerar fallback
            if (
                not strategy_quantum_signature
                or not hasattr(strategy_quantum_signature, "vector")
                or not strategy_quantum_signature.vector
                or len(strategy_quantum_signature.vector) == 0
            ):
                logger.debug(
                    f"🔮 Gerando QuantumSignaturePacket fallback para {symbol}"
                )

                # Gerar vetor quântico de fallback
                fallback_vector = self._generate_fallback_quantum_vector(
                    unified_state, symbol
                )

                # Criar métricas baseadas no estado unificado
                fallback_metrics = {
                    "source": "oracle_fallback",
                    "symbol": symbol,
                    "consciousness_level": unified_state.consciousness_level,
                    "market_data_available": symbol in unified_state.market_data,
                    "timestamp": unified_state.timestamp,
                }

                # Adicionar métricas do estado quântico se disponível
                if unified_state.quantum_state:
                    fallback_metrics.update(unified_state.quantum_state.__dict__)

                # Criar QuantumSignaturePacket válido
                quantum_packet = QuantumSignaturePacket(
                    vector=fallback_vector,
                    metrics=fallback_metrics,
                    source_details={
                        "method": "oracle_fallback_generation",
                        "vector_type": "market_data_derived",
                        "n_qubits": 4,  # Vetor 4D
                        "fallback_reason": "strategy_quantum_signature_missing_or_empty",
                    },
                )

                logger.info(
                    f"✅ QuantumSignaturePacket fallback criado para {symbol} com vetor 4D"
                )

            else:
                # Usar quantum signature da estratégia se válido
                quantum_packet = strategy_quantum_signature
                logger.debug(
                    f"🔮 Usando QuantumSignaturePacket da estratégia para {symbol}"
                )

            # Criar contexto de decisão com QuantumSignaturePacket garantidamente válido
            decision_context = DecisionContext(
                quantum_signature_packet=quantum_packet,
                market_regime="uncertain",
                trade_details={
                    "symbol": symbol,
                    "action": strategy_decision.get("signal", "HOLD"),
                    "confidence": strategy_decision.get("confidence", 0.0),
                    "stop_loss": strategy_decision.get("stop_loss"),
                    "take_profit": strategy_decision.get("take_profit"),
                    "reasons": strategy_decision.get("reasons", []),
                },
                quantum_metrics_at_decision=(
                    unified_state.quantum_state.__dict__
                    if unified_state.quantum_state
                    else {}
                ),
                timestamp=datetime.now(timezone.utc),
            )

            # Verificar se metacognição está disponível
            if not self.metacognition:
                logger.debug(f"Metacognição não disponível para {symbol}")
                return {
                    "signal_type": "NO_SIGNAL",
                    "confidence": 0.0,
                    "reasoning": "Metacognition service unavailable",
                    "context_directive": None,
                }

            # Executa metacognição com QuantumSignaturePacket válido
            metacognition_result = self.metacognition.run_with_qast_feedback(
                decision_context=decision_context, pnl_feedback=None
            )

            if metacognition_result and metacognition_result.trade_signal:
                logger.debug(f"✅ Metacognição executada com sucesso para {symbol}")
                return {
                    "signal_type": metacognition_result.trade_signal.signal_type,
                    "confidence": metacognition_result.trade_signal.confidence,
                    "reasoning": metacognition_result.trade_signal.reasoning,
                    "context_directive": (
                        metacognition_result.context.trade_directive
                        if metacognition_result.context
                        else None
                    ),
                }
            else:
                logger.debug(
                    f"Metacognição não retornou resultado válido para {symbol}"
                )
                return {
                    "signal_type": "NO_SIGNAL",
                    "confidence": 0.0,
                    "reasoning": "Metacognition did not return valid result",
                    "context_directive": None,
                }

        except Exception as e:
            logger.warning(f"Erro na metacognição para {symbol}: {e}")
            return {
                "signal_type": "NO_SIGNAL",
                "confidence": 0.0,
                "reasoning": f"Metacognition error: {str(e)}",
                "context_directive": None,
            }

    def _get_risk_assessment(
        self,
        symbol: str,
        strategy_decision: Dict[str, Any],
        unified_state: UnifiedMarketState,
    ) -> Dict[str, Any]:
        """Avalia risco da decisão."""

        risk_manager = self.risk_managers.get(symbol)
        if not risk_manager:
            return {
                "approved": False,
                "reason": "No risk manager",
                "position_size": 0.0,
            }

        try:
            # Verifica se pode abrir posição
            can_open, reason = risk_manager.can_open_new_position(
                0
            )  # Assume 0 posições abertas

            if not can_open:
                return {"approved": False, "reason": reason, "position_size": 0.0}

            # Calcula tamanho da posição
            symbol_data = unified_state.market_data.get(symbol)
            current_price = (
                symbol_data["close"].iloc[-1] if symbol_data is not None else 1.0
            )

            size_details = risk_manager.calculate_position_size(
                current_price=current_price,
                stop_loss_price=strategy_decision.get("stop_loss"),
                confidence=strategy_decision.get("confidence", 0.0),
                symbol=symbol,
            )

            return {
                "approved": size_details.get("quantity", 0.0) > 0,
                "reason": size_details.get("reason", "approved"),
                "position_size": size_details.get("quantity", 0.0),
                "risk_score": unified_state.risk_metrics.get("overall_risk", 0.5),
            }

        except Exception as e:
            logger.error(f"Erro na avaliação de risco para {symbol}: {e}")
            return {
                "approved": False,
                "reason": f"Risk assessment error: {str(e)}",
                "position_size": 0.0,
            }

    def _calculate_dynamic_weights(
        self, strategy_conf: float, holographic_conf: float, meta_conf: float
    ) -> Dict[str, float]:
        """Calcula pesos dinâmicos baseado na qualidade dos sinais.

        YAA IMPLEMENTATION - TASK-002: Correção da Unificação de Sinais

        Quando a estratégia produz confiança zero mas há sinais holográficos fortes,
        redistribui os pesos para dar maior importância aos sinais holográficos.

        Args:
            strategy_conf: Confiança da estratégia (0.0-1.0)
            holographic_conf: Confiança do sinal holográfico (0.0-1.0)
            meta_conf: Confiança da metacognição (0.0-1.0)

        Returns:
            Dicionário com pesos dinâmicos para cada componente
        """

        # Cenário 1: Strategy confidence zero mas holographic forte
        if strategy_conf == 0.0 and holographic_conf > 0.5:
            logger.info(
                f"🎯 PESOS DINÂMICOS: Strategy=0.0, Holographic={holographic_conf:.2f} - Aumentando peso holográfico"
            )
            return {
                "strategy": 0.1,
                "holographic": 0.7,  # Aumentar peso holográfico
                "metacognition": 0.2,
            }

        # Cenário 2: Strategy confidence muito baixa mas holographic moderada
        elif strategy_conf < 0.2 and holographic_conf > 0.3:
            logger.info(
                f"🎯 PESOS DINÂMICOS: Strategy={strategy_conf:.2f}, Holographic={holographic_conf:.2f} - Rebalanceando para holographic"
            )
            return {
                "strategy": 0.2,
                "holographic": 0.6,  # Peso holográfico aumentado
                "metacognition": 0.2,
            }

        # Cenário 3: Metacognição forte compensa strategy fraca
        elif strategy_conf < 0.3 and meta_conf > 0.6:
            logger.info(
                f"🎯 PESOS DINÂMICOS: Strategy={strategy_conf:.2f}, Meta={meta_conf:.2f} - Reforçando metacognição"
            )
            return {
                "strategy": 0.2,
                "holographic": 0.3,
                "metacognition": 0.5,  # Peso metacognitivo aumentado
            }

        # Cenário 4: Todos os sinais são fracos - distribuição conservadora
        elif max(strategy_conf, holographic_conf, meta_conf) < 0.3:
            logger.debug("🎯 PESOS DINÂMICOS: Todos sinais fracos - modo conservador")
            return {"strategy": 0.4, "holographic": 0.4, "metacognition": 0.2}

        # Cenário padrão: usar pesos configurados
        logger.debug(
            f"🎯 PESOS DINÂMICOS: Usando pesos padrão - Strategy={strategy_conf:.2f}, Holo={holographic_conf:.2f}, Meta={meta_conf:.2f}"
        )
        return self.decision_weights  # Usar pesos padrão

    def _get_adjusted_threshold(
        self, market_volatility: float, history: Optional[List[float]] | None = None
    ) -> float:
        """Retorna limiar ajustado conforme a volatilidade do mercado.

        O threshold torna-se mais conservador quando a volatilidade aumenta e
        mais permissivo quando diminui.

        Parameters
        ----------
        market_volatility
            Volatilidade média do mercado como fração (e.g., ``0.02`` para 2%).

        Returns
        -------
        float
            Valor do limiar de decisão ajustado.
        """

        base = self.decision_threshold
        history_data = list(history or self.volatility_history[-50:])

        if len(history_data) >= 10:
            low_pct, high_pct = np.percentile(history_data, [25, 75])

            if market_volatility >= high_pct:
                return float(min(base * 1.5, 0.9))
            if market_volatility <= low_pct:
                return float(max(base * 0.8, 0.05))

            scale = (market_volatility - low_pct) / max(high_pct - low_pct, 1e-8)
            adjusted = base * (1.0 + 0.5 * scale)
            return float(max(0.05, min(adjusted, 0.9)))

        avg_volatility = (
            float(np.mean(history_data)) if history_data else market_volatility
        )

        if avg_volatility >= 0.05:
            return float(min(base * 1.5, 0.9))
        if avg_volatility <= 0.02:
            return float(max(base * 0.8, 0.05))

        scale = (avg_volatility - 0.02) / 0.03
        adjusted = base * (1.0 + 0.5 * scale)
        return float(max(0.05, min(adjusted, 0.9)))

    def _derive_external_vector(
        self,
        strategy_decision: Dict[str, Any],
        state: QualiaState | None,
    ) -> np.ndarray:
        """Return normalized external intention vector."""

        signal = str(strategy_decision.get("signal", "HOLD")).upper()
        base = {
            "BUY": np.array([1.0, 0.0, 0.0], dtype=float),
            "SELL": np.array([0.0, 1.0, 0.0], dtype=float),
            "HOLD": np.array([0.0, 0.0, 1.0], dtype=float),
        }.get(signal, np.array([0.0, 0.0, 1.0], dtype=float))

        confidence = float(strategy_decision.get("confidence", 0.0))
        coherence = float(getattr(state, "coherence_level", 0.0) or 0.0)

        vector = (base * confidence) + (coherence / 3.0)
        total = float(np.sum(vector))
        if total > 0.0:
            vector /= total
        else:
            vector = np.array([1.0 / 3.0] * 3, dtype=float)

        return vector

    def _derive_internal_vector(self, internal_state: Dict[str, Any]) -> np.ndarray:
        """Return normalized internal intention vector.

        Parameters
        ----------
        internal_state : Dict[str, Any]
            Estado interno da simulação social contendo a chave
            ``"intention_vector"`` com as probabilidades brutas.

        Returns
        -------
        numpy.ndarray
            Vetor ``[BUY, SELL, HOLD]`` normalizado para somar ``1.0``.
        """

        vec = np.asarray(
            internal_state.get("intention_vector", [0.33, 0.33, 0.34]), dtype=float
        )
        if vec.size != 3:
            vec = np.array([0.33, 0.33, 0.34], dtype=float)

        total = float(np.sum(vec))
        if total > 0.0:
            vec /= total
        else:
            vec = np.array([1.0 / 3.0] * 3, dtype=float)

        return vec

    def _record_collapsed_state(self, decision: OracleDecision) -> None:
        """
        Registra a decisão colapsada na memória e propaga o evento.

        Esta função garante que a auto-observação do sistema influencie
        suas futuras evoluções, conforme a Fase 5 do roadmap.
        """
        # 1. Adiciona ao histórico de decisões do oráculo
        self.decision_history.append(decision)
        if len(self.decision_history) > 1000:
            self.decision_history.pop(0)

        # 2. Armazena a intenção na memória de longo prazo (IntentMemory)
        if self.memory_service and self.memory_service.intent_memory is not None:
            self.memory_service.store_intent_sequence(
                [
                    decision.symbol,
                    (
                        decision.action.lower()
                        if hasattr(decision.action, "lower")
                        else str(decision.action).lower()
                    ),
                ]
            )

        # 3. Propaga o evento da decisão final para o resto do sistema
        if self.memory_service and self.memory_service.event_bus:
            event = OracleDecisionEvent(
                symbol=decision.symbol,
                timeframe=self.timeframes[0],
                decision=decision.action,
                confidence=decision.confidence,
                details=decision.metadata.get("coherence_collapse_details", {}),
            )
            self.memory_service.event_bus.publish(event)

        # 4. Atualiza o histórico de volatilidade
        if self.last_unified_state:
            self.volatility_history.append(
                self.last_unified_state.risk_metrics.get("market_volatility", 0.0)
            )
            if len(self.volatility_history) > 1000:
                self.volatility_history.pop(0)

    async def _unify_decision(
        self,
        symbol: str,
        current_price: float,
        strategy_decision: Dict[str, Any],
        holographic_signal: Dict[str, Any],
        metacognitive_context: Dict[str, Any],
        risk_assessment: Dict[str, Any],
        unified_state: UnifiedMarketState,
        internal_state: Dict[str, Any],
    ) -> OracleDecision:
        """
        Implementa o 'Colapso por Coerência' entre as realidades externa e interna.

        Este método é o coração da Fase 5 do roadmap, onde a decisão de trading
        emerge da interferência entre a percepção do mercado (externo) e a
        simulação social (interno).
        """

        # 1. Derivar Vetor de Intenção Externo (Mercado)
        external_vector = self._derive_external_vector(
            strategy_decision, unified_state.quantum_state
        )

        # 2. Derivar Vetor de Intenção Interno (Simulação Social)
        # O vetor é derivado do estado de equilíbrio do campo de influência social
        internal_vector = self._derive_internal_vector(internal_state)

        # 3. Calcular a Coerência e Colapsar a Decisão
        collapse_result = collapse_by_coherence(external_vector, internal_vector)

        final_action = collapse_result["action"]
        final_confidence = collapse_result["confidence"]
        coherence = collapse_result["coherence"]
        is_paradox = collapse_result["paradox"]

        raw_score = final_confidence
        threshold = self._get_adjusted_threshold(
            unified_state.risk_metrics.get("market_volatility", 0.0)
        )

        reasoning = [
            f"Coerência(Ext, Int) = {coherence:.3f}",
            f"Vetor Externo: {np.round(external_vector, 2)}",
            f"Vetor Interno: {np.round(internal_vector, 2)}",
        ]

        features_on: List[str] = []
        if str(strategy_decision.get("signal", "HOLD")).upper() != "HOLD":
            features_on.append("strategy")
        if str(holographic_signal.get("signal", "HOLD")).upper() != "HOLD":
            features_on.append("holographic")
        meta_sig = (
            metacognitive_context.get("signal_type")
            or metacognitive_context.get("signal")
            or "NO_SIGNAL"
        )
        if str(meta_sig).upper() not in {"HOLD", "NO_SIGNAL"}:
            features_on.append("metacognition")

        # 4. Lidar com o Paradoxo (conflito de realidades)
        if is_paradox:
            final_action = "HOLD"  # Ação segura em caso de paradoxo
            final_confidence = 0.0
            reasoning.append("Paradoxo detectado: Conflito entre realidades.")
            logger.warning(
                f"🚨 PARADOXO DETECTADO para {symbol}. Coerência: {coherence:.3f}. "
                f"Acionando Metacognição para resolução."
            )
            # Dispara o evento para o MetacognitiveEngine
            if self.metacognition:
                paradox_payload = {
                    "symbol": symbol,
                    "external_vector": external_vector.tolist(),
                    "internal_vector": internal_vector.tolist(),
                    "coherence": coherence,
                    "unified_state": unified_state,  # Passa o estado completo
                }
                # Executa a resolução do paradoxo de forma assíncrona
                if asyncio.iscoroutinefunction(self.metacognition.resolve_paradox):
                    await self.metacognition.resolve_paradox(paradox_payload)
                else:
                    self.metacognition.resolve_paradox(paradox_payload)

        # 5. Aplicar avaliação de risco final
        if not risk_assessment.get("approved", False):
            if final_action != "HOLD":
                reasoning.append(f"Risco não aprovado: {risk_assessment.get('reason')}")
                logger.warning(f"Decisão para {symbol} revertida para HOLD por risco.")
            final_action = "HOLD"
            final_confidence = 0.0

        if raw_score < threshold and final_action != "HOLD":
            reasoning.append(f"Score {raw_score:.2f} abaixo do limiar {threshold:.2f}")
            final_action = "HOLD"
            final_confidence = 0.0

        # 6. Construir a OracleDecision final (o estado colapsado)
        decision = OracleDecision(
            symbol=symbol,
            action=final_action,
            confidence=final_confidence,
            size=(
                risk_assessment.get("position_size", 0.0)
                if final_action != "HOLD"
                else 0.0
            ),
            stop_loss=strategy_decision.get("stop_loss"),
            take_profit=strategy_decision.get("take_profit"),
            reasoning=reasoning,
            quantum_signature=(
                unified_state.quantum_state.get_signature()
                if getattr(unified_state.quantum_state, "get_signature", None)
                else None
            ),
            holographic_patterns=unified_state.holographic_patterns,
            metacognitive_context=metacognitive_context,
            risk_assessment=risk_assessment,
            risk_approved=risk_assessment.get("approved", False),
            timestamp=time.time(),
            source="coherence_collapse",
            metadata={
                "coherence_collapse_details": collapse_result,
                "external_vector": external_vector.tolist(),
                "internal_vector": internal_vector.tolist(),
            },
        )

        # 7. Registrar o estado colapsado na memória e no EventBus
        self._record_collapsed_state(decision)

        logger.info(
            f"🧠 Decisão Colapsada para {symbol}: {decision.action} "
            f"(Conf: {decision.confidence:.2f}, Coerência: {coherence:.2f})"
        )

        logger.debug(
            "decide | symbol=%s | features_on=%s | raw_score=%.3f | confidence=%.3f | threshold=%.3f | reason=%s",
            symbol,
            ",".join(features_on) or "none",
            raw_score,
            decision.confidence,
            threshold,
            "; ".join(reasoning),
        )

        if self.statsd:
            tags = [f"symbol:{symbol}"]
            self.statsd.gauge("oracle_decision.raw_score", raw_score, tags=tags)
            self.statsd.gauge(
                "oracle_decision.confidence", decision.confidence, tags=tags
            )
            self.statsd.gauge("oracle_decision.threshold", threshold, tags=tags)
            self.statsd.increment(
                "oracle_decision.action",
                tags=tags + [f"action:{decision.action.lower()}"],
            )
            for rsn in reasoning:
                slug = rsn.replace(" ", "_").replace("/", "_")[:40]
                self.statsd.increment(
                    "oracle_decision.reason", tags=tags + [f"reason:{slug}"]
                )

        return decision

    async def shutdown(self):
        """Encerra consciência unificada."""

        logger.info("🌀 Encerrando consciência QUALIA unificada...")

        try:
            # Encerra componentes
            if self.qast_core:
                await self.qast_core.shutdown()
                logger.info("✅ QASTCore encerrado")

            if self.data_collector:
                await self.data_collector.__aexit__(None, None, None)
                logger.info("✅ Enhanced Data Collector encerrado")

            if self.holographic_universe:
                await self.holographic_universe.shutdown()
                logger.info("✅ Holographic Universe encerrado")

            # Aguarda um pouco para garantir que todas as sessões sejam fechadas
            await asyncio.sleep(0.3)
            logger.info("✅ Aguardado tempo para fechamento de sessões")

            self.running = False
            logger.info("✅ Consciência QUALIA encerrada")

        except Exception as e:
            logger.error(f"❌ Erro encerrando consciência: {e}")

    def get_consciousness_status(self) -> Dict[str, Any]:
        """Retorna status da consciência unificada."""

        return {
            "consciousness_level": self.consciousness_level,
            "running": self.running,
            "symbols": len(self.symbols),
            "strategies": len(self.strategies),
            "risk_managers": len(self.risk_managers),
            "decision_history_length": len(self.decision_history),
            "last_unified_state_timestamp": (
                self.last_unified_state.timestamp if self.last_unified_state else 0
            ),
            "qast_core_status": self.qast_core.get_system_status(),
            "holographic_patterns": (
                len(self.last_unified_state.holographic_patterns)
                if self.last_unified_state
                else 0
            ),
        }

    def get_status(self) -> Dict[str, Any]:
        """Retorna o status atual do oráculo para monitoramento."""
        return {
            "consciousness_level": self.consciousness_level,
            "running": self.running,
            "total_strategies": len(self.strategies),
            "total_risk_managers": len(self.risk_managers),
            "decision_history_count": len(self.decision_history),
        }

    def _calculate_source_precisions(self) -> Dict[str, float]:
        """Calcula a precisão de cada fonte de decisão usando as últimas 100 entradas."""

        history = self.decision_history[-100:]
        totals = {"strategy": 0, "holographic": 0, "metacognition": 0}
        correct = {"strategy": 0, "holographic": 0, "metacognition": 0}

        for decision in history:
            outcome = decision.metadata.get("outcome")
            component_actions = decision.metadata.get("component_actions")
            if not outcome or not component_actions:
                continue

            for src in totals:
                action = component_actions.get(src)
                if action is None:
                    continue
                totals[src] += 1
                if action == outcome:
                    correct[src] += 1

        precisions = {}
        for src in totals:
            precisions[src] = correct[src] / totals[src] if totals[src] else 0.0
        return precisions

    def _log_weight_adjustment(
        self, precisions: Dict[str, float], new_weights: Dict[str, float]
    ) -> None:
        """Registra ajuste de pesos em arquivo de log."""

        entry = {
            "timestamp": time.time(),
            "total_decisions": len(self.decision_history),
            "precisions": precisions,
            "new_weights": new_weights,
        }

        try:
            with open(self.weight_adjustment_log_path, "a", encoding="utf-8") as fh:
                fh.write(json.dumps(entry) + "\n")
        except Exception as exc:  # pragma: no cover - logging falha não deve parar
            logger.debug(f"Falha ao registrar ajuste de pesos: {exc}")

        logger.info(
            "Ajuste de pesos realizado após %d decisões: %s",
            len(self.decision_history),
            new_weights,
        )

    def _maybe_update_decision_weights(self) -> None:
        """Atualiza os pesos de decisão utilizando suavização exponencial."""

        if len(self.decision_history) < 100 or len(self.decision_history) % 100 != 0:
            return

        precisions = self._calculate_source_precisions()
        total = sum(precisions.values())
        if total == 0:
            logger.info("Precisão insuficiente para ajuste de pesos")
            return

        normalized = {src: precisions[src] / total for src in precisions}
        alpha = self.config.get("weight_smoothing_alpha", 0.3)
        smoothed = {}
        for src in normalized:
            previous = self.decision_weights.get(src, 0.0)
            smoothed[src] = (1 - alpha) * previous + alpha * normalized[src]

        total_smoothed = sum(smoothed.values())
        new_weights = {src: smoothed[src] / total_smoothed for src in smoothed}
        self.decision_weights = new_weights
        self._log_weight_adjustment(precisions, new_weights)

    def _force_trade_if_needed(
        self, symbol: str, unified_state: UnifiedMarketState
    ) -> Optional[OracleDecision]:
        """Retorna decisão de compra forçada se configurado."""
        if self.force_trade_symbol and self.force_trade_symbol == symbol:
            logger.warning("🚨 FORÇANDO SINAL DE COMPRA PARA %s!", symbol)
            risk_assessment = self._get_risk_assessment(
                symbol, {"signal": "BUY", "confidence": 0.95}, unified_state
            )
            price = unified_state.market_data.get(symbol)["close"].iloc[-1]
            return OracleDecision(
                symbol=symbol,
                action="BUY",
                confidence=0.95,
                size=risk_assessment.get("position_size", 0.01),
                stop_loss=price * 0.98,
                take_profit=price * 1.05,
                reasoning=["!! SINAL DE TESTE FORÇADO !!"],
                source="ForceTradeTest",
                quantum_signature=None,
                holographic_patterns=[],
                metacognitive_context={},
                risk_assessment=risk_assessment,
                risk_approved=risk_assessment.get("approved", False),
                timestamp=time.time(),
            )
        return None

    async def _record_decision_memory(
        self, decision: OracleDecision, symbol: str, unified_state: UnifiedMarketState
    ) -> None:
        """Armazena assinatura quântica e contexto na memória."""
        if not self.memory_service or not unified_state.quantum_state:
            return
        try:
            packet = QuantumSignaturePacket(
                vector=list(
                    unified_state.quantum_state.folding_signature.astype(float)
                ),
                metrics=unified_state.quantum_state.__dict__,
            )
            decision.quantum_signature = packet
            market_snapshot: Dict[str, Any] = {}
            if symbol in unified_state.market_data:
                df = unified_state.market_data[symbol]
                if hasattr(df, "iloc") and not df.empty:
                    market_snapshot = df.iloc[-1].to_dict()
            await self.memory_service.store(
                packet,
                market_snapshot=market_snapshot,
                decision_context=decision.metadata,
            )
        except Exception as mem_exc:  # pragma: no cover - falha não crítica
            logger.debug("Erro ao registrar na memória: %s", mem_exc)
