"""Morphic field representations used in QUALIA.

This module provides two simplified classes: :class:`MorphicField` and
:class:`QuantumMorphicField`. Both store an internal matrix representing the
state of the field and expose :meth:`evolve_field` to update this state. The
implementation follows the experimental operators available in
:mod:`qualia.core.folding` and :mod:`qualia.core.resonance`.
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import List, Optional

import numpy as np

from .folding import apply_folding
from .resonance import apply_resonance
from ..utils.logger import get_logger
from ..memory.holographic_memory import HolographicMemory

logger = get_logger(__name__)


@dataclass
class MorphicField:
    """Classical morphic field representation.

    Parameters
    ----------
    dimension
        Size of the square field matrix.
    field_strength
        Coefficient controlling the resonance transformation.
    history_maxlen
        Maximum number of states kept in ``history``.
    """

    dimension: int
    field_strength: float = 0.1
    history_maxlen: int = 100
    state: np.ndarray = field(init=False)
    history: List[np.ndarray] = field(default_factory=list, init=False)

    def __post_init__(self) -> None:
        self.state = np.zeros((self.dimension, self.dimension))
        logger.info(
            "MorphicField initialized with dimension %s and strength %.3f",
            self.dimension,
            self.field_strength,
        )

    def evolve_field(self, delta: Optional[np.ndarray] = None) -> np.ndarray:
        """Evolve the morphic field applying resonance.

        Parameters
        ----------
        delta
            Optional matrix to add to the current state before resonance.

        Returns
        -------
        np.ndarray
            Updated field state.
        """
        if delta is not None:
            if not isinstance(delta, np.ndarray):
                raise TypeError("delta must be a numpy array")
            if delta.shape != self.state.shape:
                delta = np.resize(delta, self.state.shape)
            self.state = self.state + delta

        self.state = apply_resonance(self.state, strength=self.field_strength)
        self.history.append(self.state.copy())
        if len(self.history) > self.history_maxlen:
            self.history.pop(0)

        logger.debug("Field evolved; history length=%d", len(self.history))
        return self.state


@dataclass
class QuantumMorphicField(MorphicField):
    """Quantum-aware morphic field using folding and resonance."""

    alpha: float = 0.1
    coherence_length: float = 0.5
    memory_capacity: int = 1000
    holographic_memory_capacity: int = 2000
    holographic_memory: HolographicMemory = field(init=False)

    def __post_init__(self) -> None:  # noqa: D401 - init
        super().__post_init__()
        self.holographic_memory = HolographicMemory(
            max_items=self.holographic_memory_capacity
        )
        logger.info(
            "HolographicMemory initialized with capacity %d",
            self.holographic_memory_capacity,
        )

    def evolve_field(self, delta: Optional[np.ndarray] = None) -> np.ndarray:
        """Evolve the field using folding followed by resonance."""
        if delta is not None:
            if not isinstance(delta, np.ndarray):
                raise TypeError("delta must be a numpy array")
            if delta.shape != self.state.shape:
                delta = np.resize(delta, self.state.shape)
            self.state = self.state + delta

        folded = apply_folding(self.state, alpha=self.alpha)
        self.state = apply_resonance(folded, strength=self.field_strength)
        self.history.append(self.state.copy())
        if len(self.history) > self.history_maxlen:
            self.history.pop(0)

        logger.debug(
            "Quantum field evolved; alpha=%.3f, history length=%d",
            self.alpha,
            len(self.history),
        )
        return self.state

    # Metric methods following documentation pseudocode
    def calculate_field_strength(self) -> float:
        """Return normalized field strength."""
        norm_val = np.linalg.norm(self.state)
        max_possible = self.state.size**0.5
        return float(norm_val / (max_possible + 1e-9))

    def calculate_quantum_resonance(self) -> float:
        """Placeholder metric for quantum resonance."""
        return float(np.random.rand())

    def calculate_morphic_coherence(self) -> float:
        """Mean pairwise correlation of the field tensor."""
        corr = np.corrcoef(self.state.flatten())
        return float(np.mean(corr))

    def calculate_pattern_entropy(self) -> float:
        """Placeholder metric for pattern entropy."""
        return float(np.random.rand())

    def calculate_field_energy(self) -> float:
        """Return the squared energy of the field tensor."""
        return float(np.sum(self.state**2))

    def calculate_emergence_potential(self) -> float:
        """Placeholder metric for emergence potential."""
        return float(np.random.rand())
