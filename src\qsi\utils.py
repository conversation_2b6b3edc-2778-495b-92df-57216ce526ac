import json
import os
import subprocess
import zipfile
from typing import Any, List

import numpy as np


def is_unitary(matrix: np.ndarray, *, atol: float = 1e-9) -> bool:
    """Return True if ``matrix`` is unitary within ``atol`` tolerance."""
    matrix = np.asarray(matrix)
    if matrix.ndim != 2 or matrix.shape[0] != matrix.shape[1]:
        return False
    identity = np.eye(matrix.shape[0], dtype=matrix.dtype)
    return np.allclose(matrix.conj().T @ matrix, identity, atol=atol)


def safe_extract_zip(zip_ref: zipfile.ZipFile, extract_to: str) -> None:
    """Safely extract ``zip_ref`` into ``extract_to`` preventing path traversal."""
    os.makedirs(extract_to, exist_ok=True)
    for member in zip_ref.namelist():
        member_path = os.path.join(extract_to, member)
        if not os.path.realpath(member_path).startswith(os.path.realpath(extract_to)):
            raise Exception("Arquivo do zip tenta escapar do diret\u00f3rio de destino")
    zip_ref.extractall(extract_to)

def run_command(command: List[str], **kwargs) -> subprocess.CompletedProcess:
    """Executa um comando e retorna o resultado.

    Fornece uma camada única para chamadas a ``subprocess.run`` com ``check=True``
    e captura de saída opcional. Remove ``check`` de ``kwargs`` para evitar
    conflito ao repassar argumentos adicionais.
    """
    check_flag = kwargs.pop("check", True)
    return subprocess.run(command, check=check_flag, **kwargs)


def load_json(file_path: str, *, default: Any = None) -> Any:
    """Carrega ``file_path`` como JSON, retornando ``default`` em caso de erro."""
    try:
        with open(file_path, "r", encoding="utf-8") as fh:
            return json.load(fh)
    except Exception as e:
        print(f"    Erro ao carregar {file_path}: {e}")
        return default


def save_json(data: Any, file_path: str) -> None:
    """Salva ``data`` em ``file_path`` como JSON."""
    try:
        with open(file_path, "w", encoding="utf-8") as fh:
            json.dump(data, fh, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"    Erro ao salvar {file_path}: {e}")
