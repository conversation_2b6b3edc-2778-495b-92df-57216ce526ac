# Estratégias do QUALIA

Este diretório contém as estratégias de trading utilizadas pelo QUALIA. A forma
recomendada de carregar configurações é utilizando o
`ConfigManager` presente em `qualia.config.config_manager`.

Exemplo básico de criação de uma estratégia via `StrategyFactory`:

```python
from qualia.config.config_manager import ConfigManager
from qualia.strategies.strategy_factory import StrategyFactory

manager = ConfigManager("config/strategy_parameters.yaml")
manager.load()

strategy = StrategyFactory.create_strategy(
    alias="NovaEstrategiaQUALIA",
    context={"symbol": "BTC/USDT", "timeframe": "1h"},
    config_manager=manager,
)
```

O objeto `ConfigManager` pode ser repassado diretamente ao construtor das
estratégias para que elas acessem seções específicas conforme necessário.
