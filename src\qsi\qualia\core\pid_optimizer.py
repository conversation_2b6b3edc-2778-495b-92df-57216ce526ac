from __future__ import annotations

"""Helpers for tuning PID coefficients.

These utilities store performance metrics, fit a simple linear model to predict
profit and loss (PnL) based on the proportional (``Kp``) and integral (``Ki``)
coefficients and periodically adjust a :class:`~src.qualia.core.qast_core.QASTCore`
controller.

The :func:`schedule_pid_optimization` helper attaches a background optimisation
task to the current ``asyncio`` event loop when one is running. If invoked when
no loop is running it simply returns ``None`` so that the caller can decide
whether to start its own loop.
"""

import json
import os
import time
from datetime import datetime, timezone
from typing import List, Tuple, Optional

import asyncio

import numpy as np
from scipy.optimize import minimize

try:  # pragma: no cover - optional dependency
    from datadog import DogStatsd

    _DATADOG_AVAILABLE = True
except Exception:  # pragma: no cover - datadog not installed
    DogStatsd = None  # type: ignore[assignment]
    _DATADOG_AVAILABLE = False
from ..config.settings import settings
from ..utils.logger import get_logger
from ..utils.persistence import load_json_safe, save_json_safe

logger = get_logger(__name__)


def _performance_file() -> str:
    """Return the path to the PID performance file from ``settings``."""
    return settings.pid_performance_file


def _coeffs_file() -> str:
    """Return the path to the PID coefficients file from ``settings``."""
    return settings.pid_coeffs_file


_STATSD: DogStatsd | None = None


def _statsd_client() -> DogStatsd | None:
    """Return a singleton ``DogStatsd`` client when metrics are enabled."""
    global _STATSD
    if (
        not settings.metrics_enabled
        or not settings.pid_metrics_enabled
        or not _DATADOG_AVAILABLE
        or DogStatsd is None
    ):
        return None
    if _STATSD is None:
        _STATSD = DogStatsd()
    return _STATSD


def record_pid_performance(kp: float, ki: float, pnl: float) -> None:
    """Store a single PID performance observation.

    Parameters
    ----------
    kp : float
        Proportional gain in use when the PnL was observed.
    ki : float
        Integral gain in use when the PnL was observed.
    pnl : float
        Profit and loss value obtained with the given coefficients.

    Examples
    --------
    >>> from ..core.pid_optimizer import record_pid_performance
    >>> record_pid_performance(0.6, 0.05, 3.2)
    """
    records = load_json_safe(_performance_file(), [])
    records.append(
        {
            "timestamp": datetime.now(timezone.utc).timestamp(),
            "Kp": kp,
            "Ki": ki,
            "pnl": pnl,
        }
    )
    save_json_safe(_performance_file(), records)
    client = _statsd_client()
    if client:
        client.increment("pid.record")


def load_coefficients() -> Tuple[float, float]:
    coeff_path = _coeffs_file()
    if os.path.exists(coeff_path):
        try:
            with open(coeff_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            return float(data.get("Kp", 0.6)), float(data.get("Ki", 0.05))
        except (OSError, json.JSONDecodeError, TypeError, ValueError) as exc:
            logger.warning("Failed to read coefficient file %s: %s", coeff_path, exc)
    return 0.6, 0.05


def _save_coefficients(kp: float, ki: float) -> None:
    coeff_path = _coeffs_file()
    os.makedirs(os.path.dirname(coeff_path), exist_ok=True)
    payload = {
        "Kp": kp,
        "Ki": ki,
        "timestamp": datetime.now(timezone.utc).timestamp(),
    }
    try:
        with open(coeff_path, "w", encoding="utf-8") as f:
            json.dump(payload, f, indent=2)
    except OSError as exc:
        logger.error("Failed to store coefficients %s: %s", coeff_path, exc)


def optimize_pid(history_days: int = 7) -> Tuple[float, float]:
    """Compute optimal PID coefficients from stored performance data.

    Parameters
    ----------
    history_days : int, optional
        Number of days of historical records to consider. The default of
        seven days mirrors the weekly optimisation cadence.

    Returns
    -------
    tuple[float, float]
        The new ``(Kp, Ki)`` coefficients. If insufficient data is available
        the most recently saved coefficients are returned.

    Examples
    --------
    >>> from ..core.pid_optimizer import optimize_pid
    >>> kp, ki = optimize_pid()
    """
    client = _statsd_client()
    if client:
        client.increment("pid.optimize.start")
    start = time.perf_counter()
    try:
        records = load_json_safe(_performance_file(), [])
        if not records:
            result = load_coefficients()
        else:
            cutoff = datetime.now(timezone.utc).timestamp() - history_days * 86400
            recent = [r for r in records if r.get("timestamp", 0) >= cutoff]
            if len(recent) < 2:
                result = load_coefficients()
            else:
                X = np.array([[r["Kp"], r["Ki"], 1.0] for r in recent])
                y = np.array([r["pnl"] for r in recent])
                coef, *_ = np.linalg.lstsq(X, y, rcond=None)

                def neg_pred(params: np.ndarray) -> float:
                    kp, ki = params
                    return -(coef[0] * kp + coef[1] * ki + coef[2])

                init = [recent[-1]["Kp"], recent[-1]["Ki"]]
                res = minimize(neg_pred, init, method="Nelder-Mead")
                kp_opt, ki_opt = float(res.x[0]), float(res.x[1])
                _save_coefficients(kp_opt, ki_opt)
                result = (kp_opt, ki_opt)
        client = _statsd_client()
        if client:
            client.timing("pid.optimize_ms", (time.perf_counter() - start) * 1000)
            client.increment("pid.optimize.success")
        return result
    except Exception:
        client = _statsd_client()
        if client:
            client.timing("pid.optimize_ms", (time.perf_counter() - start) * 1000)
            client.increment("pid.optimize.error")
        raise


async def _pid_optimization_loop(
    pid_controller: "PIDController", interval_seconds: float = 7 * 86400
) -> None:
    """Background loop periodically optimising PID coefficients.

    Parameters
    ----------
    pid_controller : PIDController
        Controller instance receiving updated ``Kp`` and ``Ki`` values.
    interval_seconds : float, optional
        Sleep duration between optimisation cycles.
    """
    while True:
        try:
            await asyncio.sleep(interval_seconds)
            optimize_pid()
            kp, ki = load_coefficients()
            pid_controller.Kp = kp
            pid_controller.Ki = ki
        except asyncio.CancelledError:  # pragma: no cover - graceful shutdown
            logger.info("PID optimisation task cancelled")
            break
        except Exception:  # pragma: no cover - log and keep running
            logger.exception("PID optimisation failed")


def schedule_pid_optimization(
    pid_controller: "PIDController", interval_seconds: float = 7 * 86400
) -> Optional[asyncio.Task]:
    """Schedule periodic optimisation of the given PID controller.

    Parameters
    ----------
    pid_controller : PIDController
        Instance to update when new coefficients are computed.
    interval_seconds : float, optional
        How often to run the optimisation loop in seconds. Defaults to one week.

    Returns
    -------
    Optional[asyncio.Task]
        The created background task or ``None`` if no event loop is running.

    Examples
    --------
    >>> task = schedule_pid_optimization(controller, interval_seconds=3600)
    >>> task is not None
    True
    """
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:  # pragma: no cover - no running loop
        return None
    return loop.create_task(_pid_optimization_loop(pid_controller, interval_seconds))


__all__ = [
    "record_pid_performance",
    "optimize_pid",
    "load_coefficients",
    "schedule_pid_optimization",
]
