from __future__ import annotations

"""Incremental history store for Farsight insights."""

import json
import os
from datetime import datetime, timezone
from threading import Lock
from typing import Any, Dict, List

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

_DEFAULT_PATH = os.getenv("QUALIA_FARSIGHT_HISTORY", "farsight_history.jsonl")


class FarsightHistory:
    """Append-only JSONL store for Farsight insights."""

    def __init__(self, path: str | None = None) -> None:
        self.path = path or _DEFAULT_PATH
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        self._lock = Lock()
        logger.info("FarsightHistory persisting to %s", self.path)

    def append_batch(self, insights: List[Dict[str, Any]]) -> None:
        timestamp = datetime.now(timezone.utc).timestamp()
        with self._lock:
            with open(self.path, "a", encoding="utf-8") as fh:
                for ins in insights:
                    record = ins.copy()
                    record["timestamp"] = timestamp
                    fh.write(json.dumps(record, ensure_ascii=False) + "\n")


__all__ = ["FarsightHistory"]
