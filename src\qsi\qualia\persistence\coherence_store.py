from __future__ import annotations

"""Persistence layer for Nexus coherence and alert events.

Writes events to an *append-only* JSON Lines file (one dict per line). This file
can be later replayed for historical analysis or model retraining.
"""

import json
import os
from datetime import datetime, timezone
from typing import Dict, Any
from threading import Lock

from qualia.memory.event_bus import SimpleEventBus
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

_DEFAULT_PATH = os.getenv("QUALIA_NEXUS_HISTORY", "nexus_coherence_history.jsonl")


class CoherenceStore:
    """Subscribe to EventBus and persist coherence + alert events."""

    def __init__(self, event_bus: SimpleEventBus, path: str | None = None) -> None:
        self.path = path or _DEFAULT_PATH
        self.event_bus = event_bus
        # ensure dir
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        # subscribe
        self.event_bus.subscribe("nexus.cross_modal_coherence", self._on_coherence)
        self.event_bus.subscribe("nexus.alert", self._on_alert)
        self._lock = Lock()
        logger.info("CoherenceStore persistindo em %s", self.path)

    # ------------------------------------------------------------------

    def _append(self, record: Dict[str, Any]) -> None:
        with self._lock:
            with open(self.path, "a", encoding="utf-8") as fh:
                fh.write(json.dumps(record, ensure_ascii=False) + "\n")

    def _on_coherence(self, payload: Any) -> None:
        if hasattr(payload, "coherence"):
            ts = payload.timestamp.timestamp()
            coh = payload.coherence
        else:
            ts = payload.get("timestamp", datetime.now(timezone.utc).timestamp())
            coh = payload.get("coherence")
        rec = {
            "type": "coherence",
            "timestamp": ts,
            "coherence": coh,
        }
        self._append(rec)

    def _on_alert(self, payload: Any) -> None:
        if hasattr(payload, "coherence"):
            ts = payload.timestamp.timestamp()
            coh = payload.coherence
            th = payload.threshold
        else:
            ts = payload.get("timestamp", datetime.now(timezone.utc).timestamp())
            coh = payload.get("coherence")
            th = payload.get("threshold")
        rec = {
            "type": "alert",
            "timestamp": ts,
            "coherence": coh,
            "threshold": th,
        }
        self._append(rec)
