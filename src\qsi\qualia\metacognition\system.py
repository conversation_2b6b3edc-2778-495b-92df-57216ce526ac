"""
Metacognition System

Implements metacognitive capabilities for self-awareness, strategy adaptation,
and continuous improvement of trading performance.
"""

import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from collections import deque
from scipy import stats
import json
from ..utils.logger import get_logger
from ..memory.event_bus import SimpleEventBus
from ..utils.event_bus import MarketDataUpdated
import aiohttp


@dataclass
class PerformanceMetric:
    """Represents a performance metric"""

    name: str
    value: float
    timestamp: datetime
    category: str
    target_value: Optional[float] = None
    improvement_trend: str = "stable"  # "improving", "declining", "stable"


@dataclass
class StrategyAdjustment:
    """Represents a strategy adjustment"""

    id: str
    strategy_component: str
    adjustment_type: str
    old_value: Any
    new_value: Any
    reason: str
    expected_impact: str
    timestamp: datetime
    effectiveness: Optional[float] = None


class MetacognitionSystem:
    """Core component responsible for metacognitive supervision.

    The system observes trading performance, detects improvement
    opportunities and adapts strategies accordingly. Health monitoring
    parameters are configured through ``config``:

    ``health_check_interval``
        Interval in seconds between successive health checks.
    ``health_stale_threshold``
        Maximum age in seconds of the latest market data before the
        system is considered unhealthy.
    ``alert_webhook``
        Optional HTTP endpoint used to send health alerts when the
        system becomes unhealthy.
    """

    def __init__(self, config: Dict, event_bus: Optional[SimpleEventBus] = None):
        """Instantiate the metacognition system.

        Parameters
        ----------
        config : dict
            Configuration values. ``health_check_interval`` sets how often the
            system verifies the freshness of market data. ``health_stale_threshold``
            defines the staleness limit in seconds before a health alert is
            triggered. ``alert_webhook`` optionally provides an URL to receive
            health alerts.
        event_bus : SimpleEventBus, optional
            Event bus used to subscribe to market data updates.
        """

        self.config = config
        self.logger = get_logger(__name__)
        self.event_bus = event_bus

        # Metacognition parameters
        self.adaptation_rate = config.get("adaptation_rate", 0.1)
        self.confidence_threshold = config.get("confidence_threshold", 0.7)
        self.feedback_window = config.get("feedback_window", 100)
        self.strategy_evaluation_period = config.get("strategy_evaluation_period", 3600)

        # Performance tracking
        self.performance_metrics: deque = deque(maxlen=1000)
        self.strategy_adjustments: deque = deque(maxlen=500)

        # Self-awareness components
        self.self_assessment = {
            "confidence_level": 0.5,
            "learning_rate": 0.1,
            "adaptation_frequency": 0.0,
            "performance_trend": "stable",
            "risk_tolerance": 0.5,
            "decision_quality": 0.5,
        }

        # Strategy effectiveness tracking
        self.strategy_effectiveness = {}
        self.cognitive_biases = {
            "overconfidence": 0.0,
            "confirmation_bias": 0.0,
            "anchoring_bias": 0.0,
            "recency_bias": 0.0,
        }

        # Adaptation history
        self.adaptation_history = []
        self.feedback_loops = []

        # Health monitoring parameters
        self.health_check_interval = float(config.get("health_check_interval", 60.0))
        self.health_stale_threshold = float(config.get("health_stale_threshold", 300.0))
        self.alert_webhook = config.get("alert_webhook")
        self.last_market_data_time: Optional[datetime] = None
        self.health_state: str = "HEALTHY"

        # Background tasks
        self.evaluation_task = None
        self.adaptation_task = None
        self.health_monitor_task = None

        if self.event_bus is not None:
            self.event_bus.subscribe(
                "market.data.updated", self._on_market_data_updated
            )

    async def initialize(self):
        """Initialize the metacognition system"""
        try:
            self.logger.info("Initializing metacognition system...")

            # Load existing metacognitive state
            await self._load_metacognitive_state()

            # Start background evaluation tasks
            self.evaluation_task = asyncio.create_task(self._evaluation_loop())
            self.adaptation_task = asyncio.create_task(self._adaptation_loop())
            self.health_monitor_task = asyncio.create_task(self._health_monitor_loop())

            self.logger.info("Metacognition system initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize metacognition system: {e}")
            raise

    async def close(self):
        """Close the metacognition system"""
        try:
            # Cancel background tasks
            if self.evaluation_task:
                self.evaluation_task.cancel()
            if self.adaptation_task:
                self.adaptation_task.cancel()
            if self.health_monitor_task:
                self.health_monitor_task.cancel()

            # Save final state
            await self._save_metacognitive_state()

            self.logger.info("Metacognition system closed")

        except Exception as e:
            self.logger.error(f"Error closing metacognition system: {e}")

    async def update_performance(self, trade_results: List[Dict]):
        """Update performance metrics with new trade results"""
        try:
            for trade_result in trade_results:
                # Extract performance metrics
                metrics = self._extract_performance_metrics(trade_result)

                for metric in metrics:
                    self.performance_metrics.append(metric)

                # Update self-assessment
                self._update_self_assessment(trade_result)

            # Trigger metacognitive analysis
            await self._analyze_performance()

        except Exception as e:
            self.logger.error(f"Error updating performance: {e}")

    def _extract_performance_metrics(
        self, trade_result: Dict
    ) -> List[PerformanceMetric]:
        """Extract performance metrics from trade result"""
        metrics = []
        timestamp = datetime.now(timezone.utc)

        try:
            # Basic performance metrics
            if "success" in trade_result:
                success_value = 1.0 if trade_result["success"] else 0.0
                metrics.append(
                    PerformanceMetric(
                        name="trade_success_rate",
                        value=success_value,
                        timestamp=timestamp,
                        category="execution",
                        target_value=0.8,
                    )
                )

            # Profit/Loss metrics
            if "profit_loss" in trade_result:
                pl_value = float(trade_result["profit_loss"])
                metrics.append(
                    PerformanceMetric(
                        name="profit_loss",
                        value=pl_value,
                        timestamp=timestamp,
                        category="financial",
                        target_value=0.0,  # Positive P&L target
                    )
                )

            # Execution speed
            if "execution_time" in trade_result:
                exec_time = float(trade_result["execution_time"])
                metrics.append(
                    PerformanceMetric(
                        name="execution_speed",
                        value=exec_time,
                        timestamp=timestamp,
                        category="execution",
                        target_value=1.0,  # Target 1 second or less
                    )
                )

            # Confidence accuracy
            if "confidence" in trade_result and "success" in trade_result:
                confidence = float(trade_result["confidence"])
                success = trade_result["success"]

                # Measure calibration: high confidence should correlate with success
                calibration_error = abs(confidence - (1.0 if success else 0.0))
                metrics.append(
                    PerformanceMetric(
                        name="confidence_calibration",
                        value=1.0 - calibration_error,  # Higher is better
                        timestamp=timestamp,
                        category="decision_quality",
                        target_value=0.9,
                    )
                )

            return metrics

        except Exception as e:
            self.logger.error(f"Error extracting performance metrics: {e}")
            return metrics

    def _update_self_assessment(self, trade_result: Dict):
        """Update self-awareness based on trade result"""
        try:
            # Update confidence based on outcome
            if "success" in trade_result:
                success = trade_result["success"]

                if success:
                    # Successful trade: slight confidence increase
                    self.self_assessment["confidence_level"] = min(
                        1.0, self.self_assessment["confidence_level"] + 0.01
                    )
                else:
                    # Failed trade: confidence decrease
                    self.self_assessment["confidence_level"] = max(
                        0.0, self.self_assessment["confidence_level"] - 0.02
                    )

            # Update decision quality assessment
            if "confidence" in trade_result and "success" in trade_result:
                predicted_confidence = trade_result["confidence"]
                actual_success = trade_result["success"]

                # Good decision if high confidence led to success or low confidence avoided failure
                decision_quality = (
                    predicted_confidence
                    if actual_success
                    else (1.0 - predicted_confidence)
                )

                # Update with exponential moving average
                alpha = 0.1
                self.self_assessment["decision_quality"] = (
                    alpha * decision_quality
                    + (1 - alpha) * self.self_assessment["decision_quality"]
                )

        except Exception as e:
            self.logger.error(f"Error updating self-assessment: {e}")

    async def _analyze_performance(self):
        """Analyze recent performance for insights"""
        try:
            if len(self.performance_metrics) < 10:
                return

            # Analyze trends in different metric categories
            categories = set(metric.category for metric in self.performance_metrics)

            for category in categories:
                await self._analyze_category_performance(category)

            # Detect cognitive biases
            self._detect_cognitive_biases()

            # Update performance trend
            self._update_performance_trend()

        except Exception as e:
            self.logger.error(f"Error analyzing performance: {e}")

    async def _analyze_category_performance(self, category: str):
        """Analyze performance for a specific category"""
        try:
            # Get recent metrics for this category
            recent_metrics = [
                metric
                for metric in list(self.performance_metrics)[-50:]
                if metric.category == category
            ]

            if len(recent_metrics) < 5:
                return

            # Calculate trends
            values = [metric.value for metric in recent_metrics]
            timestamps = [metric.timestamp for metric in recent_metrics]

            # Linear regression for trend
            time_numeric = [(ts - timestamps[0]).total_seconds() for ts in timestamps]
            if len(time_numeric) > 1 and np.std(time_numeric) > 0:
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    time_numeric, values
                )

                # Determine trend
                if p_value < 0.05:  # Significant trend
                    trend = "improving" if slope > 0 else "declining"
                else:
                    trend = "stable"

                # Update trend in recent metrics
                for metric in recent_metrics[-5:]:  # Last 5 metrics
                    metric.improvement_trend = trend

            # Check for performance against targets
            metrics_with_targets = [
                m for m in recent_metrics if m.target_value is not None
            ]
            if metrics_with_targets:
                target_achievement = np.mean(
                    [
                        1.0 if m.value >= m.target_value else m.value / m.target_value
                        for m in metrics_with_targets
                    ]
                )

                # Store category performance
                self.strategy_effectiveness[f"{category}_performance"] = (
                    target_achievement
                )

        except Exception as e:
            self.logger.error(f"Error analyzing category {category}: {e}")

    def _detect_cognitive_biases(self):
        """Detect cognitive biases in decision making"""
        try:
            recent_metrics = list(self.performance_metrics)[-100:]

            # Detect overconfidence bias
            confidence_metrics = [
                m for m in recent_metrics if m.name == "confidence_calibration"
            ]

            if len(confidence_metrics) > 10:
                avg_calibration = np.mean([m.value for m in confidence_metrics])
                # Low calibration indicates overconfidence
                self.cognitive_biases["overconfidence"] = max(
                    0.0, 1.0 - avg_calibration
                )

            # Detect recency bias (overweight recent performance)
            success_metrics = [
                m for m in recent_metrics if m.name == "trade_success_rate"
            ]

            if len(success_metrics) > 20:
                recent_success = np.mean([m.value for m in success_metrics[-10:]])
                overall_success = np.mean([m.value for m in success_metrics])

                # Large difference suggests recency bias
                recency_difference = abs(recent_success - overall_success)
                self.cognitive_biases["recency_bias"] = min(1.0, recency_difference * 2)

            # Detect confirmation bias (look for patterns in strategy adjustments)
            if len(self.strategy_adjustments) > 10:
                adjustment_directions = []
                for adj in list(self.strategy_adjustments)[-20:]:
                    if adj.effectiveness is not None:
                        if adj.effectiveness > 0.5:
                            adjustment_directions.append(1)  # Positive
                        else:
                            adjustment_directions.append(-1)  # Negative

                if len(adjustment_directions) > 10:
                    # Bias toward positive adjustments despite mixed results
                    positive_ratio = np.mean(
                        [1 for d in adjustment_directions if d > 0]
                    )
                    if positive_ratio > 0.8:  # Too optimistic
                        self.cognitive_biases["confirmation_bias"] = (
                            positive_ratio - 0.5
                        )

        except Exception as e:
            self.logger.error(f"Error detecting cognitive biases: {e}")

    def _update_performance_trend(self):
        """Update overall performance trend assessment"""
        try:
            if len(self.performance_metrics) < 20:
                return

            # Get recent profit/loss metrics
            pl_metrics = [
                m
                for m in list(self.performance_metrics)[-50:]
                if m.name == "profit_loss"
            ]

            if len(pl_metrics) >= 10:
                recent_pl = [m.value for m in pl_metrics[-10:]]
                earlier_pl = (
                    [m.value for m in pl_metrics[-20:-10]]
                    if len(pl_metrics) >= 20
                    else recent_pl
                )

                recent_avg = np.mean(recent_pl)
                earlier_avg = np.mean(earlier_pl)

                if recent_avg > earlier_avg * 1.1:
                    self.self_assessment["performance_trend"] = "improving"
                elif recent_avg < earlier_avg * 0.9:
                    self.self_assessment["performance_trend"] = "declining"
                else:
                    self.self_assessment["performance_trend"] = "stable"

        except Exception as e:
            self.logger.error(f"Error updating performance trend: {e}")

    async def _evaluation_loop(self):
        """Background task for regular performance evaluation"""
        try:
            while True:
                await asyncio.sleep(self.strategy_evaluation_period)
                await self._evaluate_strategies()

        except asyncio.CancelledError:
            self.logger.info("Evaluation loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in evaluation loop: {e}")

    async def _evaluate_strategies(self):
        """Evaluate current strategies and their effectiveness"""
        try:
            # Evaluate recent strategy adjustments
            await self._evaluate_strategy_adjustments()

            # Identify improvement opportunities
            opportunities = self._identify_improvement_opportunities()

            # Log evaluation results
            if opportunities:
                self.logger.info(
                    f"Identified {len(opportunities)} improvement opportunities"
                )
                for opp in opportunities[:3]:  # Log top 3
                    self.logger.info(
                        f"Opportunity: {opp['description']} (Impact: {opp['expected_impact']})"
                    )

        except Exception as e:
            self.logger.error(f"Error evaluating strategies: {e}")

    async def _evaluate_strategy_adjustments(self):
        """Evaluate the effectiveness of recent strategy adjustments"""
        try:
            current_time = datetime.now(timezone.utc)

            for adjustment in self.strategy_adjustments:
                if adjustment.effectiveness is not None:
                    continue  # Already evaluated

                # Check if enough time has passed for evaluation
                time_since_adjustment = (
                    current_time - adjustment.timestamp
                ).total_seconds()
                if time_since_adjustment < 3600:  # Need at least 1 hour
                    continue

                # Get performance metrics after adjustment
                post_adjustment_metrics = [
                    m
                    for m in self.performance_metrics
                    if m.timestamp > adjustment.timestamp
                ]

                if len(post_adjustment_metrics) < 10:
                    continue  # Need sufficient data

                # Compare with pre-adjustment performance
                pre_adjustment_metrics = [
                    m
                    for m in self.performance_metrics
                    if m.timestamp < adjustment.timestamp
                    and (adjustment.timestamp - m.timestamp).total_seconds() < 3600
                ]

                if len(pre_adjustment_metrics) >= 5:
                    effectiveness = self._calculate_adjustment_effectiveness(
                        pre_adjustment_metrics, post_adjustment_metrics, adjustment
                    )
                    adjustment.effectiveness = effectiveness

                    # Log significant results
                    if effectiveness > 0.7:
                        self.logger.info(
                            f"Successful adjustment: {adjustment.adjustment_type} "
                            f"(Effectiveness: {effectiveness:.2f})"
                        )
                    elif effectiveness < 0.3:
                        self.logger.warning(
                            f"Ineffective adjustment: {adjustment.adjustment_type} "
                            f"(Effectiveness: {effectiveness:.2f})"
                        )

        except Exception as e:
            self.logger.error(f"Error evaluating strategy adjustments: {e}")

    def _calculate_adjustment_effectiveness(
        self,
        pre_metrics: List[PerformanceMetric],
        post_metrics: List[PerformanceMetric],
        adjustment: StrategyAdjustment,
    ) -> float:
        """Calculate the effectiveness of a strategy adjustment"""
        try:
            # Focus on relevant metrics for the adjustment
            relevant_categories = {
                "risk_adjustment": ["financial", "execution"],
                "confidence_adjustment": ["decision_quality"],
                "execution_adjustment": ["execution"],
                "strategy_adjustment": ["financial", "decision_quality"],
            }

            categories = relevant_categories.get(
                adjustment.adjustment_type, ["financial"]
            )

            # Calculate average performance before and after
            pre_values = []
            post_values = []

            for category in categories:
                pre_category = [m.value for m in pre_metrics if m.category == category]
                post_category = [
                    m.value for m in post_metrics if m.category == category
                ]

                if pre_category and post_category:
                    pre_values.extend(pre_category)
                    post_values.extend(post_category)

            if not pre_values or not post_values:
                return 0.5  # Neutral if no data

            pre_avg = np.mean(pre_values)
            post_avg = np.mean(post_values)

            # Calculate improvement ratio
            if pre_avg != 0:
                improvement_ratio = post_avg / pre_avg
                # Convert to 0-1 scale where 1 is good improvement
                effectiveness = min(1.0, max(0.0, (improvement_ratio - 0.5) * 2))
            else:
                effectiveness = 0.5

            return effectiveness

        except Exception as e:
            self.logger.error(f"Error calculating adjustment effectiveness: {e}")
            return 0.5

    def _identify_improvement_opportunities(self) -> List[Dict]:
        """Identify opportunities for improvement"""
        opportunities = []

        try:
            # Analyze performance gaps
            recent_metrics = list(self.performance_metrics)[-50:]

            # Group by category and identify underperforming areas
            categories = set(m.category for m in recent_metrics)

            for category in categories:
                category_metrics = [m for m in recent_metrics if m.category == category]
                metrics_with_targets = [
                    m for m in category_metrics if m.target_value is not None
                ]

                if metrics_with_targets:
                    avg_achievement = np.mean(
                        [
                            m.value / m.target_value if m.target_value != 0 else 1.0
                            for m in metrics_with_targets
                        ]
                    )

                    if avg_achievement < 0.8:  # Underperforming
                        opportunities.append(
                            {
                                "type": "performance_gap",
                                "category": category,
                                "current_achievement": avg_achievement,
                                "target_achievement": 1.0,
                                "gap_size": 1.0 - avg_achievement,
                                "description": f"Improve {category} performance",
                                "expected_impact": (
                                    "high" if avg_achievement < 0.6 else "medium"
                                ),
                            }
                        )

            # Identify bias-related opportunities
            for bias_name, bias_level in self.cognitive_biases.items():
                if bias_level > 0.3:
                    opportunities.append(
                        {
                            "type": "cognitive_bias",
                            "bias_name": bias_name,
                            "bias_level": bias_level,
                            "description": f"Address {bias_name.replace('_', ' ')}",
                            "expected_impact": "high" if bias_level > 0.6 else "medium",
                        }
                    )

            # Identify strategy adjustment opportunities
            if self.self_assessment["performance_trend"] == "declining":
                opportunities.append(
                    {
                        "type": "strategy_revision",
                        "description": "Revise trading strategies due to declining performance",
                        "expected_impact": "high",
                        "urgency": "high",
                    }
                )

            # Sort by expected impact
            impact_priority = {"high": 3, "medium": 2, "low": 1}
            opportunities.sort(
                key=lambda x: impact_priority.get(x.get("expected_impact", "low"), 0),
                reverse=True,
            )

            return opportunities

        except Exception as e:
            self.logger.error(f"Error identifying improvement opportunities: {e}")
            return opportunities

    async def _adaptation_loop(self):
        """Background task for strategy adaptation"""
        try:
            while True:
                await asyncio.sleep(1800)  # Run every 30 minutes
                await self._adaptive_adjustment()

        except asyncio.CancelledError:
            self.logger.info("Adaptation loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in adaptation loop: {e}")

    async def _adaptive_adjustment(self):
        """Perform adaptive adjustments based on metacognitive analysis"""
        try:
            # Check if adaptation is needed
            if not self._should_adapt():
                return

            # Identify top improvement opportunity
            opportunities = self._identify_improvement_opportunities()
            if not opportunities:
                return

            top_opportunity = opportunities[0]

            # Generate adjustment based on opportunity type
            adjustment = self._generate_adjustment(top_opportunity)

            if adjustment:
                self.strategy_adjustments.append(adjustment)
                self.adaptation_history.append(
                    {
                        "timestamp": datetime.now(timezone.utc),
                        "opportunity": top_opportunity,
                        "adjustment": asdict(adjustment),
                        "self_assessment": self.self_assessment.copy(),
                    }
                )

                self.logger.info(
                    f"Applied adaptive adjustment: {adjustment.adjustment_type}"
                )

        except Exception as e:
            self.logger.error(f"Error in adaptive adjustment: {e}")

    def _should_adapt(self) -> bool:
        """Determine if adaptation is needed"""
        try:
            # Don't adapt too frequently
            if self.strategy_adjustments:
                last_adjustment = self.strategy_adjustments[-1]
                time_since_last = (
                    datetime.now(timezone.utc) - last_adjustment.timestamp
                ).total_seconds()
                if time_since_last < 3600:  # Wait at least 1 hour
                    return False

            # Adapt if confidence is low
            if self.self_assessment["confidence_level"] < self.confidence_threshold:
                return True

            # Adapt if performance is declining
            if self.self_assessment["performance_trend"] == "declining":
                return True

            # Adapt if significant cognitive bias detected
            max_bias = max(self.cognitive_biases.values())
            if max_bias > 0.5:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error determining adaptation need: {e}")
            return False

    def _generate_adjustment(self, opportunity: Dict) -> Optional[StrategyAdjustment]:
        """Generate a strategy adjustment based on an improvement opportunity"""
        try:
            adjustment_id = f"adj_{datetime.now(timezone.utc).timestamp()}"
            timestamp = datetime.now(timezone.utc)

            if opportunity["type"] == "performance_gap":
                category = opportunity["category"]

                if category == "execution":
                    return StrategyAdjustment(
                        id=adjustment_id,
                        strategy_component="execution_speed",
                        adjustment_type="execution_adjustment",
                        old_value=self.self_assessment.get("execution_speed", 1.0),
                        new_value=self.self_assessment.get("execution_speed", 1.0)
                        * 0.9,
                        reason=f"Improve {category} performance",
                        expected_impact="medium",
                        timestamp=timestamp,
                    )

                elif category == "financial":
                    return StrategyAdjustment(
                        id=adjustment_id,
                        strategy_component="risk_tolerance",
                        adjustment_type="risk_adjustment",
                        old_value=self.self_assessment["risk_tolerance"],
                        new_value=max(
                            0.1, self.self_assessment["risk_tolerance"] * 0.9
                        ),
                        reason="Reduce risk due to poor financial performance",
                        expected_impact="high",
                        timestamp=timestamp,
                    )

            elif opportunity["type"] == "cognitive_bias":
                bias_name = opportunity["bias_name"]

                if bias_name == "overconfidence":
                    return StrategyAdjustment(
                        id=adjustment_id,
                        strategy_component="confidence_threshold",
                        adjustment_type="confidence_adjustment",
                        old_value=self.confidence_threshold,
                        new_value=min(0.9, self.confidence_threshold * 1.1),
                        reason="Increase confidence threshold to combat overconfidence",
                        expected_impact="medium",
                        timestamp=timestamp,
                    )

                elif bias_name == "recency_bias":
                    return StrategyAdjustment(
                        id=adjustment_id,
                        strategy_component="feedback_window",
                        adjustment_type="strategy_adjustment",
                        old_value=self.feedback_window,
                        new_value=min(200, self.feedback_window * 1.2),
                        reason="Increase feedback window to reduce recency bias",
                        expected_impact="medium",
                        timestamp=timestamp,
                    )

            return None

        except Exception as e:
            self.logger.error(f"Error generating adjustment: {e}")
            return None

    async def _save_metacognitive_state(self):
        """Save metacognitive state to storage"""
        try:
            # Convert to serializable format
            metrics_data = []
            for metric in list(self.performance_metrics)[
                -500:
            ]:  # Save last 500 metrics
                metric_dict = asdict(metric)
                metric_dict["timestamp"] = metric.timestamp.isoformat()
                metrics_data.append(metric_dict)

            adjustments_data = []
            for adjustment in list(self.strategy_adjustments):
                adj_dict = asdict(adjustment)
                adj_dict["timestamp"] = adjustment.timestamp.isoformat()
                adjustments_data.append(adj_dict)

            state_data = {
                "schema_version": "v1",
                "performance_metrics": metrics_data,
                "strategy_adjustments": adjustments_data,
                "self_assessment": self.self_assessment,
                "strategy_effectiveness": self.strategy_effectiveness,
                "cognitive_biases": self.cognitive_biases,
                "adaptation_history": self.adaptation_history,
                "saved_at": datetime.now(timezone.utc).isoformat(),
            }

            # Save to file
            import os

            base_dir = "jsonstore/metacognition"
            os.makedirs(base_dir, exist_ok=True)

            if not os.access(base_dir, os.W_OK):
                # Fail fast when the directory cannot be written to
                raise PermissionError(f"No write permission for {base_dir}")

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            state_path = os.path.join(base_dir, f"{timestamp}.json")

            with open(state_path, "w") as f:
                json.dump(state_data, f, indent=2, default=str)

            self.logger.debug("Metacognitive state saved")

        except Exception as e:
            self.logger.error(f"Error saving metacognitive state: {e}")
            raise

    async def _load_metacognitive_state(self):
        """Load metacognitive state from storage"""
        try:
            import os

            base_dir = "jsonstore/metacognition"
            os.makedirs(base_dir, exist_ok=True)

            files = sorted(f for f in os.listdir(base_dir) if f.endswith(".json"))
            if not files:
                self.logger.info("No existing metacognitive state found")
                return

            state_path = os.path.join(base_dir, files[-1])

            with open(state_path, "r") as f:
                state_data = json.load(f)

            version = state_data.get("schema_version")
            if not version or not version.startswith("v") or int(version[1:]) < 1:
                self.logger.error(
                    f"Incompatible metacognition state schema version: {version}"
                )
                return

            # Load performance metrics
            for metric_dict in state_data.get("performance_metrics", []):
                try:
                    metric_dict["timestamp"] = datetime.fromisoformat(
                        metric_dict["timestamp"]
                    )
                    metric = PerformanceMetric(**metric_dict)
                    self.performance_metrics.append(metric)
                except Exception as e:
                    self.logger.warning(f"Failed to load performance metric: {e}")

            # Load strategy adjustments
            for adj_dict in state_data.get("strategy_adjustments", []):
                try:
                    adj_dict["timestamp"] = datetime.fromisoformat(
                        adj_dict["timestamp"]
                    )
                    adjustment = StrategyAdjustment(**adj_dict)
                    self.strategy_adjustments.append(adjustment)
                except Exception as e:
                    self.logger.warning(f"Failed to load strategy adjustment: {e}")

            # Load other state
            self.self_assessment.update(state_data.get("self_assessment", {}))
            self.strategy_effectiveness.update(
                state_data.get("strategy_effectiveness", {})
            )
            self.cognitive_biases.update(state_data.get("cognitive_biases", {}))
            self.adaptation_history = state_data.get("adaptation_history", [])

            self.logger.info(
                f"Loaded metacognitive state with {len(self.performance_metrics)} metrics"
            )

        except Exception as e:
            self.logger.error(f"Error loading metacognitive state: {e}")

    def get_metacognitive_summary(self) -> Dict:
        """Get summary of current metacognitive state"""
        return {
            "self_assessment": self.self_assessment.copy(),
            "cognitive_biases": self.cognitive_biases.copy(),
            "strategy_effectiveness": self.strategy_effectiveness.copy(),
            "performance_metrics_count": len(self.performance_metrics),
            "strategy_adjustments_count": len(self.strategy_adjustments),
            "recent_performance_trend": self.self_assessment["performance_trend"],
            "confidence_level": self.self_assessment["confidence_level"],
            "adaptation_frequency": len(self.adaptation_history),
            "last_adjustment": (
                self.strategy_adjustments[-1].timestamp.isoformat()
                if self.strategy_adjustments
                else None
            ),
        }

    def get_improvement_recommendations(self) -> List[Dict]:
        """Get current improvement recommendations"""
        try:
            opportunities = self._identify_improvement_opportunities()

            recommendations = []
            for opp in opportunities[:5]:  # Top 5 recommendations
                recommendation = {
                    "type": opp["type"],
                    "description": opp["description"],
                    "expected_impact": opp.get("expected_impact", "medium"),
                    "urgency": opp.get("urgency", "medium"),
                    "actionable": True,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
                recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            self.logger.error(f"Error getting improvement recommendations: {e}")
            return []

    # ------------------------------------------------------------------
    def _on_market_data_updated(self, payload: MarketDataUpdated) -> None:
        """Update timestamp when fresh market data arrives."""
        self.last_market_data_time = datetime.now(timezone.utc)

    # ------------------------------------------------------------------
    def update_market_data_time(self, timestamp: Optional[datetime] = None) -> None:
        """Manually update the timestamp of the latest valid market data.

        Parameters
        ----------
        timestamp : datetime, optional
            Explicit time of the most recent market data. When ``None`` the
            current UTC time is recorded.

        Returns
        -------
        None
            This method updates internal state only.
        """
        self.last_market_data_time = timestamp or datetime.now(timezone.utc)

    # ------------------------------------------------------------------
    def check_health(self) -> None:
        """Evaluate and update the current health state.

        The method compares the age of the last received market data with
        ``health_stale_threshold``. When the data is older than this threshold
        the system state changes to ``UNHEALTHY`` and an alert is triggered via
        :meth:`_send_alert`.

        Returns
        -------
        None
        """
        if self.last_market_data_time is None:
            new_state = "UNHEALTHY"
        else:
            delta = (
                datetime.now(timezone.utc) - self.last_market_data_time
            ).total_seconds()
            new_state = (
                "UNHEALTHY" if delta > self.health_stale_threshold else "HEALTHY"
            )

        if new_state != self.health_state:
            self.health_state = new_state
            if new_state == "UNHEALTHY":
                asyncio.create_task(self._send_alert("System entered UNHEALTHY state"))

    # ------------------------------------------------------------------
    async def _health_monitor_loop(self) -> None:
        """Continuously monitor system health.

        This coroutine sleeps for ``health_check_interval`` seconds between
        calls to :meth:`check_health`.

        Returns
        -------
        None
        """
        try:
            while True:
                await asyncio.sleep(self.health_check_interval)
                self.check_health()
        except asyncio.CancelledError:
            self.logger.info("Health monitor loop cancelled")
        except Exception as exc:
            self.logger.error(f"Error in health monitor loop: {exc}")

    # ------------------------------------------------------------------
    async def _send_alert(self, message: str) -> None:
        """Send an alert message to the configured webhook.

        Parameters
        ----------
        message : str
            Human readable message describing the alert condition.

        Returns
        -------
        None
        """
        if not self.alert_webhook:
            self.logger.warning("Alert webhook not configured; skipping alert")
            return
        try:
            async with aiohttp.ClientSession() as session:
                await session.post(self.alert_webhook, json={"text": message})
        except Exception as exc:
            self.logger.error(f"Failed to send alert: {exc}")
