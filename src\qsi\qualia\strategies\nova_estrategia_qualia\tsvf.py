from __future__ import annotations

"""TSVF computation helpers used by :mod:`nova_estrategia_qualia`."""

from typing import Dict
import importlib.util

from ...core import apply_anacronism, apply_folding, apply_resonance

from ...intentions import IntentionEnvelope
from ...retroselector import RetroSelector

import numpy as np
import pandas as pd
from scipy.stats import entropy

from ...utils.logger import get_logger
from ...trading.data_utils import is_data_empty

logger = get_logger(__name__)

# Detect optional acceleration libraries
CUPY_AVAILABLE = importlib.util.find_spec("cupy") is not None
QISKIT_AVAILABLE = importlib.util.find_spec("qiskit") is not None


def make_tsvf_state(price_segment: np.ndarray, vector_size: int) -> np.ndarray:
    """Normalize a price segment into a TSVF state vector."""
    if not isinstance(price_segment, np.ndarray):
        price_segment = np.array(price_segment, dtype=float)

    if len(price_segment) == 0:
        return np.zeros(vector_size)

    min_val = price_segment.min()
    max_val = price_segment.max()
    if max_val == min_val:
        normalized_segment = np.zeros_like(price_segment, dtype=float)
    else:
        normalized_segment = (price_segment - min_val) / (max_val - min_val) * 2 - 1

    if len(normalized_segment) == vector_size:
        vec = normalized_segment
    elif len(normalized_segment) > vector_size:
        vec = normalized_segment[:vector_size]
    else:
        vec = np.pad(
            normalized_segment,
            (0, vector_size - len(normalized_segment)),
            "constant",
            constant_values=0,
        )

    norm = np.linalg.norm(vec)
    return vec / norm if norm != 0 else np.zeros(vector_size)


def calculate_tsvf_outputs(
    prices_in: np.ndarray,
    prices_fin: np.ndarray,
    vector_size: int,
    alpha: float,
    gamma: float,
    cE: float,
    cH: float,
    k_components: int = 5,
    envelope: IntentionEnvelope | None = None,
    mc_paths: np.ndarray | None = None,
    retro_selector: RetroSelector | None = None,
    anacronism_steps: int | None = None,
    use_resonance: bool = False,
    resonance_strength: float | None = None,
    use_folding: bool = False,
    *,
    use_acceleration: bool = False,
    num_subfields: int = 4,
) -> Dict[str, float | list]:
    """Calculate TSVF metrics for a price window.

    Parameters
    ----------
    prices_in
        Segment of prices representing the initial state.
    prices_fin
        Segment of prices representing the final state when ``envelope`` is not
        used.
    vector_size
        Desired size of the TSVF vectors.
    alpha
        Forward evolution parameter.
    gamma
        Backward evolution parameter.
    cE
        Weight applied to the coherence metric ``E``.
    cH
        Weight applied to the entropy metric ``H``.
    k_components
        Number of vector components used when computing ``strength``.
    envelope, optional
        If provided alongside ``mc_paths``, ``psi_fin`` is obtained from the
        average of the trajectories selected by ``retro_selector``.
    mc_paths, optional
        Monte Carlo paths used to estimate the retrocausal final state.
    retro_selector, optional
        Selector responsible for choosing trajectories that satisfy
        ``envelope``.
    anacronism_steps, optional
        Number of positions to shift ``psi_vector`` backwards. ``None`` uses
        ``QUALIA_ANACRONISM_STEPS``.
    use_resonance, optional
        When ``True`` applies :func:`apply_resonance` before anacronism.
    resonance_strength, optional
        Strength passed to :func:`apply_resonance`.
    use_folding, optional
        When ``True`` applies :func:`apply_folding` before anacronism.
    use_acceleration, optional
        When ``True`` and ``cupy`` or ``qiskit`` are available, performs matrix
        operations on GPU or QPU.

    Returns
    -------
    Dict[str, float | list]
        Dictionary with ``strength``, ``E``, ``H``, ``emitted``, ``psi_vector``
        and an array ``subfields`` contendo métricas de cada subconjunto do
        vetor.
    """
    psi_in = make_tsvf_state(prices_in, vector_size)
    if envelope is not None and mc_paths is not None:
        if retro_selector is None:
            retro_selector = RetroSelector()
        selected = retro_selector.select(mc_paths, envelope)
        avg_path = selected.mean(axis=0)
        psi_fin = make_tsvf_state(avg_path, vector_size)
    else:
        psi_fin = make_tsvf_state(prices_fin, vector_size)

    xp = np
    qiskit_sv = None
    if use_acceleration:
        if CUPY_AVAILABLE:
            import cupy as cp

            xp = cp
            psi_in = cp.asarray(psi_in)
            psi_fin = cp.asarray(psi_fin)
        elif QISKIT_AVAILABLE:
            from qiskit.quantum_info import Statevector

            qiskit_sv = True
        else:
            logger.debug("Acceleration requested but no library available")

    psi_fwd = (1 - alpha) * psi_in + alpha * psi_fin
    psi_bwd = psi_fwd + gamma * (psi_fin - psi_fwd)

    if qiskit_sv:
        from qiskit.quantum_info import Statevector

        psi_bwd_sv = Statevector(np.asarray(psi_bwd))
        norm_psi_bwd = np.linalg.norm(psi_bwd_sv.data)
        psi_final = (
            psi_bwd_sv.data / norm_psi_bwd
            if norm_psi_bwd != 0
            else np.zeros_like(psi_bwd_sv.data)
        )
    else:
        norm_psi_bwd = xp.linalg.norm(psi_bwd)
        psi_final = (
            psi_bwd / norm_psi_bwd if norm_psi_bwd != 0 else xp.zeros_like(psi_bwd)
        )

    if CUPY_AVAILABLE and use_acceleration and not qiskit_sv:
        psi_final_cpu = np.asarray(xp.asnumpy(psi_final))
    else:
        psi_final_cpu = np.asarray(psi_final)

    # YAA: Logging detalhado para diagnóstico de força zero
    logger.debug(
        f"🔬 TSVF Debug - psi_final_cpu: len={len(psi_final_cpu)}, k_components={k_components}"
    )
    logger.debug(
        f"🔬 TSVF Debug - psi_final_cpu values: {psi_final_cpu[:10]}"
    )  # Primeiros 10 valores
    logger.debug(
        f"🔬 TSVF Debug - psi_final_cpu abs mean: {np.mean(np.abs(psi_final_cpu)) if len(psi_final_cpu) > 0 else 'EMPTY'}"
    )

    if (
        len(psi_final_cpu) > 0
        and k_components > 0
        and len(psi_final_cpu) >= k_components
    ):
        abs_components = np.abs(psi_final_cpu)
        sorted_components = np.sort(abs_components)
        top_k = sorted_components[-k_components:]
        strength = np.mean(top_k)
        logger.debug(
            f"🔬 TSVF Debug - Path 1: top_{k_components} components: {top_k}, strength={strength}"
        )
    elif len(psi_final_cpu) > 0 and k_components > 0:
        strength = np.mean(np.abs(psi_final_cpu))
        logger.debug(
            f"🔬 TSVF Debug - Path 2: mean abs all components, strength={strength}"
        )
    else:
        strength = 0.0
        logger.debug("🔬 TSVF Debug - Path 3: strength=0.0 (empty or k_components=0)")

    var_psi_in = np.var(np.asarray(psi_in))
    var_psi_in_safe = var_psi_in if var_psi_in > 1e-9 else 1.0
    E_metric = 1.0 - (np.var(psi_final_cpu) / var_psi_in_safe)

    probs = np.abs(psi_final_cpu) ** 2
    sum_probs = np.sum(probs)
    H_metric = 0.0
    if sum_probs > 1e-9:
        normalized_probs = probs / sum_probs
        if len(normalized_probs) > 1:
            H_metric = entropy(normalized_probs) / np.log(len(normalized_probs))
        else:
            H_metric = 0.0

    emitted_signal = cE * E_metric + cH * H_metric

    sub_metrics: list[dict[str, float]] = []
    if num_subfields > 1:
        segments = np.array_split(psi_final_cpu, num_subfields)
        for seg in segments:
            if len(seg) == 0:
                sub_strength = 0.0
            elif len(seg) >= k_components and k_components > 0:
                sub_strength = float(np.mean(np.sort(np.abs(seg))[-k_components:]))
            elif k_components > 0:
                sub_strength = float(np.mean(np.abs(seg)))
            else:
                sub_strength = 0.0

            sub_E = 1.0 - (np.var(seg) / var_psi_in_safe)
            seg_probs = np.abs(seg) ** 2
            seg_sum = np.sum(seg_probs)
            if seg_sum > 1e-9 and len(seg_probs) > 1:
                sub_H = float(entropy(seg_probs / seg_sum) / np.log(len(seg_probs)))
            else:
                sub_H = 0.0
            sub_metrics.append({"strength": sub_strength, "E": sub_E, "H": sub_H})

    if use_resonance:
        psi_final_cpu = apply_resonance(psi_final_cpu, strength=resonance_strength)
    if use_folding:
        psi_final_cpu = apply_folding(psi_final_cpu)

    psi_final_cpu = apply_anacronism(np.asarray(psi_final_cpu), anacronism_steps)
    psi_final = psi_final_cpu

    if CUPY_AVAILABLE and use_acceleration and not qiskit_sv:
        psi_vector_out = xp.asnumpy(psi_final).tolist()
    else:
        psi_vector_out = np.asarray(psi_final).tolist()

    return {
        "strength": float(strength),
        "E": float(E_metric),
        "H": float(H_metric),
        "emitted": float(emitted_signal),
        "psi_vector": psi_vector_out,
        "subfields": sub_metrics,
    }


def compute_financial_otoc(
    returns: pd.Series | np.ndarray, delta: int, window: int
) -> float:
    """Compute a financial analogue of the OTOC.

    Parameters
    ----------
    returns
        Series or array of returns values.
    delta
        Shift applied when computing the OTOC.
    window
        Rolling window size used to average the pointwise OTOC.
    """
    if isinstance(returns, np.ndarray):
        returns = pd.Series(returns)

    if not isinstance(returns, pd.Series) or is_data_empty(returns):
        return 0.0

    if len(returns) < delta + 1:
        return 0.0

    r_shift = returns.shift(-delta)
    otoc_series_pointwise = (returns * r_shift) ** 2
    rolling_otoc = otoc_series_pointwise.rolling(
        window=window, min_periods=max(1, window // 2)
    ).mean()
    rolling_otoc_clean = rolling_otoc.dropna()
    if is_data_empty(rolling_otoc_clean):
        return 0.0
    from ...trading.data_utils import safe_iloc

    last_value = safe_iloc(rolling_otoc_clean, -1)
    return float(last_value) if last_value is not None else 0.0


class TSVFCalculator:
    """Utility class to compute TSVF metrics and related quantities."""

    logger = get_logger(__name__ + ".TSVFCalculator")

    def __init__(self, *, use_acceleration: bool = False) -> None:
        """Create calculator.

        Parameters
        ----------
        use_acceleration
            If ``True``, attempt to use GPU or QPU acceleration when available.
        """
        self.use_acceleration = use_acceleration

    def make_state(self, price_segment: np.ndarray, vector_size: int) -> np.ndarray:
        """Return a normalized TSVF state vector."""

        return make_tsvf_state(price_segment, vector_size)

    def compute_outputs(
        self,
        prices_in: np.ndarray,
        prices_fin: np.ndarray,
        vector_size: int,
        alpha: float,
        gamma: float,
        cE: float,
        cH: float,
        k_components: int = 5,
        envelope: IntentionEnvelope | None = None,
        mc_paths: np.ndarray | None = None,
        retro_selector: RetroSelector | None = None,
        anacronism_steps: int | None = None,
        use_resonance: bool = False,
        resonance_strength: float | None = None,
        use_folding: bool = False,
        use_quantum_paths: bool = False,
        *,
        start_ts: int | None = None,
        end_ts: int | None = None,
        psi_cache: Dict[tuple[int, int, int], Dict[str, float | list]] | None = None,
        num_subfields: int = 4,
    ) -> Dict[str, float | list]:
        """Calculate TSVF metrics for the given price window.

        Parameters
        ----------
        use_resonance
            When ``True`` applies :func:`apply_resonance` to the TSVF vector.
        resonance_strength
            Strength passed to :func:`apply_resonance`.
        use_folding
            When ``True`` applies :func:`apply_folding` to the TSVF vector.
        use_quantum_paths
            When ``True`` and ``qiskit`` is available, Monte Carlo paths are
            generated via :func:`simulate_quantum_paths` if not provided.
        num_subfields
            Number of segments used to produzir métricas locais do vetor psi.
        """

        if start_ts is not None and end_ts is not None and psi_cache is not None:
            key = (start_ts, end_ts, vector_size)
            cached = psi_cache.get(key)
            if cached is not None:
                self.logger.debug("Reusing TSVF cache for window %s", key)
                return cached

        if use_quantum_paths and mc_paths is None:
            try:
                from ...utils.simulator import simulate_quantum_paths

                horizon = len(prices_fin)
                mc_paths = simulate_quantum_paths(
                    mu=0.0,
                    sigma=(
                        float(np.std(np.diff(prices_fin) / prices_fin[:-1]))
                        if len(prices_fin) > 1
                        else 1.0
                    ),
                    horizon=horizon,
                    num_paths=100,
                )
            except Exception as exc:  # pragma: no cover - best effort
                self.logger.debug("Quantum path generation failed: %s", exc)

        result = calculate_tsvf_outputs(
            prices_in,
            prices_fin,
            vector_size,
            alpha,
            gamma,
            cE,
            cH,
            k_components,
            envelope,
            mc_paths,
            retro_selector,
            anacronism_steps,
            use_resonance=use_resonance,
            resonance_strength=resonance_strength,
            use_folding=use_folding,
            use_acceleration=self.use_acceleration,
            num_subfields=num_subfields,
        )

        if start_ts is not None and end_ts is not None and psi_cache is not None:
            psi_cache[(start_ts, end_ts, vector_size)] = result

        return result

    def compute_otoc(
        self, returns: pd.Series | np.ndarray, delta: int, window: int
    ) -> float:
        """Compute a financial analogue of the OTOC."""

        return compute_financial_otoc(returns, delta, window)
