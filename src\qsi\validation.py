import numpy as np
from functools import wraps
from typing import Any, Callable


def validate_quantum_state(func: Callable) -> Callable:
    """Decorador de validação e normalização automática de estados quânticos.

    Garante que vetores de estado estejam normalizados e com dimensões
    compatíveis com ``self.dimensions``. Suporta estados únicos ou em lote.

    YAA CORREÇÃO: Normaliza automaticamente estados não normalizados
    em vez de rejeitá-los, mantendo a integridade quântica do sistema.
    """

    @wraps(func)
    def wrapper(self: Any, state: np.ndarray, *args, **kwargs) -> Any:
        state = np.asarray(state, dtype=np.complex128)

        if state.ndim == 1:
            states = state[None, :]
            single_state = True
        else:
            states = state
            single_state = False

        # YAA CORREÇÃO: Normalização automática em vez de rejeição
        norms = np.sqrt(np.sum(np.abs(states) ** 2, axis=1))

        # Verificar se algum estado tem norma zero ou muito pequena
        zero_norm_mask = norms < 1e-12
        if np.any(zero_norm_mask):
            # Substituir estados com norma zero por estado base |0⟩
            for i in np.where(zero_norm_mask)[0]:
                states[i] = 0.0
                states[i, 0] = 1.0
            norms = np.sqrt(np.sum(np.abs(states) ** 2, axis=1))

        # Normalizar todos os estados
        states = states / norms[:, np.newaxis]

        # Verificação final da normalização
        final_norms = np.sqrt(np.sum(np.abs(states) ** 2, axis=1))
        if not np.allclose(final_norms, 1.0, atol=1e-10):
            raise ValueError("ERRO CRÍTICO: Falha na normalização automática de estados quânticos.")

        # Validar dimensões
        if states.shape[-1] != self.dimensions:
            raise ValueError(
                f"Dimensões de estado inválidas. Esperado: {self.dimensions}, Recebido: {states.shape[-1]}"
            )

        # Retornar ao formato original se era um estado único
        if single_state:
            states = states[0]

        return func(self, states, *args, **kwargs)

    return wrapper
