"""Comprehensive memory system used throughout QUALIA."""

from __future__ import annotations

import asyncio
import json
from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional, Tuple
import time
from functools import lru_cache
from contextlib import nullcontext

import numpy as np

try:
    from datadog import DogStatsd
except Exception:  # pragma: no cover - datadog optional

    class DogStatsd:  # type: ignore[misc]
        def timer(self, *args: Any, **kwargs: Any):
            return nullcontext()

        def increment(self, *args: Any, **kwargs: Any) -> None:
            pass


from datetime import datetime, timezone
from collections import deque
import hashlib
import hmac
import base64
import os

from ..config import settings

from ..config.settings import get_env, stm_metrics_enabled

from ..utils.logger import get_logger
from typing import TYPE_CHECKING

from .holographic_memory import HolographicMemory
from .quantum_pattern_memory import QuantumPatternMemory
from ..config.settings import qpm_memory_file
from ..common_types import QuantumSignaturePacket
from ..risk.manager import create_risk_manager

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from ..core.resonance import ResonanceState


@dataclass
class Experience:
    """Represents a trading experience"""

    id: str
    timestamp: datetime
    market_data: Dict
    signal: Dict
    trade_result: Dict
    outcome: str  # 'success', 'failure', 'neutral'
    profit_loss: float
    metadata: Dict


@dataclass
class Pattern:
    """Represents a learned pattern"""

    id: str
    pattern_type: str
    conditions: Dict
    outcomes: List[str]
    success_rate: float
    confidence: float
    usage_count: int
    last_used: datetime


class MemorySystem:
    """
    Advanced memory system for trading experiences and patterns

    Stores trading experiences, learns from outcomes, and provides
    pattern recognition and retrieval capabilities.
    """

    def __init__(self, config: Dict, *, statsd_client: Optional[DogStatsd] = None):
        self.config = config
        self.statsd = statsd_client or (DogStatsd() if stm_metrics_enabled else None)
        self.logger = get_logger(__name__)

        secret_key = get_env("QUALIA_SECRET_KEY", None, warn=False)
        if secret_key is None or len(secret_key.encode()) < 32:
            raise RuntimeError(
                "QUALIA_SECRET_KEY is required and must be at least 32 bytes long"
            )

        # Memory configuration
        self.max_experiences = config.get("max_experiences", 10000)
        self.decay_factor = config.get("decay_factor", 0.95)
        self.consolidation_threshold = config.get("consolidation_threshold", 0.8)
        self.persistence_interval = config.get("persistence_interval", 300)
        self.use_retro_operator = (
            settings.experimental_enabled and settings.retro_operator_enabled
        )
        self.holographic_memory: Optional[HolographicMemory] = None
        if settings.experimental_enabled:
            self.holographic_memory = HolographicMemory(
                max_items=config.get("holo_max_items", 10000),
                half_life=config.get("holo_half_life", 3600.0),
            )
        else:
            self.logger.info(
                "HolographicMemory desativada; defina QUALIA_EXPERIMENTAL=1 para habilitar"
            )
        default_rm = create_risk_manager(
            initial_capital=1000.0, risk_profile="conservative"
        )
        self.qpm = QuantumPatternMemory(
            enable_warmstart=False,
            risk_manager=default_rm,
            persistence_path=qpm_memory_file,
            auto_persist=True,
        )

        # Memory storage
        self.experiences: deque = deque(maxlen=self.max_experiences)
        self.patterns: Dict[str, Pattern] = {}
        self.consolidated_memories: List[Dict] = []

        # Performance tracking
        self.memory_stats = {
            "total_experiences": 0,
            "successful_experiences": 0,
            "failed_experiences": 0,
            "patterns_learned": 0,
            "patterns_used": 0,
            "consolidations": 0,
        }

        # Learning parameters
        self.learning_rate = 0.1
        self.pattern_threshold = 0.7
        self.min_pattern_occurrences = 3

        # Background tasks
        self.consolidation_task = None
        self.persistence_task = None

    async def initialize(self):
        """Initialize the memory system"""
        try:
            self.logger.info("Initializing memory system...")

            # Load existing memories if available
            await self._load_memories()

            # Start background tasks
            self.consolidation_task = asyncio.create_task(self._consolidation_loop())
            self.persistence_task = asyncio.create_task(self._persistence_loop())

            self.logger.info(
                f"Memory system initialized with {len(self.experiences)} experiences and {len(self.patterns)} patterns"
            )

        except Exception as exc:
            self.logger.exception("Failed to initialize memory system", exc_info=exc)
            raise

    async def close(self):
        """Close the memory system and save data"""
        try:
            # Cancel background tasks
            if self.consolidation_task:
                self.consolidation_task.cancel()
            if self.persistence_task:
                self.persistence_task.cancel()

            # Save final state
            await self._save_memories()

            self.logger.info("Memory system closed")

        except Exception as exc:
            self.logger.exception("Error closing memory system", exc_info=exc)
            raise

    async def store_experiences(self, experiences: List[Dict]):
        """Store new trading experiences"""
        try:
            for exp_data in experiences:
                experience = self._create_experience(exp_data)
                if experience:
                    self.experiences.append(experience)
                    self.memory_stats["total_experiences"] += 1

                    # Update outcome statistics
                    if experience.outcome == "success":
                        self.memory_stats["successful_experiences"] += 1
                    elif experience.outcome == "failure":
                        self.memory_stats["failed_experiences"] += 1

            # Trigger pattern learning
            await self._learn_patterns()

            self.logger.debug(f"Stored {len(experiences)} new experiences")

        except Exception as exc:
            self.logger.exception("Error storing experiences", exc_info=exc)
            raise

    def _create_experience(self, exp_data: Dict) -> Optional[Experience]:
        """Create an Experience object from raw data"""
        try:
            # Generate unique ID
            exp_id = self._generate_experience_id(exp_data)

            # Extract components
            market_data = exp_data.get("market_data", {})
            signal = exp_data.get("signal", {})
            trade_result = exp_data.get("trade_result", {})

            # Determine outcome
            outcome = self._determine_outcome(trade_result)

            # Calculate profit/loss
            profit_loss = self._calculate_profit_loss(trade_result)

            return Experience(
                id=exp_id,
                timestamp=exp_data.get("timestamp", datetime.now(timezone.utc)),
                market_data=market_data,
                signal=signal,
                trade_result=trade_result,
                outcome=outcome,
                profit_loss=profit_loss,
                metadata=exp_data.get("metadata", {}),
            )

        except (KeyError, TypeError, ValueError) as exc:
            self.logger.exception("Error creating experience", exc_info=exc)
            return None

    def _generate_experience_id(self, exp_data: Dict) -> str:
        """Generate unique ID for experience"""
        content = json.dumps(exp_data, sort_keys=True, default=str)
        return hashlib.md5(content.encode()).hexdigest()[:16]

    def _determine_outcome(self, trade_result: Dict) -> str:
        """Determine the outcome of a trade"""
        if not trade_result:
            return "neutral"

        success = trade_result.get("success", False)
        profit_loss = trade_result.get("profit_loss", 0.0)

        if success and profit_loss > 0:
            return "success"
        elif success and profit_loss < 0:
            return "failure"
        elif not success:
            return "failure"
        else:
            return "neutral"

    def _calculate_profit_loss(self, trade_result: Dict) -> float:
        """Calculate profit/loss from trade result"""
        if not trade_result:
            return 0.0

        # Try different ways to extract P&L
        if "profit_loss" in trade_result:
            return float(trade_result["profit_loss"])

        # Calculate from price and quantity
        price = trade_result.get("price", 0.0)
        quantity = trade_result.get("quantity", 0.0)

        if price > 0 and quantity > 0:
            # This is a simplified calculation
            # In reality, you'd need entry price to calculate actual P&L
            return price * quantity * 0.01  # Assume 1% profit/loss

        return 0.0

    async def _learn_patterns(self):
        """Learn patterns from recent experiences"""
        try:
            if len(self.experiences) < self.min_pattern_occurrences:
                return

            # Analyze recent experiences for patterns
            recent_experiences = list(self.experiences)[-100:]  # Last 100 experiences

            # Group experiences by market conditions
            market_patterns = self._extract_market_patterns(recent_experiences)

            # Learn signal patterns
            signal_patterns = self._extract_signal_patterns(recent_experiences)

            # Update pattern database
            for pattern_id, pattern_data in market_patterns.items():
                await self._update_pattern(pattern_id, pattern_data)

            for pattern_id, pattern_data in signal_patterns.items():
                await self._update_pattern(pattern_id, pattern_data)

        except Exception as exc:
            self.logger.exception("Error learning patterns", exc_info=exc)
            raise

    def _extract_market_patterns(
        self, experiences: List[Experience]
    ) -> Dict[str, Dict]:
        """Extract market condition patterns"""
        patterns = {}

        try:
            # Group by market conditions
            condition_groups = {}

            for exp in experiences:
                # Create a simplified market condition signature
                market_data = exp.market_data
                if "prices" in market_data and len(market_data["prices"]) > 0:
                    prices = market_data["prices"]

                    # Calculate basic market features
                    price_change = (
                        (prices[-1] - prices[0]) / prices[0] if prices[0] != 0 else 0
                    )
                    volatility = (
                        np.std(prices[-10:]) / np.mean(prices[-10:])
                        if len(prices) >= 10
                        else 0
                    )

                    # Create condition signature
                    condition_key = self._create_condition_signature(
                        {
                            "price_change": price_change,
                            "volatility": volatility,
                            "trend": (
                                "up"
                                if price_change > 0.01
                                else "down" if price_change < -0.01 else "flat"
                            ),
                        }
                    )

                    if condition_key not in condition_groups:
                        condition_groups[condition_key] = []
                    condition_groups[condition_key].append(exp)

            # Analyze each group for patterns
            for condition_key, group_experiences in condition_groups.items():
                if len(group_experiences) >= self.min_pattern_occurrences:
                    pattern_data = self._analyze_experience_group(group_experiences)
                    if pattern_data["success_rate"] > self.pattern_threshold:
                        patterns[f"market_{condition_key}"] = pattern_data

            return patterns

        except (KeyError, TypeError, ValueError) as exc:
            self.logger.exception("Error extracting market patterns", exc_info=exc)
            return patterns

    def _extract_signal_patterns(
        self, experiences: List[Experience]
    ) -> Dict[str, Dict]:
        """Extract signal-based patterns"""
        patterns = {}

        try:
            # Group by signal characteristics
            signal_groups = {}

            for exp in experiences:
                signal = exp.signal
                if signal:
                    # Create signal signature
                    signal_key = self._create_signal_signature(signal)

                    if signal_key not in signal_groups:
                        signal_groups[signal_key] = []
                    signal_groups[signal_key].append(exp)

            # Analyze each signal group
            for signal_key, group_experiences in signal_groups.items():
                if len(group_experiences) >= self.min_pattern_occurrences:
                    pattern_data = self._analyze_experience_group(group_experiences)
                    if pattern_data["success_rate"] > self.pattern_threshold:
                        patterns[f"signal_{signal_key}"] = pattern_data

            return patterns

        except (KeyError, TypeError, ValueError) as exc:
            self.logger.exception("Error extracting signal patterns", exc_info=exc)
            return patterns

    @staticmethod
    @lru_cache(maxsize=128)
    def _cached_condition_signature(items: Tuple[Tuple[str, Any], ...]) -> str:
        rounded = {}
        for key, value in items:
            if isinstance(value, (int, float)):
                rounded[key] = round(value, 2)
            else:
                rounded[key] = value
        return "_".join(f"{k}:{v}" for k, v in sorted(rounded.items()))

    def _create_condition_signature(self, conditions: Dict) -> str:
        """Create a signature string for market conditions"""
        try:
            key = tuple(sorted(conditions.items()))
            return self._cached_condition_signature(key)
        except (TypeError, ValueError) as exc:
            self.logger.warning(
                "Failed to create condition signature: %s", exc, exc_info=True
            )
            if self.statsd:
                self.statsd.increment("ms.condition_signature.error")
            return "unknown"

    @staticmethod
    @lru_cache(maxsize=128)
    def _cached_signal_signature(items: Tuple[Tuple[str, Any], ...]) -> str:
        signal_dict = dict(items)
        key_features = []
        if "action" in signal_dict:
            key_features.append(f"action:{signal_dict['action']}")
        if "confidence" in signal_dict:
            confidence_bucket = round(float(signal_dict["confidence"]), 1)
            key_features.append(f"conf:{confidence_bucket}")
        if "signal_type" in signal_dict:
            key_features.append(f"type:{signal_dict['signal_type']}")
        return "_".join(key_features) if key_features else "generic"

    def _create_signal_signature(self, signal: Dict) -> str:
        """Create a signature string for signal characteristics"""
        try:
            key = tuple(sorted(signal.items()))
            return self._cached_signal_signature(key)
        except (TypeError, ValueError) as exc:
            self.logger.warning(
                "Failed to create signal signature: %s", exc, exc_info=True
            )
            if self.statsd:
                self.statsd.increment("ms.signal_signature.error")
            return "unknown"

    def _analyze_experience_group(self, experiences: List[Experience]) -> Dict:
        """Analyze a group of experiences to extract pattern data"""
        try:
            total_experiences = len(experiences)
            pl_array = np.array([exp.profit_loss for exp in experiences], dtype=float)
            success_flags = np.array(
                [exp.outcome == "success" for exp in experiences], dtype=float
            )

            success_rate = float(success_flags.mean()) if total_experiences > 0 else 0.0

            avg_profit_loss = float(pl_array.mean()) if pl_array.size > 0 else 0.0

            conditions = {}
            outcomes = [exp.outcome for exp in experiences]

            return {
                "conditions": conditions,
                "outcomes": outcomes,
                "success_rate": success_rate,
                "confidence": min(
                    1.0, total_experiences / 10.0
                ),  # Confidence based on sample size
                "usage_count": 0,
                "avg_profit_loss": avg_profit_loss,
                "sample_size": total_experiences,
            }

        except (KeyError, TypeError, ValueError) as exc:
            self.logger.exception("Error analyzing experience group", exc_info=exc)
            return {
                "conditions": {},
                "outcomes": [],
                "success_rate": 0.0,
                "confidence": 0.0,
                "usage_count": 0,
                "avg_profit_loss": 0.0,
                "sample_size": 0,
            }

    async def _update_pattern(self, pattern_id: str, pattern_data: Dict):
        """Update or create a pattern"""
        try:
            if pattern_id in self.patterns:
                # Update existing pattern
                existing_pattern = self.patterns[pattern_id]

                # Update with exponential moving average
                alpha = self.learning_rate
                existing_pattern.success_rate = (
                    1 - alpha
                ) * existing_pattern.success_rate + alpha * pattern_data["success_rate"]
                existing_pattern.confidence = (
                    1 - alpha
                ) * existing_pattern.confidence + alpha * pattern_data["confidence"]

                # Update conditions (simplified merge)
                existing_pattern.conditions.update(pattern_data["conditions"])
                existing_pattern.outcomes.extend(pattern_data["outcomes"])

                # Keep only recent outcomes
                if len(existing_pattern.outcomes) > 100:
                    existing_pattern.outcomes = existing_pattern.outcomes[-100:]

            else:
                # Create new pattern
                self.patterns[pattern_id] = Pattern(
                    id=pattern_id,
                    pattern_type="learned",
                    conditions=pattern_data["conditions"],
                    outcomes=pattern_data["outcomes"],
                    success_rate=pattern_data["success_rate"],
                    confidence=pattern_data["confidence"],
                    usage_count=0,
                    last_used=datetime.now(timezone.utc),
                )

                self.memory_stats["patterns_learned"] += 1

            vector = np.array(
                [
                    pattern_data.get("success_rate", 0.0),
                    pattern_data.get("avg_profit_loss", 0.0),
                    pattern_data.get("confidence", 0.0),
                ]
            )
            self.store_market_pattern(vector, {"pattern_id": pattern_id})

        except Exception as exc:
            self.logger.exception("Error updating pattern %s", pattern_id, exc_info=exc)
            raise

    def store_market_pattern(
        self,
        vector: List[float] | np.ndarray,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Store ``vector`` and ``metadata`` in the internal memory backends."""
        vec = np.asarray(vector, dtype=float)
        if self.use_retro_operator:
            from ..core.retrocausality import apply_retrocausality

            vec = apply_retrocausality(
                vec,
                None,
                gamma=settings.retro_operator_gamma,
            )
        packet = QuantumSignaturePacket(vector=list(vec), metrics={})
        self.qpm.store_pattern(
            packet,
            {},
            {},
            metadata,
            extra_metadata=metadata,
        )
        if self.holographic_memory:
            self.holographic_memory.store(vec, metadata or {})

    def query_market_patterns(
        self, vector: List[float] | np.ndarray, top_n: int = 5
    ) -> List[Dict[str, Any]]:
        """Query patterns similar to ``vector``.

        The method delegates the retrieval to :class:`QuantumPatternMemory` and
        returns results from :class:`HolographicMemory` when disponível.
        """
        query_vec = np.asarray(vector, dtype=float)
        if self.use_retro_operator:
            from ..core.retrocausality import apply_retrocausality

            query_vec = apply_retrocausality(
                query_vec, None, gamma=settings.retro_operator_gamma
            )
        packet = QuantumSignaturePacket(vector=list(query_vec), metrics={})
        qpm_results = self.qpm.retrieve_similar_patterns(packet, top_n=top_n)
        if self.holographic_memory:
            return self.holographic_memory.query(query_vec, top_n=top_n)
        now = time.time()
        results = []
        for entry in qpm_results:
            ts = entry.get("timestamp", now)
            results.append(
                {
                    "similarity": entry.get("similarity_score", 0.0),
                    "metadata": entry.get("decision_context", {}),
                    "age": now - ts,
                }
            )
        return results

    def store_resonance_state(
        self, state: ResonanceState, metadata: Optional[Dict[str, Any]] | None = None
    ) -> None:
        """Store a resonance state in :class:`HolographicMemory`.

        Parameters
        ----------
        state : ResonanceState
            Resonance analysis state to persist.
        metadata : dict, optional
            Additional metadata associated with the pattern.
        """

        vector = self._resonance_to_vector(state)
        meta = metadata or {"pattern_type": "resonance"}
        self.store_market_pattern(vector, meta)

    def query_resonance_patterns(
        self, state: ResonanceState, top_n: int = 5
    ) -> List[Dict[str, Any]]:
        """Query patterns similar to the given resonance ``state``."""

        vector = self._resonance_to_vector(state)
        return self.query_market_patterns(vector, top_n=top_n)

    @staticmethod
    def _resonance_to_vector(state: ResonanceState) -> np.ndarray:
        """Convert :class:`ResonanceState` into a fixed-length vector."""

        amplitudes = state.amplitudes if len(state.amplitudes) > 0 else np.array([0.0])
        return np.array(
            [
                state.resonance_strength,
                float(len(state.harmonics)),
                float(np.mean(amplitudes)),
                float(np.std(amplitudes)),
            ]
        )

    async def retrieve_similar_experiences(
        self, current_conditions: Dict, limit: int = 10
    ) -> List[Experience]:
        """Retrieve experiences similar to current conditions"""
        try:
            similar_experiences = []

            for experience in self.experiences:
                similarity = self._calculate_similarity(current_conditions, experience)
                if similarity > 0.5:  # Similarity threshold
                    similar_experiences.append((experience, similarity))

            # Sort by similarity and return top results
            similar_experiences.sort(key=lambda x: x[1], reverse=True)
            return [exp for exp, _ in similar_experiences[:limit]]

        except Exception as exc:
            self.logger.exception("Error retrieving similar experiences", exc_info=exc)
            raise

    def _calculate_similarity(self, conditions: Dict, experience: Experience) -> float:
        """Calculate similarity between conditions and experience"""
        try:
            # This is a simplified similarity calculation
            # In practice, you might use more sophisticated methods

            market_data = experience.market_data
            similarity_score = 0.0
            comparison_count = 0

            prices = np.asarray(market_data.get("prices", []), dtype=float)

            if prices.size > 0 and "current_price" in conditions:
                price_similarity = (
                    1.0
                    - abs(prices[-1] - conditions["current_price"])
                    / conditions["current_price"]
                )
                similarity_score += max(0.0, price_similarity)
                comparison_count += 1

            if prices.size >= 10 and "volatility" in conditions:
                market_volatility = float(np.std(prices[-10:]))
                vol_similarity = 1.0 - abs(
                    market_volatility - conditions["volatility"]
                ) / max(market_volatility, conditions["volatility"], 1e-10)
                similarity_score += max(0.0, vol_similarity)
                comparison_count += 1

            return similarity_score / comparison_count if comparison_count > 0 else 0.0

        except (KeyError, TypeError, ValueError):
            return 0.0

    async def get_pattern_recommendations(self, current_conditions: Dict) -> List[Dict]:
        """Get pattern-based recommendations for current conditions"""
        try:
            recommendations = []

            for pattern_id, pattern in self.patterns.items():
                # Check if pattern conditions match current conditions
                match_score = self._match_pattern_conditions(
                    pattern.conditions, current_conditions
                )

                if match_score > 0.6:  # Matching threshold
                    recommendation = {
                        "pattern_id": pattern_id,
                        "pattern_type": pattern.pattern_type,
                        "success_rate": pattern.success_rate,
                        "confidence": pattern.confidence,
                        "match_score": match_score,
                        "usage_count": pattern.usage_count,
                        "recommendation_strength": pattern.success_rate
                        * pattern.confidence
                        * match_score,
                    }
                    recommendations.append(recommendation)

                    # Update pattern usage
                    pattern.usage_count += 1
                    pattern.last_used = datetime.now(timezone.utc)
                    self.memory_stats["patterns_used"] += 1

            # Sort by recommendation strength
            recommendations.sort(
                key=lambda x: x["recommendation_strength"], reverse=True
            )

            return recommendations[:5]  # Return top 5 recommendations

        except Exception as exc:
            self.logger.exception("Error getting pattern recommendations", exc_info=exc)
            raise

    def _match_pattern_conditions(
        self, pattern_conditions: Dict, current_conditions: Dict
    ) -> float:
        """Calculate how well pattern conditions match current conditions"""
        try:
            if not pattern_conditions:
                return 0.0

            match_score = 0.0
            total_conditions = len(pattern_conditions)

            for condition_key, pattern_value in pattern_conditions.items():
                if condition_key in current_conditions:
                    current_value = current_conditions[condition_key]

                    # Calculate condition match based on type
                    if isinstance(pattern_value, str) and isinstance(
                        current_value, str
                    ):
                        match_score += 1.0 if pattern_value == current_value else 0.0
                    elif isinstance(pattern_value, (int, float)) and isinstance(
                        current_value, (int, float)
                    ):
                        # Numerical similarity
                        max_val = max(abs(pattern_value), abs(current_value), 1e-10)
                        similarity = 1.0 - abs(pattern_value - current_value) / max_val
                        match_score += max(0.0, similarity)

            return match_score / total_conditions if total_conditions > 0 else 0.0

        except (KeyError, TypeError, ValueError):
            return 0.0

    async def _consolidation_loop(self):
        """Background task for memory consolidation"""
        try:
            while True:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._consolidate_memories()

        except asyncio.CancelledError:
            self.logger.info("Consolidation loop cancelled")
        except Exception as exc:
            self.logger.exception("Error in consolidation loop", exc_info=exc)

    async def _consolidate_memories(self):
        """Consolidate similar memories to reduce storage"""
        try:
            if (
                len(self.experiences) < 1000
            ):  # Only consolidate when we have many experiences
                return

            # Group similar experiences
            experience_groups = self._group_similar_experiences()

            # Consolidate each group
            for group in experience_groups:
                if len(group) >= 5:  # Only consolidate groups with multiple experiences
                    consolidated = self._consolidate_experience_group(group)
                    self.consolidated_memories.append(consolidated)

                    # Remove original experiences (keep most recent one)
                    group.sort(key=lambda x: x.timestamp, reverse=True)
                    for exp in group[1:]:  # Remove all but the most recent
                        try:
                            self.experiences.remove(exp)
                        except ValueError:
                            pass  # Experience already removed

            self.memory_stats["consolidations"] += 1
            self.logger.info(
                f"Consolidated memories. Total experiences: {len(self.experiences)}"
            )

        except Exception as exc:
            self.logger.exception("Error consolidating memories", exc_info=exc)
            raise

    def _group_similar_experiences(self) -> List[List[Experience]]:
        """Group similar experiences for consolidation"""
        groups = []
        try:
            exps = list(self.experiences)
            if not exps:
                return groups

            outcomes = np.array([exp.outcome for exp in exps], dtype=object)
            profits = np.array([exp.profit_loss for exp in exps], dtype=float)
            actions = np.array(
                [exp.signal.get("action", "") if exp.signal else "" for exp in exps],
                dtype=object,
            )

            outcome_match = outcomes[:, None] == outcomes
            sign_match = profits[:, None] * profits >= 0
            diff = np.abs(profits[:, None] - profits)
            denom = np.abs(profits[:, None]) + np.abs(profits) + 1e-10
            profit_sim = np.where(sign_match, 1.0 - diff / denom, 0.0)
            action_match = actions[:, None] == actions

            sim_matrix = (
                outcome_match.astype(float) + profit_sim + action_match.astype(float)
            ) / 3.0

            used = np.zeros(len(exps), dtype=bool)
            for i, exp in enumerate(exps):
                if used[i]:
                    continue
                idx = np.where(
                    (sim_matrix[i] > self.consolidation_threshold) & (~used)
                )[0]
                if idx.size > 1:
                    groups.append([exps[j] for j in idx])
                    used[idx] = True
                else:
                    used[i] = True

            return groups

        except Exception as exc:
            self.logger.exception("Error grouping similar experiences", exc_info=exc)
            raise

    def _calculate_experience_similarity(
        self, exp1: Experience, exp2: Experience
    ) -> float:
        """Calculate similarity between two experiences"""
        try:
            factors = np.array(
                [
                    1.0 if exp1.outcome == exp2.outcome else 0.0,
                    (
                        (
                            1.0
                            - abs(exp1.profit_loss - exp2.profit_loss)
                            / (abs(exp1.profit_loss) + abs(exp2.profit_loss) + 1e-10)
                        )
                        if exp1.profit_loss * exp2.profit_loss >= 0
                        else 0.0
                    ),
                    (
                        1.0
                        if (exp1.signal.get("action", "") if exp1.signal else "")
                        == (exp2.signal.get("action", "") if exp2.signal else "")
                        else 0.0
                    ),
                ],
                dtype=float,
            )

            return float(factors.mean())

        except (KeyError, TypeError, ValueError):
            return 0.0

    def _consolidate_experience_group(self, group: List[Experience]) -> Dict:
        """Consolidate a group of experiences into a summary"""
        try:
            return {
                "id": f"consolidated_{datetime.now(timezone.utc).timestamp()}",
                "experience_count": len(group),
                "outcomes": [exp.outcome for exp in group],
                "avg_profit_loss": np.mean([exp.profit_loss for exp in group]),
                "success_rate": sum(1 for exp in group if exp.outcome == "success")
                / len(group),
                "time_range": {
                    "start": min(exp.timestamp for exp in group),
                    "end": max(exp.timestamp for exp in group),
                },
                "representative_signal": group[
                    0
                ].signal,  # Use first signal as representative
                "consolidated_at": datetime.now(timezone.utc),
            }

        except Exception as exc:
            self.logger.exception("Error consolidating experience group", exc_info=exc)
            return {}

    async def _persistence_loop(self):
        """Background task for saving memories"""
        try:
            while True:
                await asyncio.sleep(self.persistence_interval)
                await self._save_memories()

        except asyncio.CancelledError:
            self.logger.info("Persistence loop cancelled")
        except Exception as exc:
            self.logger.exception("Error in persistence loop", exc_info=exc)
            raise

    @staticmethod
    def _compute_hmac(payload: Dict[str, Any]) -> str:
        """Return HMAC-SHA256 for ``payload`` using ``QUALIA_SECRET_KEY``."""

        key = get_env("QUALIA_SECRET_KEY")
        if not key:
            raise EnvironmentError("QUALIA_SECRET_KEY not set")
        if key == "dev_secret_key":
            raise EnvironmentError("QUALIA_SECRET_KEY must not be 'dev_secret_key'")
        decoded = base64.urlsafe_b64decode(key)
        serialized = json.dumps(payload, sort_keys=True, separators=(",", ":")).encode(
            "utf-8"
        )
        return hmac.new(decoded, serialized, hashlib.sha256).hexdigest()

    async def _save_memories(self):
        """Save memories to persistent storage"""
        try:
            # Convert experiences to serializable format
            experiences_data = []
            for exp in list(self.experiences)[-1000:]:  # Save last 1000 experiences
                exp_dict = asdict(exp)
                exp_dict["timestamp"] = exp.timestamp.isoformat()
                experiences_data.append(exp_dict)

            # Convert patterns to serializable format
            patterns_data = {}
            for pattern_id, pattern in self.patterns.items():
                pattern_dict = asdict(pattern)
                pattern_dict["last_used"] = pattern.last_used.isoformat()
                patterns_data[pattern_id] = pattern_dict

            # Save data
            memory_data = {
                "experiences": experiences_data,
                "patterns": patterns_data,
                "consolidated_memories": self.consolidated_memories,
                "memory_stats": self.memory_stats,
                "saved_at": datetime.now(timezone.utc).isoformat(),
            }

            hmac_value = self._compute_hmac(memory_data)
            memory_data["hmac"] = hmac_value

            # Save to file (in a real implementation, you might use a database)
            os.makedirs(os.path.dirname(settings.memory_file), exist_ok=True)

            with open(settings.memory_file, "w") as f:
                json.dump(memory_data, f, indent=2, default=str)

            self.logger.debug("Memories saved to persistent storage")

        except Exception as exc:
            self.logger.exception("Error saving memories", exc_info=exc)
            raise

    async def _load_memories(self):
        """Load memories from persistent storage"""
        try:
            if not os.path.exists(settings.memory_file):
                self.logger.info("No existing memory file found")
                return

            with open(settings.memory_file, "r") as f:
                memory_data = json.load(f)

            expected_hmac = self._compute_hmac(
                {k: v for k, v in memory_data.items() if k != "hmac"}
            )
            if memory_data.get("hmac") != expected_hmac:
                self.logger.error("Invalid snapshot HMAC; skipping load")
                return

            # Load experiences
            for exp_dict in memory_data.get("experiences", []):
                try:
                    exp_dict["timestamp"] = datetime.fromisoformat(
                        exp_dict["timestamp"]
                    )
                    experience = Experience(**exp_dict)
                    self.experiences.append(experience)
                except (ValueError, TypeError, KeyError) as e:
                    self.logger.warning(f"Failed to load experience: {e}")

            # Load patterns
            for pattern_id, pattern_dict in memory_data.get("patterns", {}).items():
                try:
                    pattern_dict["last_used"] = datetime.fromisoformat(
                        pattern_dict["last_used"]
                    )
                    pattern = Pattern(**pattern_dict)
                    self.patterns[pattern_id] = pattern
                except (ValueError, TypeError, KeyError) as e:
                    self.logger.warning(f"Failed to load pattern {pattern_id}: {e}")

            # Load consolidated memories
            self.consolidated_memories = memory_data.get("consolidated_memories", [])

            # Load stats
            self.memory_stats.update(memory_data.get("memory_stats", {}))

            self.logger.info(
                f"Loaded {len(self.experiences)} experiences and {len(self.patterns)} patterns from storage"
            )

        except Exception as exc:
            self.logger.exception("Error loading memories", exc_info=exc)
            raise

    def get_memory_stats(self) -> Dict:
        """Get current memory system statistics"""
        return {
            "current_experiences": len(self.experiences),
            "total_patterns": len(self.patterns),
            "consolidated_memories": len(self.consolidated_memories),
            "memory_stats": self.memory_stats.copy(),
            "memory_usage": {
                "experiences_capacity": self.max_experiences,
                "experiences_used": len(self.experiences),
                "experiences_utilization": len(self.experiences) / self.max_experiences,
            },
        }

    def get_top_patterns(self, limit: int = 10) -> List[Dict]:
        """Get top performing patterns"""
        try:
            pattern_list = []

            for pattern_id, pattern in self.patterns.items():
                pattern_dict = asdict(pattern)
                pattern_dict["last_used"] = pattern.last_used.isoformat()
                pattern_dict["performance_score"] = (
                    pattern.success_rate * pattern.confidence
                )
                pattern_list.append(pattern_dict)

            # Sort by performance score
            pattern_list.sort(key=lambda x: x["performance_score"], reverse=True)

            return pattern_list[:limit]

        except (KeyError, TypeError, ValueError) as exc:
            self.logger.exception("Error getting top patterns", exc_info=exc)
            return []
