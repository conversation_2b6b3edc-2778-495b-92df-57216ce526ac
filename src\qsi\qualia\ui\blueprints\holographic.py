from __future__ import annotations

from flask import Blueprint, render_template

from ..initialize import get_qualia_state

holographic_bp = Blueprint("holographic", __name__)


@holographic_bp.route("/holographic")
def holographic_view() -> str:
    """Render the holographic visualization page."""
    state = get_qualia_state()
    use_webgpu = False
    if state is not None:
        with state.locked():
            use_webgpu = getattr(state, "use_webgpu", False)
    return render_template("holographic.html", use_webgpu=use_webgpu)
