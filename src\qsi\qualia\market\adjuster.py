"""Mechanism for applying bounded adjustments to risk parameters."""

from __future__ import annotations

from dataclasses import dataclass
import math


@dataclass
class ValueAdjuster:
    """Apply adjustments with optional manual override.

    The adjustment magnitude (``delta``) is limited by ``beta``. When
    ``uncertainty_index`` exceeds 0.8, expanding beyond ``beta`` requires
    ``require_manual_approval=True``.
    """

    beta: float = 1.0

    def apply(
        self,
        base_value: float,
        delta: float,
        *,
        uncertainty_index: float = 0.0,
        require_manual_approval: bool = False,
    ) -> float:
        """Return ``base_value`` adjusted by ``delta`` respecting constraints."""
        if math.fabs(delta) <= self.beta:
            return base_value + delta

        if uncertainty_index > 0.8:
            if not require_manual_approval:
                raise PermissionError(
                    "Manual approval required when uncertainty exceeds 0.8"
                )
            return base_value + delta

        if require_manual_approval:
            return base_value + delta

        clipped_delta = math.copysign(self.beta, delta)
        return base_value + clipped_delta
