"""
QUALIA Dimensional Arbitrage Engine

Sistema de arbitragem quântica multi-exchange que explora diferenças dimensionais
entre Kraken e KuCoin para maximizar oportunidades de lucro.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import pandas as pd
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class ArbitrageOpportunity:
    """Representa uma oportunidade de arbitragem identificada"""
    symbol: str
    exchange_buy: str
    exchange_sell: str
    price_buy: float
    price_sell: float
    spread_percentage: float
    volume_available: float
    estimated_profit: float
    confidence_score: float
    timestamp: datetime
    risk_score: float
    execution_window: float = 30.0  # Janela de execução em segundos
    
    def to_dict(self) -> Dict[str, Any]:
        """Converter para dicionário para logging/persistência"""
        return asdict(self)
    
    def is_expired(self, current_time: datetime) -> bool:
        """Verificar se a oportunidade expirou"""
        return (current_time - self.timestamp).total_seconds() > self.execution_window


class CircuitBreaker:
    """Circuit breaker para proteger against falhas consecutivas"""
    
    def __init__(self, failure_threshold: int = 5, timeout_seconds: int = 300):
        self.failure_threshold = failure_threshold
        self.timeout_seconds = timeout_seconds
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def can_execute(self) -> bool:
        """Verificar se pode executar operação"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if (datetime.now(timezone.utc) - self.last_failure_time).total_seconds() > self.timeout_seconds:
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Registrar sucesso"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """Registrar falha"""
        self.failure_count += 1
        self.last_failure_time = datetime.now(timezone.utc)
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker OPEN após {self.failure_count} falhas")


class DimensionalArbitrageEngine:
    """
    Motor principal de arbitragem dimensional QUALIA
    
    Monitora múltiplas exchanges simultaneamente, identifica oportunidades
    de arbitragem e executa trades coordenados baseados em análise quântica.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.dimensional_config = config.get("dimensional", {})
        
        # Configurações otimizadas
        self.min_spread_threshold = self.dimensional_config.get("min_spread_threshold", 0.001)
        self.max_latency_ms = self.dimensional_config.get("max_latency_ms", 1000)
        self.quantum_coherence_threshold = self.dimensional_config.get("quantum_coherence_threshold", 0.55)
        self.position_size_pct = self.dimensional_config.get("position_size_pct", 0.002)
        self.max_daily_arbitrages = self.dimensional_config.get("max_daily_arbitrages", 25)
        
        # Rate limiting config
        rate_limits = self.dimensional_config.get("rate_limits", {})
        self.kraken_rps = rate_limits.get("kraken_requests_per_second", 1)
        self.kucoin_rps = rate_limits.get("kucoin_requests_per_second", 8)
        
        # Circuit breakers por exchange
        cb_threshold = rate_limits.get("circuit_breaker_threshold", 5)
        cb_timeout = rate_limits.get("circuit_breaker_timeout", 300)
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
        # Monitoring config
        monitoring = self.dimensional_config.get("monitoring", {})
        self.scan_interval = monitoring.get("opportunity_scan_interval", 3.0)
        self.max_opportunities = monitoring.get("max_opportunities_cache", 50)
        self.cleanup_threshold = monitoring.get("cleanup_old_opportunities", 300)
        
        # Estado do engine
        self.active_opportunities: deque = deque(maxlen=self.max_opportunities)
        self.executed_arbitrages_today = 0
        self.total_profit = 0.0
        self.running = False
        self.daily_reset_time = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Price monitoring otimizado
        self.price_cache: Dict[str, Dict[str, Tuple[float, datetime]]] = {}
        self.latency_monitor: Dict[str, deque] = {}
        
        # Rate limiting per exchange
        self.rate_limiters: Dict[str, asyncio.Semaphore] = {}
        self.last_requests: Dict[str, deque] = {}
        
        logger.info(
            f"🌌 Dimensional Arbitrage Engine inicializado: "
            f"threshold={self.min_spread_threshold*100:.1f}%, "
            f"max_daily={self.max_daily_arbitrages}, "
            f"position_size={self.position_size_pct*100:.2f}%"
        )
    
    def _initialize_circuit_breakers(self, exchanges: Dict[str, Any]):
        """Inicializar circuit breakers para cada exchange"""
        rate_limits = self.dimensional_config.get("rate_limits", {})
        cb_threshold = rate_limits.get("circuit_breaker_threshold", 5)
        cb_timeout = rate_limits.get("circuit_breaker_timeout", 300)
        
        for exchange_name in exchanges.keys():
            self.circuit_breakers[exchange_name] = CircuitBreaker(cb_threshold, cb_timeout)
            
            # Configurar rate limiters
            rps = self.kucoin_rps if exchange_name == "kucoin" else self.kraken_rps
            self.rate_limiters[exchange_name] = asyncio.Semaphore(rps)
            self.last_requests[exchange_name] = deque(maxlen=rps * 2)  # Buffer 2x
            self.latency_monitor[exchange_name] = deque(maxlen=100)  # Últimas 100 medições
    
    async def _rate_limited_call(self, exchange_name: str, coro) -> Any:
        """Executar chamada com rate limiting e circuit breaker"""
        # Verificar circuit breaker
        if not self.circuit_breakers[exchange_name].can_execute():
            raise Exception(f"Circuit breaker OPEN para {exchange_name}")
        
        # Rate limiting
        current_time = asyncio.get_event_loop().time()
        
        # Limpar requisições antigas
        while (self.last_requests[exchange_name] and 
               current_time - self.last_requests[exchange_name][0] > 1.0):
            self.last_requests[exchange_name].popleft()
        
        # Verificar limite
        rps = self.kucoin_rps if exchange_name == "kucoin" else self.kraken_rps
        if len(self.last_requests[exchange_name]) >= rps:
            sleep_time = 1.0 - (current_time - self.last_requests[exchange_name][0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        # Executar com semáforo
        try:
            async with self.rate_limiters[exchange_name]:
                self.last_requests[exchange_name].append(current_time)
                result = await coro
                self.circuit_breakers[exchange_name].record_success()
                return result
        except Exception as e:
            self.circuit_breakers[exchange_name].record_failure()
            raise
    
    async def start_monitoring(self, exchanges: Dict[str, Any], symbols: List[str]):
        """Inicia o monitoramento contínuo de oportunidades de arbitragem"""
        self.running = True
        self._initialize_circuit_breakers(exchanges)
        
        logger.info(
            f"🔍 Iniciando monitoramento dimensional: "
            f"{len(exchanges)} exchanges, {len(symbols)} símbolos"
        )
        
        try:
            while self.running:
                cycle_start = datetime.now(timezone.utc)
                
                # Verificar reset diário
                await self._check_daily_reset()
                
                # Limpar oportunidades expiradas
                self._cleanup_expired_opportunities()
                
                # Coletar preços de todas as exchanges
                price_data = await self._collect_price_data(exchanges, symbols)
                
                # Identificar oportunidades
                new_opportunities = self._identify_arbitrage_opportunities(price_data)
                
                # Filtrar por qualidade quântica
                filtered_opportunities = await self._filter_quantum_opportunities(new_opportunities)
                
                # Atualizar cache de oportunidades
                for opp in filtered_opportunities:
                    self.active_opportunities.append(opp)
                
                # Executar arbitragens aprovadas (apenas as melhores)
                best_opportunities = sorted(
                    self.active_opportunities, 
                    key=lambda x: x.confidence_score * x.spread_percentage, 
                    reverse=True
                )[:3]  # Máximo 3 simultâneas
                
                if best_opportunities:
                    await self._execute_arbitrage_batch(exchanges, best_opportunities)
                
                # Atualizar estatísticas
                self._update_performance_metrics()
                
                # Calcular tempo de ciclo e aguardar
                cycle_time = (datetime.now(timezone.utc) - cycle_start).total_seconds()
                sleep_time = max(0.1, self.scan_interval - cycle_time)
                
                if cycle_time > self.scan_interval * 1.5:
                    logger.warning(f"Ciclo de arbitragem lento: {cycle_time:.2f}s")
                
                await asyncio.sleep(sleep_time)
                
        except asyncio.CancelledError:
            logger.info("🛑 Monitoramento dimensional cancelado")
        except Exception as e:
            logger.error(f"❌ Erro no monitoramento dimensional: {e}")
        finally:
            self.running = False
    
    async def _check_daily_reset(self):
        """Verificar se precisa resetar contadores diários"""
        current_time = datetime.now(timezone.utc)
        if current_time.date() > self.daily_reset_time.date():
            self.executed_arbitrages_today = 0
            self.daily_reset_time = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            logger.info("🔄 Reset diário: contadores de arbitragem zerados")
    
    def _cleanup_expired_opportunities(self):
        """Limpar oportunidades expiradas"""
        current_time = datetime.now(timezone.utc)
        
        # Converter deque para lista, filtrar, e recriar deque
        valid_opportunities = [
            opp for opp in self.active_opportunities 
            if not opp.is_expired(current_time)
        ]
        
        if len(valid_opportunities) < len(self.active_opportunities):
            expired_count = len(self.active_opportunities) - len(valid_opportunities)
            logger.debug(f"🧹 Limpas {expired_count} oportunidades expiradas")
            
            self.active_opportunities.clear()
            self.active_opportunities.extend(valid_opportunities)
    
    async def _collect_price_data(self, exchanges: Dict[str, Any], symbols: List[str]) -> Dict[str, Dict[str, float]]:
        """Coleta preços de todas as exchanges simultaneamente com circuit breakers"""
        price_data = {}
        
        # Criar tasks para coleta paralela
        tasks = []
        for symbol in symbols:
            for exchange_name, exchange in exchanges.items():
                if self.circuit_breakers[exchange_name].can_execute():
                    task = self._fetch_price_with_timing(exchange_name, exchange, symbol)
                    tasks.append(task)
        
        if not tasks:
            logger.warning("⚠️ Nenhuma exchange disponível (circuit breakers)")
            return price_data
        
        # Executar todas as tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Processar resultados
        successful_fetches = 0
        for result in results:
            if isinstance(result, Exception):
                logger.debug(f"Erro na coleta de preços: {result}")
                continue
                
            exchange_name, symbol, price, latency = result
            if price is not None:
                if symbol not in price_data:
                    price_data[symbol] = {}
                price_data[symbol][exchange_name] = price
                
                # Atualizar cache e latência
                self._update_price_cache(symbol, exchange_name, price)
                self._update_latency_stats(exchange_name, latency)
                successful_fetches += 1
        
        logger.debug(f"📊 Preços coletados: {successful_fetches}/{len(tasks)} requisições bem-sucedidas")
        return price_data
    
    async def _fetch_price_with_timing(self, exchange_name: str, exchange: Any, symbol: str) -> Tuple[str, str, Optional[float], float]:
        """Busca preço com medição de latência e rate limiting"""
        start_time = datetime.now()
        
        try:
            # Usar get_ticker de forma consistente
            ticker_coro = exchange.get_ticker(symbol)
            ticker = await self._rate_limited_call(exchange_name, ticker_coro)
            
            price = ticker.get('last') or ticker.get('close') if ticker else None
            latency = (datetime.now() - start_time).total_seconds() * 1000  # ms
            
            return exchange_name, symbol, price, latency
            
        except Exception as e:
            latency = (datetime.now() - start_time).total_seconds() * 1000
            logger.debug(f"Erro ao buscar preço {symbol} em {exchange_name}: {e}")
            return exchange_name, symbol, None, latency
    
    def _identify_arbitrage_opportunities(self, price_data: Dict[str, Dict[str, float]]) -> List[ArbitrageOpportunity]:
        """Identifica oportunidades de arbitragem baseadas nos preços coletados"""
        opportunities = []
        
        for symbol, prices in price_data.items():
            exchanges = list(prices.keys())
            
            if len(exchanges) < 2:
                continue
            
            # Comparar todos os pares de exchanges
            for i, exchange_buy in enumerate(exchanges):
                for exchange_sell in exchanges[i+1:]:
                    price_buy = prices[exchange_buy]
                    price_sell = prices[exchange_sell]
                    
                    # Calcular spread em ambas as direções
                    for buy_ex, sell_ex, buy_price, sell_price in [
                        (exchange_buy, exchange_sell, price_buy, price_sell),
                        (exchange_sell, exchange_buy, price_sell, price_buy)
                    ]:
                        opp = self._calculate_arbitrage_opportunity(
                            symbol, buy_ex, sell_ex, buy_price, sell_price
                        )
                        if opp:
                            opportunities.append(opp)
        
        # Filtrar e ordenar oportunidades válidas
        valid_opportunities = [
            op for op in opportunities 
            if op and op.spread_percentage >= self.min_spread_threshold
        ]
        
        if valid_opportunities:
            logger.info(
                f"💎 {len(valid_opportunities)} oportunidades identificadas, "
                f"melhor spread: {max(op.spread_percentage for op in valid_opportunities)*100:.2f}%"
            )
        
        return valid_opportunities
    
    def _calculate_arbitrage_opportunity(self, symbol: str, exchange_buy: str, exchange_sell: str, 
                                       price_buy: float, price_sell: float) -> Optional[ArbitrageOpportunity]:
        """Calcula uma oportunidade específica de arbitragem"""
        if price_buy >= price_sell:
            return None
        
        spread_percentage = (price_sell - price_buy) / price_buy
        
        if spread_percentage < self.min_spread_threshold:
            return None
        
        # Estimar volume disponível e lucro
        estimated_volume = self._estimate_available_volume(symbol, exchange_buy, exchange_sell)
        estimated_profit = self._calculate_estimated_profit(
            price_buy, price_sell, estimated_volume, symbol
        )
        
        # Calcular score de confiança baseado em latência e circuit breakers
        confidence_score = self._calculate_confidence_score(exchange_buy, exchange_sell)
        
        # Calcular score de risco
        risk_score = self._calculate_risk_score(spread_percentage, confidence_score)
        
        return ArbitrageOpportunity(
            symbol=symbol,
            exchange_buy=exchange_buy,
            exchange_sell=exchange_sell,
            price_buy=price_buy,
            price_sell=price_sell,
            spread_percentage=spread_percentage,
            volume_available=estimated_volume,
            estimated_profit=estimated_profit,
            confidence_score=confidence_score,
            timestamp=datetime.now(timezone.utc),
            risk_score=risk_score,
            execution_window=30.0
        )
    
    async def _filter_quantum_opportunities(self, opportunities: List[ArbitrageOpportunity]) -> List[ArbitrageOpportunity]:
        """Filtra oportunidades usando análise quântica de coerência"""
        if not opportunities:
            return []
        
        filtered = []
        
        for opportunity in opportunities:
            # Calcular coerência quântica aprimorada
            quantum_coherence = await self._calculate_quantum_coherence(opportunity)
            
            # Verificar se passa nos filtros
            if (quantum_coherence >= self.quantum_coherence_threshold and
                opportunity.confidence_score > 0.6 and
                opportunity.risk_score < 0.7):
                
                filtered.append(opportunity)
                logger.debug(
                    f"✨ Oportunidade quântica: {opportunity.symbol} "
                    f"{opportunity.exchange_buy}→{opportunity.exchange_sell} "
                    f"Spread: {opportunity.spread_percentage*100:.2f}% "
                    f"Coerência: {quantum_coherence:.3f} "
                    f"Confiança: {opportunity.confidence_score:.3f}"
                )
        
        return filtered
    
    async def _calculate_quantum_coherence(self, opportunity: ArbitrageOpportunity) -> float:
        """Calcula coerência quântica da oportunidade de arbitragem"""
        try:
            # Fatores base para coerência quântica
            base_coherence = 0.5
            
            # Fator de spread: spreads maiores têm maior coerência
            spread_factor = min(opportunity.spread_percentage / 0.01, 1.0) * 0.25
            
            # Fator de confiança
            confidence_factor = opportunity.confidence_score * 0.25
            
            # Fator de risco inverso
            risk_factor = (1.0 - opportunity.risk_score) * 0.15
            
            # Fator de latência das exchanges
            avg_latency = np.mean([
                np.mean(list(self.latency_monitor[opportunity.exchange_buy])) if self.latency_monitor[opportunity.exchange_buy] else 500,
                np.mean(list(self.latency_monitor[opportunity.exchange_sell])) if self.latency_monitor[opportunity.exchange_sell] else 500
            ])
            latency_factor = max(0, 1.0 - (avg_latency / self.max_latency_ms)) * 0.15
            
            # Fator de circuit breaker (estado das exchanges)
            cb_factor = 0.2 if (
                self.circuit_breakers[opportunity.exchange_buy].state == "CLOSED" and
                self.circuit_breakers[opportunity.exchange_sell].state == "CLOSED"
            ) else 0.0
            
            # Adicionar ruído quântico controlado
            quantum_noise = np.random.normal(0, 0.02)
            
            coherence = (base_coherence + spread_factor + confidence_factor + 
                        risk_factor + latency_factor + cb_factor + quantum_noise)
            
            return max(0.0, min(1.0, coherence))
            
        except Exception as e:
            logger.warning(f"Erro no cálculo de coerência quântica: {e}")
            return 0.0
    
    async def _execute_arbitrage_batch(self, exchanges: Dict[str, Any], opportunities: List[ArbitrageOpportunity]):
        """Executa um lote de arbitragens aprovadas"""
        if self.executed_arbitrages_today >= self.max_daily_arbitrages:
            logger.info(f"🚫 Limite diário de arbitragens atingido: {self.max_daily_arbitrages}")
            return
        
        executed_count = 0
        for opportunity in opportunities:
            if self.executed_arbitrages_today >= self.max_daily_arbitrages:
                break
                
            # Verificar se a oportunidade ainda é válida
            if opportunity.is_expired(datetime.now(timezone.utc)):
                continue
                
            success = await self._execute_single_arbitrage(exchanges, opportunity)
            if success:
                executed_count += 1
                self.executed_arbitrages_today += 1
                self.total_profit += opportunity.estimated_profit
                
                logger.info(
                    f"💰 Arbitragem #{self.executed_arbitrages_today}: "
                    f"{opportunity.symbol} +${opportunity.estimated_profit:.4f} "
                    f"({opportunity.spread_percentage*100:.2f}% spread)"
                )
        
        if executed_count > 0:
            logger.info(f"📊 Lote executado: {executed_count} arbitragens, total hoje: {self.executed_arbitrages_today}")
    
    async def _execute_single_arbitrage(self, exchanges: Dict[str, Any], opportunity: ArbitrageOpportunity) -> bool:
        """Executa uma única arbitragem"""
        try:
            logger.debug(
                f"🚀 Executando arbitragem: {opportunity.symbol} "
                f"{opportunity.exchange_buy}→{opportunity.exchange_sell} "
                f"Spread: {opportunity.spread_percentage*100:.2f}%"
            )
            
            # Calcular tamanho da posição
            position_size = self._calculate_position_size(opportunity)
            
            # Verificar circuit breakers antes da execução
            if (not self.circuit_breakers[opportunity.exchange_buy].can_execute() or
                not self.circuit_breakers[opportunity.exchange_sell].can_execute()):
                logger.warning(f"Circuit breaker ativo, pulando arbitragem {opportunity.symbol}")
                return False
            
            # Simular execução (em modo paper trading)
            if self.config.get("mode") == "paper_trading":
                await asyncio.sleep(0.05)  # Simular latência de execução reduzida
                logger.debug(f"📄 Arbitragem simulada: {position_size:.6f} {opportunity.symbol}")
                return True
            
            # Execução real seria implementada aqui
            # Incluiria:
            # 1. Verificação de saldos
            # 2. Colocação de ordens simultâneas
            # 3. Monitoramento de execução
            # 4. Rollback em caso de falha parcial
            
            return False  # Não executar trades reais ainda
            
        except Exception as e:
            logger.error(f"❌ Erro na execução de arbitragem: {e}")
            return False
    
    def _calculate_position_size(self, opportunity: ArbitrageOpportunity) -> float:
        """Calcula o tamanho ideal da posição para arbitragem"""
        capital = self.config.get("capital", 1000)
        max_position_value = capital * self.position_size_pct
        
        # Ajustar por confiança e risco
        risk_adjusted_size = max_position_value * opportunity.confidence_score * (1.0 - opportunity.risk_score)
        
        position_size = risk_adjusted_size / opportunity.price_buy
        
        # Limitar pelo volume disponível
        position_size = min(position_size, opportunity.volume_available * 0.5)  # Max 50% do volume
        
        return position_size
    
    def _estimate_available_volume(self, symbol: str, exchange_buy: str, exchange_sell: str) -> float:
        """Estima o volume disponível para arbitragem"""
        # Volume base conservador por símbolo
        base_volumes = {
            "BTC/USDT": 0.05,    # Reduzido para ser mais conservador
            "ETH/USDT": 0.5,
            "ADA/USDT": 500.0,
            "DOT/USDT": 5.0
        }
        
        base_volume = base_volumes.get(symbol, 0.01)
        
        # Ajustar baseado no estado dos circuit breakers
        cb_buy_factor = 1.0 if self.circuit_breakers[exchange_buy].state == "CLOSED" else 0.5
        cb_sell_factor = 1.0 if self.circuit_breakers[exchange_sell].state == "CLOSED" else 0.5
        
        return base_volume * cb_buy_factor * cb_sell_factor
    
    def _calculate_estimated_profit(self, price_buy: float, price_sell: float, volume: float, symbol: str) -> float:
        """Calcula o lucro estimado da arbitragem"""
        gross_profit = (price_sell - price_buy) * volume
        
        # Taxas estimadas mais realistas
        # Kraken: ~0.16%, KuCoin: ~0.1%
        avg_fee_rate = 0.0013  # ~0.13% média
        fees = (price_buy * volume * avg_fee_rate) + (price_sell * volume * avg_fee_rate)
        
        # Considerar slippage estimado
        slippage = gross_profit * 0.1  # 10% do lucro bruto
        
        net_profit = gross_profit - fees - slippage
        
        return max(0, net_profit)
    
    def _calculate_confidence_score(self, exchange_buy: str, exchange_sell: str) -> float:
        """Calcula score de confiança baseado em latência e circuit breakers"""
        scores = []
        
        for exchange in [exchange_buy, exchange_sell]:
            # Score baseado em latência
            if exchange in self.latency_monitor and self.latency_monitor[exchange]:
                avg_latency = np.mean(list(self.latency_monitor[exchange]))
                latency_score = max(0.1, 1.0 - (avg_latency / self.max_latency_ms))
            else:
                latency_score = 0.5
            
            # Score baseado no circuit breaker
            cb_score = {
                "CLOSED": 1.0,
                "HALF_OPEN": 0.7,
                "OPEN": 0.1
            }.get(self.circuit_breakers[exchange].state, 0.5)
            
            combined_score = (latency_score * 0.7) + (cb_score * 0.3)
            scores.append(combined_score)
        
        return np.mean(scores)
    
    def _calculate_risk_score(self, spread_percentage: float, confidence_score: float) -> float:
        """Calcula score de risco da oportunidade"""
        # Risco base: spreads muito altos podem ser suspeitos
        spread_risk = min(spread_percentage / 0.05, 1.0) * 0.3  # Normalizado para 5%
        
        # Risco de confiança: baixa confiança = alto risco
        confidence_risk = (1.0 - confidence_score) * 0.4
        
        # Risco de mercado: volatilidade implícita
        market_risk = 0.2  # Risco fixo de mercado
        
        # Risco de liquidez
        liquidity_risk = 0.1  # Risco fixo de liquidez
        
        total_risk = spread_risk + confidence_risk + market_risk + liquidity_risk
        
        return min(total_risk, 1.0)
    
    def _update_price_cache(self, symbol: str, exchange: str, price: float):
        """Atualizar cache de preços"""
        if symbol not in self.price_cache:
            self.price_cache[symbol] = {}
        
        self.price_cache[symbol][exchange] = (price, datetime.now(timezone.utc))
    
    def _update_latency_stats(self, exchange: str, latency: float):
        """Atualizar estatísticas de latência"""
        if exchange not in self.latency_monitor:
            self.latency_monitor[exchange] = deque(maxlen=100)
        
        self.latency_monitor[exchange].append(latency)
    
    def _update_performance_metrics(self):
        """Atualizar métricas de performance"""
        # Esta função pode ser expandida para coletar mais métricas
        pass
    
    async def shutdown(self):
        """Encerrar o engine de arbitragem"""
        logger.info("🛑 Encerrando Dimensional Arbitrage Engine")
        self.running = False
        
        # Log de estatísticas finais
        logger.info(
            f"📊 Estatísticas da sessão: "
            f"Arbitragens executadas: {self.executed_arbitrages_today}, "
            f"Lucro total: ${self.total_profit:.4f}, "
            f"Oportunidades ativas: {len(self.active_opportunities)}"
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Retorna resumo de performance da arbitragem dimensional"""
        return {
            "total_profit": self.total_profit,
            "executed_arbitrages_today": self.executed_arbitrages_today,
            "max_daily_arbitrages": self.max_daily_arbitrages,
            "active_opportunities": len(self.active_opportunities),
            "avg_latencies": {
                exchange: np.mean(list(latencies)) if latencies else 0
                for exchange, latencies in self.latency_monitor.items()
            },
            "circuit_breaker_states": {
                exchange: cb.state
                for exchange, cb in self.circuit_breakers.items()
            },
            "best_opportunity": (
                self.active_opportunities[-1].to_dict() 
                if self.active_opportunities else None
            ),
            "config": {
                "min_spread_threshold": self.min_spread_threshold,
                "position_size_pct": self.position_size_pct,
                "scan_interval": self.scan_interval
            }
        } 