"""Microtubule Operator - Simulated Orch-OR dynamics.

This module implements a simplified microtubule model inspired by the
MICQI framework. Stochastic collapse events are interpreted as insight
flashes that modulate the cognitive state.
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import numpy as np
from scipy.linalg import expm

logger = logging.getLogger(__name__)


@dataclass
class CollapseEvent:
    """Represents a stochastic collapse event."""

    probability: float
    timestamp: float


@dataclass
class MicrotubuleState:
    """State of the microtubule network."""

    tubulin_states: np.ndarray
    coherence: float
    collapse_events: List[CollapseEvent]
    timestamp: float


class MicrotubuleOperator:
    """Operator simulating Orch-OR microtubule dynamics."""

    def __init__(
        self, config: Dict[str, Any] | None = None, *, history_maxlen: int = 1000
    ) -> None:
        cfg = config or {}
        self.num_sites = int(cfg.get("num_sites", 4))
        self.coherence_threshold = float(cfg.get("coherence_threshold", 0.7))
        self.collapse_chance = float(cfg.get("collapse_chance", 0.05))

        self.current_state: Optional[MicrotubuleState] = None
        self.history: List[MicrotubuleState] = []
        self.history_maxlen = int(history_maxlen)

        self.tubulin_state = self._random_state(self.num_sites)

    # ------------------------------------------------------------------
    def _random_state(self, num_sites: int) -> np.ndarray:
        state = np.random.randn(num_sites, 2) + 1j * np.random.randn(num_sites, 2)
        norms = np.linalg.norm(state, axis=1, keepdims=True) + 1e-12
        return state / norms

    def _collapse_state(self, state: np.ndarray) -> tuple[np.ndarray, float]:
        probs = np.abs(state) ** 2
        collapsed = np.zeros_like(state)
        mean_prob = 0.0
        for i, p in enumerate(probs):
            collapse_to_zero = np.random.rand() < p[0]
            collapsed[i] = (
                np.array([1.0, 0.0], dtype=complex)
                if collapse_to_zero
                else np.array([0.0, 1.0], dtype=complex)
            )
            mean_prob += p[0] if collapse_to_zero else p[1]
        mean_prob /= state.shape[0]
        return collapsed, float(mean_prob)

    def _evolve_state(self, field_mean: float) -> np.ndarray:
        h = np.array([[0.0, field_mean], [field_mean, 0.0]], dtype=float)
        u = expm(-1j * h)
        return self.tubulin_state @ u

    # ------------------------------------------------------------------
    async def simulate_dynamics(
        self, external_field: np.ndarray, timestamp: float
    ) -> MicrotubuleState:
        """Simulate a step of microtubule evolution.

        Parameters
        ----------
        external_field
            External influence applied to the network.
        timestamp
            Current timestamp.

        Returns
        -------
        MicrotubuleState
            Updated microtubule state.
        """
        field_mean = float(np.mean(external_field))
        evolved = self._evolve_state(field_mean)
        coherence = float(np.mean(np.abs(evolved @ evolved.conj().T)) - 1.0)

        events: List[CollapseEvent] = []
        if (
            coherence < self.coherence_threshold
            or np.random.rand() < self.collapse_chance
        ):
            evolved, prob = self._collapse_state(evolved)
            events.append(CollapseEvent(prob, timestamp))

        state = MicrotubuleState(evolved, coherence, events, timestamp)
        self.current_state = state
        self.history.append(state)
        if len(self.history) > self.history_maxlen:
            self.history.pop(0)
        self.tubulin_state = evolved
        return state

    # ------------------------------------------------------------------
    def insight_rate(self) -> float:
        """Return average number of insights per step."""
        if not self.history:
            return 0.0
        events = sum(len(s.collapse_events) for s in self.history)
        return float(events / len(self.history))

    def calculate_entropy(self) -> float:
        """Return normalized entropy of current tubulin probabilities."""
        if self.current_state is None:
            return 0.0
        probs = np.abs(self.current_state.tubulin_states) ** 2
        flat = probs.flatten()
        if flat.size == 0:
            return 0.0
        flat = flat / flat.sum()
        flat = flat[flat > 0]
        ent = float(np.sum(-flat * np.log(flat)))
        max_ent = np.log(flat.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def get_state_dict(self) -> Dict[str, Any]:
        """Return a serializable representation of the operator state."""
        return {
            "num_sites": self.num_sites,
            "coherence_threshold": self.coherence_threshold,
            "collapse_chance": self.collapse_chance,
            "history_length": len(self.history),
            "current_coherence": (
                float(self.current_state.coherence) if self.current_state else 0.0
            ),
            "insight_events": sum(len(s.collapse_events) for s in self.history),
        }
