from __future__ import annotations

"""Entanglement and entropy utilities for QUALIA."""

from typing import Iterable, Sequence

import numpy as np
from qiskit.quantum_info import Statevector, DensityMatrix, partial_trace, entropy

from ..utils.logger import get_logger

logger = get_logger(__name__)


def von_neumann_entropy(
    state: Statevector | Sequence[complex],
    subsystems: Iterable[int] | None = None,
) -> float:
    """Return the von Neumann entropy of ``state`` or a ``subsystem``.

    Parameters
    ----------
    state:
        Quantum state as a :class:`Statevector` or array-like.
    subsystems:
        Indices of qubits to keep when computing the reduced density matrix.
        If ``None``, the entropy of the full system is returned.

    Returns
    -------
    float
        von Neumann entropy in bits.
    """
    try:
        sv = state if isinstance(state, Statevector) else Statevector(state)
        dm = DensityMatrix(sv)
        if subsystems is not None:
            all_idx = list(range(dm.num_qubits))
            trace_out = [i for i in all_idx if i not in subsystems]
            try:
                dm = partial_trace(dm, trace_out)
            except Exception:
                dm = _partial_trace_numpy(dm.data, trace_out)
        value = entropy(dm, base=2)
        return float(value)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("von_neumann_entropy failed: %s", exc)
        try:
            vec = np.asarray(state, dtype=complex).ravel()
            rho = np.outer(vec, np.conj(vec))
            n_qubits = int(np.log2(vec.size))
            trace_out = []
            if subsystems is not None:
                trace_out = [i for i in range(n_qubits) if i not in subsystems]
            rho_red = _partial_trace_numpy(rho, trace_out)
            vals = np.linalg.eigvalsh(rho_red)
            vals = vals[vals > 1e-12]
            if vals.size == 0:
                return 0.0
            return float(-np.sum(vals * np.log2(vals)))
        except Exception as exc2:  # pragma: no cover - defensive
            logger.error("fallback von_neumann_entropy failed: %s", exc2)
            return 0.0


def bipartite_entanglement(
    state: Statevector | Sequence[complex],
    subsystem: Iterable[int],
) -> float:
    """Return entanglement between ``subsystem`` and the rest of ``state``.

    The measure is the von Neumann entropy of the reduced density matrix
    obtained by tracing out all qubits except those in ``subsystem``.
    For pure states this equals the entanglement entropy.
    """
    return von_neumann_entropy(state, subsystem)


def multipartite_entanglement(state: Statevector | Sequence[complex]) -> float:
    """Return the average single-qubit entanglement of ``state``."""
    try:
        sv = state if isinstance(state, Statevector) else Statevector(state)
        n_qubits = sv.num_qubits
        ent_vals = [bipartite_entanglement(sv, [i]) for i in range(n_qubits)]
        return float(np.mean(ent_vals)) if ent_vals else 0.0
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("multipartite_entanglement failed: %s", exc)
        return 0.0


def _partial_trace_numpy(rho: np.ndarray, trace_out: Iterable[int]) -> np.ndarray:
    """Perform partial trace using NumPy for fallback cases."""
    dims = int(round(np.log2(rho.shape[0])))
    reshaped = rho.reshape([2] * dims * 2)
    for k in sorted(trace_out, reverse=True):
        reshaped = np.trace(reshaped, axis1=k, axis2=k + dims)
    keep = dims - len(trace_out)
    final = reshaped.reshape(2**keep, 2**keep)
    return final
