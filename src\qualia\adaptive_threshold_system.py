#!/usr/bin/env python3
"""
QUALIA Adaptive Threshold System
Sistema inteligente de thresholds adaptativos baseado em performance real

FUNCIONALIDADES:
- Análise diagnóstica completa de métricas vs thresholds
- Thresholds adaptativos que se ajustam automaticamente
- Múltiplos modos: conservador/moderado/agressivo
- Logs detalhados para debugging
- Validação empírica baseada em resultados históricos
"""

import asyncio
import ccxt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import logging
import os
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple
from enum import Enum

# Importar o sistema de configuração centralizado
from .config_manager import get_config_manager, ConfigurationError
from .intelligent_adaptation_system import IntelligentAdaptationSystem

# Configurar logging detalhado
# Garantir que o diretório de logs existe
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, f'qualia_adaptive_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingMode(Enum):
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

@dataclass
class ThresholdConfig:
    """Configuração de thresholds por modo"""
    consciousness: float
    coherence: float
    confidence: float
    volume_surge_min: float
    momentum_min: float
    spectral_phi_alignment_min: float
    golden_symmetry_min: float

@dataclass
class MetricAnalysis:
    """Análise de uma métrica específica"""
    symbol: str
    consciousness: float
    coherence: float
    confidence: float
    volume_surge: float
    momentum: float
    spectral_phi_alignment: float
    golden_symmetry: float
    geometric_coherence: float
    threshold_passed: bool
    failed_thresholds: List[str]
    quality_score: float

class AdaptiveThresholdManager:
    """Gerenciador inteligente de thresholds adaptativos"""
    
    def __init__(self, config_path: str = 'config/qualia_config.yaml', trading_system=None):
        # CARREGAR CONFIGURAÇÕES DE THRESHOLD DO YAML (elimina hardcoding)
        self.trading_system = trading_system  # Referência para sincronização
        try:
            self.config_manager = get_config_manager(config_path)

            # Carregar configurações por modo do YAML
            trading_modes = self.config_manager.get('quantum_thresholds.trading_modes')

            # Obter valores base dos thresholds geométricos
            base_spectral_phi = self.config_manager.get('quantum_thresholds.spectral_phi_alignment_min')
            base_golden_symmetry = self.config_manager.get('quantum_thresholds.golden_symmetry_min')

            self.threshold_configs = {
                TradingMode.CONSERVATIVE: ThresholdConfig(
                    consciousness=trading_modes['conservative']['consciousness'],
                    coherence=trading_modes['conservative']['coherence'],
                    confidence=trading_modes['conservative']['confidence'],
                    volume_surge_min=trading_modes['conservative']['volume_surge_min'],
                    momentum_min=trading_modes['conservative']['momentum_min'],
                    spectral_phi_alignment_min=base_spectral_phi,
                    golden_symmetry_min=base_golden_symmetry
                ),
                TradingMode.MODERATE: ThresholdConfig(
                    consciousness=trading_modes['moderate']['consciousness'],
                    coherence=trading_modes['moderate']['coherence'],
                    confidence=trading_modes['moderate']['confidence'],
                    volume_surge_min=trading_modes['moderate']['volume_surge_min'],
                    momentum_min=trading_modes['moderate']['momentum_min'],
                    spectral_phi_alignment_min=base_spectral_phi,
                    golden_symmetry_min=base_golden_symmetry
                ),
                TradingMode.AGGRESSIVE: ThresholdConfig(
                    consciousness=trading_modes['aggressive']['consciousness'],
                    coherence=trading_modes['aggressive']['coherence'],
                    confidence=trading_modes['aggressive']['confidence'],
                    volume_surge_min=trading_modes['aggressive']['volume_surge_min'],
                    momentum_min=trading_modes['aggressive']['momentum_min'],
                    spectral_phi_alignment_min=base_spectral_phi,
                    golden_symmetry_min=base_golden_symmetry
                )
            }

            logger.info("[OK] Configurações de threshold carregadas do YAML (sem hardcoding)")

        except ConfigurationError as e:
            logger.error(f"[ERROR] FALHA CRÍTICA: Não foi possível carregar configurações de threshold: {e}")
            raise SystemExit(f"Sistema adaptativo não pode inicializar sem configuração válida: {e}")
        
        # THRESHOLDS CALIBRADOS CIENTIFICAMENTE (baseado em 2,282 pontos históricos, 24.4% win rate)
        # Usar valores calibrados da raiz do YAML em vez de valores hardcoded
        base_consciousness = self.config_manager.get('quantum_thresholds.consciousness')
        base_coherence = self.config_manager.get('quantum_thresholds.coherence')
        base_confidence = self.config_manager.get('quantum_thresholds.confidence')
        base_volume_surge = self.config_manager.get('quantum_thresholds.volume_surge_min')
        base_momentum = self.config_manager.get('quantum_thresholds.momentum_min')

        self.empirical_thresholds = ThresholdConfig(
            consciousness=base_consciousness,
            coherence=base_coherence,
            confidence=base_confidence,
            volume_surge_min=base_volume_surge,
            momentum_min=base_momentum,
            spectral_phi_alignment_min=base_spectral_phi,
            golden_symmetry_min=base_golden_symmetry
        )

        logger.info(f"[CALIBRATED] Usando thresholds calibrados: C={base_consciousness:.3f}, Coh={base_coherence:.3f}, Conf={base_confidence:.3f}")

        # THRESHOLDS ULTRA-OTIMIZADOS (baseados nos valores calibrados - 85% dos valores)
        self.ultra_optimized_thresholds = ThresholdConfig(
            consciousness=base_consciousness * 0.85,
            coherence=base_coherence * 0.85,
            confidence=base_confidence * 0.85,
            volume_surge_min=base_volume_surge * 0.85,
            momentum_min=base_momentum * 0.85,
            spectral_phi_alignment_min=base_spectral_phi * 0.8,  # Mais permissivo para geometria
            golden_symmetry_min=base_golden_symmetry * 0.8
        )
        
        # Estado atual
        self.current_mode = TradingMode.MODERATE
        self.current_thresholds = self.threshold_configs[self.current_mode]
        self.cycles_without_signals = 0
        self.total_assets_analyzed = 0
        self.metric_statistics = {
            'consciousness': [], 'coherence': [], 'confidence': [],
            'volume_surge': [], 'momentum': []
        }
        
        # Histórico de adaptações
        self.adaptation_history = []

        # SISTEMA DE ADAPTAÇÃO INTELIGENTE
        # Preparar thresholds calibrados para o sistema inteligente
        calibrated_thresholds = {
            'consciousness': base_consciousness,
            'coherence': base_coherence,
            'confidence': base_confidence,
            'volume_surge_min': base_volume_surge,
            'momentum_min': base_momentum,
            'spectral_phi_alignment_min': base_spectral_phi,
            'golden_symmetry_min': base_golden_symmetry
        }

        # Inicializar sistema de adaptação inteligente
        self.intelligent_adaptation = IntelligentAdaptationSystem(
            config_manager=self.config_manager,
            calibrated_thresholds=calibrated_thresholds,
            trading_system=self.trading_system  # Passar referência para aplicação direta
        )

        # Usar thresholds do sistema inteligente se habilitado
        if self.intelligent_adaptation.config['enabled']:
            logger.info("[INTELLIGENT] Sistema de Adaptação Inteligente ATIVADO")
            logger.info("[INTELLIGENT] Thresholds calibrados como base de referência")
            # Atualizar current_thresholds com os valores do sistema inteligente
            intelligent_thresholds = self.intelligent_adaptation.current_thresholds
            self.current_thresholds = ThresholdConfig(
                consciousness=intelligent_thresholds['consciousness'],
                coherence=intelligent_thresholds['coherence'],
                confidence=intelligent_thresholds['confidence'],
                volume_surge_min=intelligent_thresholds['volume_surge_min'],
                momentum_min=intelligent_thresholds['momentum_min'],
                spectral_phi_alignment_min=intelligent_thresholds['spectral_phi_alignment_min'],
                golden_symmetry_min=intelligent_thresholds['golden_symmetry_min']
            )
        else:
            logger.info("[INTELLIGENT] Sistema de Adaptação Inteligente DESABILITADO - usando thresholds fixos")
        self.performance_tracking = {
            'signals_generated': 0,
            'trades_executed': 0,
            'win_rate': 0.0,
            'last_adaptation': None
        }

    def update_thresholds_from_memory(self, aggregated: Dict[str, float]):
        """Suaviza thresholds a partir das métricas agregadas do histórico."""
        if not aggregated:
            return

        for key in ['consciousness', 'coherence', 'confidence']:
            if key in aggregated:
                current = getattr(self.current_thresholds, key)
                new_value = current * 0.75 + aggregated[key] * 0.25
                setattr(self.current_thresholds, key, new_value)
    
    def analyze_metric_vs_threshold(self, metrics: Dict, symbol: str) -> MetricAnalysis:
        """Análise detalhada de métricas vs thresholds"""
        
        failed_thresholds = []
        
        # Verificar cada threshold
        if metrics['consciousness'] < self.current_thresholds.consciousness:
            failed_thresholds.append(f"consciousness ({metrics['consciousness']:.3f} < {self.current_thresholds.consciousness:.3f})")
        
        if metrics['coherence'] < self.current_thresholds.coherence:
            failed_thresholds.append(f"coherence ({metrics['coherence']:.3f} < {self.current_thresholds.coherence:.3f})")
        
        if metrics['confidence'] < self.current_thresholds.confidence:
            failed_thresholds.append(f"confidence ({metrics['confidence']:.3f} < {self.current_thresholds.confidence:.3f})")
        
        if metrics['volume_surge'] < self.current_thresholds.volume_surge_min:
            failed_thresholds.append(f"volume_surge ({metrics['volume_surge']:.3f} < {self.current_thresholds.volume_surge_min:.3f})")
        
        if abs(metrics['momentum']) < self.current_thresholds.momentum_min:
            failed_thresholds.append(f"momentum ({abs(metrics['momentum']):.4f} < {self.current_thresholds.momentum_min:.4f})")

        # Verificar métricas geométricas
        spectral_phi = metrics.get('spectral_phi_alignment', 0.0)
        golden_symmetry = metrics.get('golden_symmetry', 0.0)
        geometric_coherence = metrics.get('geometric_coherence', 0.0)

        if spectral_phi < self.current_thresholds.spectral_phi_alignment_min:
            failed_thresholds.append(f"spectral_phi_alignment ({spectral_phi:.3f} < {self.current_thresholds.spectral_phi_alignment_min:.3f})")

        if golden_symmetry < self.current_thresholds.golden_symmetry_min:
            failed_thresholds.append(f"golden_symmetry ({golden_symmetry:.3f} < {self.current_thresholds.golden_symmetry_min:.3f})")

        threshold_passed = len(failed_thresholds) == 0
        quality_score = (metrics['consciousness'] + metrics['coherence'] + metrics['confidence']) / 3

        return MetricAnalysis(
            symbol=symbol,
            consciousness=metrics['consciousness'],
            coherence=metrics['coherence'],
            confidence=metrics['confidence'],
            volume_surge=metrics['volume_surge'],
            momentum=metrics['momentum'],
            spectral_phi_alignment=spectral_phi,
            golden_symmetry=golden_symmetry,
            geometric_coherence=geometric_coherence,
            threshold_passed=threshold_passed,
            failed_thresholds=failed_thresholds,
            quality_score=quality_score
        )
    
    def update_statistics(self, metrics: Dict):
        """Atualiza estatísticas das métricas com validação"""
        self.metric_statistics['consciousness'].append(metrics['consciousness'])
        self.metric_statistics['coherence'].append(metrics['coherence'])
        self.metric_statistics['confidence'].append(metrics['confidence'])

        # Adicionar volume_surge e momentum com validação
        if 'volume_surge' in metrics and metrics['volume_surge'] is not None:
            self.metric_statistics['volume_surge'].append(metrics['volume_surge'])
        if 'momentum' in metrics and metrics['momentum'] is not None:
            self.metric_statistics['momentum'].append(abs(metrics['momentum']))  # Usar valor absoluto

        self.total_assets_analyzed += 1

        # Manter apenas últimas 100 métricas para performance
        for metric_list in self.metric_statistics.values():
            if len(metric_list) > 100:
                metric_list.pop(0)
    
    def get_diagnostic_report(self) -> Dict:
        """Gera relatório diagnóstico completo"""
        if not self.metric_statistics['consciousness']:
            return {"error": "Nenhuma métrica coletada ainda"}
        
        stats = {}
        for metric, values in self.metric_statistics.items():
            if values:
                stats[metric] = {
                    'mean': np.mean(values),
                    'median': np.median(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'percentile_75': np.percentile(values, 75),
                    'percentile_90': np.percentile(values, 90),
                    'current_threshold': getattr(self.current_thresholds, metric, 'N/A')
                }
        
        # Análise de lacunas
        gaps = {}
        for metric in ['consciousness', 'coherence', 'confidence']:
            current_threshold = getattr(self.current_thresholds, metric)
            mean_value = stats[metric]['mean']
            gaps[metric] = {
                'gap': current_threshold - mean_value,
                'percentage_above_threshold': sum(1 for v in self.metric_statistics[metric] if v >= current_threshold) / len(self.metric_statistics[metric]) * 100
            }
        
        return {
            'current_mode': self.current_mode.value,
            'current_thresholds': asdict(self.current_thresholds),
            'cycles_without_signals': self.cycles_without_signals,
            'total_assets_analyzed': self.total_assets_analyzed,
            'metric_statistics': stats,
            'threshold_gaps': gaps,
            'adaptation_history': self.adaptation_history[-5:],  # Últimas 5 adaptações
            'performance': self.performance_tracking
        }
    
    def suggest_threshold_adjustments(self) -> Dict:
        """Sugere ajustes de threshold baseado em dados reais"""
        if not self.metric_statistics['consciousness']:
            return {"suggestion": "Aguardar mais dados"}
        
        suggestions = {}
        
        for metric in ['consciousness', 'coherence', 'confidence']:
            values = self.metric_statistics[metric]
            current_threshold = getattr(self.current_thresholds, metric)
            
            # Calcular percentis
            p75 = np.percentile(values, 75)
            p90 = np.percentile(values, 90)
            mean_val = np.mean(values)
            
            # Sugestões baseadas em distribuição
            if current_threshold > p90:
                suggestions[metric] = {
                    'current': current_threshold,
                    'suggested': p75,
                    'reason': f'Threshold muito alto - apenas {sum(1 for v in values if v >= current_threshold)/len(values)*100:.1f}% dos ativos passam',
                    'empirical_validated': getattr(self.empirical_thresholds, metric)
                }
            elif current_threshold > p75:
                suggestions[metric] = {
                    'current': current_threshold,
                    'suggested': (p75 + mean_val) / 2,
                    'reason': f'Threshold alto - {sum(1 for v in values if v >= current_threshold)/len(values)*100:.1f}% dos ativos passam',
                    'empirical_validated': getattr(self.empirical_thresholds, metric)
                }
        
        return suggestions
    
    def should_adapt_thresholds(self) -> bool:
        """Decide se deve adaptar thresholds com lógica otimizada"""
        # Adaptar se muitos ciclos sem sinais (reduzido de 5 para 3)
        if self.cycles_without_signals >= 3:
            return True

        # Adaptar baseado na taxa de aprovação atual
        if self.total_assets_analyzed >= 10:  # Reduzido de 20 para 10 (mais responsivo)
            current_pass_rate = self.calculate_current_pass_rate()

            # Meta: 15-20% de aprovação
            if current_pass_rate < 0.10:  # Menos de 10% - muito baixo
                logger.info(f"Taxa de aprovação muito baixa: {current_pass_rate:.1%} (meta: 15-20%)")
                return True
            elif current_pass_rate > 0.30:  # Mais de 30% - muito alto
                logger.info(f"Taxa de aprovação muito alta: {current_pass_rate:.1%} (meta: 15-20%)")
                return True

        return False

    def calculate_current_pass_rate(self) -> float:
        """Calcula taxa de aprovação atual baseada em métricas coletadas"""
        if not self.metric_statistics['consciousness']:
            return 0.0

        total_assets = len(self.metric_statistics['consciousness'])
        passed_assets = 0

        for i in range(total_assets):
            # Verificar se ativo passou em todos os thresholds
            consciousness_pass = self.metric_statistics['consciousness'][i] >= self.current_thresholds.consciousness
            coherence_pass = self.metric_statistics['coherence'][i] >= self.current_thresholds.coherence
            confidence_pass = self.metric_statistics['confidence'][i] >= self.current_thresholds.confidence

            # Verificar volume_surge e momentum se disponíveis
            volume_pass = True
            momentum_pass = True

            if 'volume_surge' in self.metric_statistics and i < len(self.metric_statistics['volume_surge']):
                volume_pass = self.metric_statistics['volume_surge'][i] >= self.current_thresholds.volume_surge_min

            if 'momentum' in self.metric_statistics and i < len(self.metric_statistics['momentum']):
                momentum_pass = abs(self.metric_statistics['momentum'][i]) >= self.current_thresholds.momentum_min

            if consciousness_pass and coherence_pass and confidence_pass and volume_pass and momentum_pass:
                passed_assets += 1

        return passed_assets / total_assets if total_assets > 0 else 0.0
    
    def adapt_thresholds(self, force_mode: Optional[TradingMode] = None):
        """Adapta thresholds automaticamente usando sistema inteligente"""

        # Se sistema inteligente está habilitado, usar sua lógica
        if self.intelligent_adaptation.config['enabled']:
            result = self._adapt_with_intelligent_system()
            # CRÍTICO: Garantir que mudanças sejam aplicadas no sistema de trading
            self._force_sync_with_trading_system()
            return result

        # Caso contrário, usar lógica legada
        return self._adapt_legacy_system(force_mode)

    def _adapt_with_intelligent_system(self):
        """Adaptação usando sistema inteligente"""
        # Calcular métricas atuais
        current_pass_rate = self.calculate_current_pass_rate()
        signals_found = 1 if current_pass_rate > 0 else 0

        # Processar ciclo no sistema inteligente
        updated_thresholds = self.intelligent_adaptation.process_cycle(
            pass_rate=current_pass_rate,
            assets_analyzed=self.total_assets_analyzed,
            signals_found=signals_found
        )

        # Atualizar current_thresholds se houve mudança
        old_thresholds = asdict(self.current_thresholds)
        new_thresholds = ThresholdConfig(
            consciousness=updated_thresholds['consciousness'],
            coherence=updated_thresholds['coherence'],
            confidence=updated_thresholds['confidence'],
            volume_surge_min=updated_thresholds['volume_surge_min'],
            momentum_min=updated_thresholds['momentum_min'],
            spectral_phi_alignment_min=updated_thresholds['spectral_phi_alignment_min'],
            golden_symmetry_min=updated_thresholds['golden_symmetry_min']
        )

        # Verificar se houve mudança
        if asdict(new_thresholds) != old_thresholds:
            self.current_thresholds = new_thresholds

            logger.info(f"[INTELLIGENT] Thresholds atualizados pelo sistema inteligente")
            logger.info(f"[INTELLIGENT] Estado atual: {self.intelligent_adaptation.current_state.value}")

            # Log das mudanças principais
            for metric in ['consciousness', 'coherence', 'confidence']:
                old_val = old_thresholds[metric]
                new_val = getattr(new_thresholds, metric)
                if abs(new_val - old_val) > 0.001:
                    change = new_val - old_val
                    logger.info(f"[INTELLIGENT]   {metric}: {old_val:.3f} -> {new_val:.3f} ({change:+.3f})")

    def _adapt_legacy_system(self, force_mode: Optional[TradingMode] = None):
        """Sistema de adaptação legado (fallback)"""
        old_thresholds = asdict(self.current_thresholds)
        old_mode = self.current_mode

        if force_mode:
            self.current_mode = force_mode
        else:
            # Lógica de adaptação inteligente baseada em taxa de aprovação
            current_pass_rate = self.calculate_current_pass_rate()

            if current_pass_rate < 0.05:  # Menos de 5% - usar ultra-otimizado
                self.current_thresholds = self.ultra_optimized_thresholds
                adaptation_reason = f"Taxa muito baixa ({current_pass_rate:.1%}) - aplicando thresholds ultra-otimizados"
                new_mode = 'ultra_optimized'

            elif current_pass_rate < 0.10:  # Menos de 10% - usar agressivo
                self.current_mode = TradingMode.AGGRESSIVE
                adaptation_reason = f"Taxa baixa ({current_pass_rate:.1%}) - modo agressivo"
                new_mode = self.current_mode.value

            elif current_pass_rate > 0.30:  # Mais de 30% - usar conservativo
                self.current_mode = TradingMode.CONSERVATIVE
                adaptation_reason = f"Taxa alta ({current_pass_rate:.1%}) - modo conservativo"
                new_mode = self.current_mode.value

            elif self.cycles_without_signals >= 5:  # Muitos ciclos sem sinais
                self.current_mode = TradingMode.AGGRESSIVE
                adaptation_reason = f"Muitos ciclos sem sinais ({self.cycles_without_signals})"
                new_mode = self.current_mode.value

            else:
                # Usar thresholds empíricos otimizados
                self.current_thresholds = self.empirical_thresholds
                adaptation_reason = f"Aplicando thresholds empíricos otimizados (taxa atual: {current_pass_rate:.1%})"
                new_mode = 'empirical_optimized'

                adaptation = {
                    'timestamp': datetime.now().isoformat(),
                    'reason': adaptation_reason,
                    'old_mode': old_mode.value,
                    'new_mode': new_mode,
                    'old_thresholds': old_thresholds,
                    'new_thresholds': asdict(self.current_thresholds),
                    'cycles_without_signals': self.cycles_without_signals,
                    'pass_rate': current_pass_rate
                }

                self.adaptation_history.append(adaptation)
                self.performance_tracking['last_adaptation'] = datetime.now()

                logger.info("APLICANDO THRESHOLDS EMPÍRICOS OTIMIZADOS")
                logger.info(f"Taxa de aprovação atual: {current_pass_rate:.1%}")
                logger.info(f"Consciousness: {old_thresholds['consciousness']:.3f} -> {self.current_thresholds.consciousness:.3f}")
                logger.info(f"Coherence: {old_thresholds['coherence']:.3f} -> {self.current_thresholds.coherence:.3f}")
                logger.info(f"Confidence: {old_thresholds['confidence']:.3f} -> {self.current_thresholds.confidence:.3f}")
                logger.info(f"Volume Surge: {old_thresholds['volume_surge_min']:.3f} -> {self.current_thresholds.volume_surge_min:.3f}")
                logger.info(f"Momentum: {old_thresholds['momentum_min']:.4f} -> {self.current_thresholds.momentum_min:.4f}")
                return
        
        # Aplicar novo modo se não foi aplicado acima
        if new_mode not in ['ultra_optimized', 'empirical_optimized']:
            self.current_thresholds = self.threshold_configs[self.current_mode]

        # Registrar adaptação
        current_pass_rate = self.calculate_current_pass_rate()
        adaptation = {
            'timestamp': datetime.now().isoformat(),
            'reason': adaptation_reason,
            'old_mode': old_mode.value,
            'new_mode': new_mode,
            'old_thresholds': old_thresholds,
            'new_thresholds': asdict(self.current_thresholds),
            'cycles_without_signals': self.cycles_without_signals,
            'pass_rate': current_pass_rate
        }

        self.adaptation_history.append(adaptation)
        self.performance_tracking['last_adaptation'] = datetime.now()

        logger.info("ADAPTAÇÃO AUTOMÁTICA DE THRESHOLDS OTIMIZADA")
        logger.info(f"Modo: {old_mode.value} -> {new_mode}")
        logger.info(f"Motivo: {adaptation_reason}")
        logger.info(f"Taxa de aprovação: {current_pass_rate:.1%}")
        logger.info(f"Consciousness: {old_thresholds['consciousness']:.3f} -> {self.current_thresholds.consciousness:.3f}")
        logger.info(f"Coherence: {old_thresholds['coherence']:.3f} -> {self.current_thresholds.coherence:.3f}")
        logger.info(f"Confidence: {old_thresholds['confidence']:.3f} -> {self.current_thresholds.confidence:.3f}")
        logger.info(f"Volume Surge: {old_thresholds['volume_surge_min']:.3f} -> {self.current_thresholds.volume_surge_min:.3f}")
        logger.info(f"Momentum: {old_thresholds['momentum_min']:.4f} -> {self.current_thresholds.momentum_min:.4f}")
    
    def reset_cycle_counter(self):
        """Reseta contador de ciclos sem sinais"""
        self.cycles_without_signals = 0
    
    def increment_cycle_counter(self):
        """Incrementa contador de ciclos sem sinais"""
        self.cycles_without_signals += 1
    
    def log_detailed_analysis(self, analyses: List[MetricAnalysis]):
        """Log detalhado de todas as análises"""
        logger.info("=" * 80)
        logger.info("ANÁLISE DETALHADA DE MÉTRICAS vs THRESHOLDS")
        logger.info("=" * 80)
        logger.info(f"Modo atual: {self.current_mode.value.upper()}")
        logger.info(f"Thresholds: C>={self.current_thresholds.consciousness:.3f}, "
                   f"Coh>={self.current_thresholds.coherence:.3f}, "
                   f"Conf>={self.current_thresholds.confidence:.3f}")
        logger.info("-" * 80)
        
        passed_count = 0
        for analysis in analyses:
            status = "PASS" if analysis.threshold_passed else "FAIL"
            logger.info(f"{status} {analysis.symbol}:")
            logger.info(f"  Consciousness: {analysis.consciousness:.3f} "
                       f"({'PASS' if analysis.consciousness >= self.current_thresholds.consciousness else 'FAIL'})")
            logger.info(f"  Coherence:     {analysis.coherence:.3f} "
                       f"({'PASS' if analysis.coherence >= self.current_thresholds.coherence else 'FAIL'})")
            logger.info(f"  Confidence:    {analysis.confidence:.3f} "
                       f"({'PASS' if analysis.confidence >= self.current_thresholds.confidence else 'FAIL'})")
            logger.info(f"  Volume Surge:  {analysis.volume_surge:.3f} "
                       f"({'PASS' if analysis.volume_surge >= self.current_thresholds.volume_surge_min else 'FAIL'})")
            logger.info(f"  Momentum:      {abs(analysis.momentum):.4f} "
                       f"({'PASS' if abs(analysis.momentum) >= self.current_thresholds.momentum_min else 'FAIL'})")
            logger.info(f"  Spectral Phi:  {analysis.spectral_phi_alignment:.3f} "
                       f"({'PASS' if analysis.spectral_phi_alignment >= self.current_thresholds.spectral_phi_alignment_min else 'FAIL'})")
            logger.info(f"  Golden Symm:   {analysis.golden_symmetry:.3f} "
                       f"({'PASS' if analysis.golden_symmetry >= self.current_thresholds.golden_symmetry_min else 'FAIL'})")
            logger.info(f"  Geom Coherence:{analysis.geometric_coherence:.3f}")
            logger.info(f"  Quality Score: {analysis.quality_score:.3f}")

            if not analysis.threshold_passed:
                logger.info(f"  Failed: {', '.join(analysis.failed_thresholds)}")
            else:
                passed_count += 1

            logger.info("")
        
        pass_rate = passed_count / len(analyses) * 100 if analyses else 0
        logger.info(f"RESUMO: {passed_count}/{len(analyses)} ativos passaram ({pass_rate:.1f}%)")
        logger.info(f"META: 15-20% de aprovação")
        logger.info(f"Ciclos sem sinais: {self.cycles_without_signals}")

        # Sugestão de otimização baseada na taxa atual
        if pass_rate < 10:
            logger.warning(f"TAXA MUITO BAIXA ({pass_rate:.1f}%) - Thresholds muito restritivos")
        elif pass_rate > 25:
            logger.warning(f"TAXA ALTA ({pass_rate:.1f}%) - Thresholds muito permissivos")
        else:
            logger.info(f"TAXA ADEQUADA: {pass_rate:.1f}% está próxima da meta (15-20%)")

        logger.info("=" * 80)

    def get_optimization_summary(self) -> Dict:
        """Retorna resumo das otimizações implementadas"""
        current_pass_rate = self.calculate_current_pass_rate()

        return {
            'optimization_status': 'IMPLEMENTED',
            'current_mode': self.current_mode.value,
            'current_pass_rate': current_pass_rate,
            'target_pass_rate': '15-20%',
            'optimizations': {
                'moderate_mode': {
                    'volume_surge_min': f"1.3 -> {self.threshold_configs[TradingMode.MODERATE].volume_surge_min}",
                    'momentum_min': f"0.0008 -> {self.threshold_configs[TradingMode.MODERATE].momentum_min}",
                    'consciousness': f"0.60 -> {self.threshold_configs[TradingMode.MODERATE].consciousness}",
                    'coherence': f"0.50 -> {self.threshold_configs[TradingMode.MODERATE].coherence}",
                    'confidence': f"0.55 -> {self.threshold_configs[TradingMode.MODERATE].confidence}"
                },
                'aggressive_mode': {
                    'volume_surge_min': f"1.2 -> {self.threshold_configs[TradingMode.AGGRESSIVE].volume_surge_min}",
                    'momentum_min': f"0.0005 -> {self.threshold_configs[TradingMode.AGGRESSIVE].momentum_min}"
                },
                'ultra_optimized': {
                    'volume_surge_min': self.ultra_optimized_thresholds.volume_surge_min,
                    'momentum_min': self.ultra_optimized_thresholds.momentum_min,
                    'description': 'Para situações de baixa atividade'
                }
            },
            'intelligent_adaptation': {
                'pass_rate_monitoring': True,
                'automatic_adjustment': True,
                'target_achievement': current_pass_rate >= 0.15 and current_pass_rate <= 0.20
            },
            'total_adaptations': len(self.adaptation_history),
            'cycles_without_signals': self.cycles_without_signals
        }

    def _force_sync_with_trading_system(self):
        """
        Força sincronização dos thresholds com o sistema de trading
        Garante que mudanças do sistema inteligente sejam aplicadas efetivamente
        """
        try:
            if hasattr(self, 'trading_system') and self.trading_system:
                # Obter thresholds atuais do sistema inteligente
                intelligent_thresholds = self.intelligent_adaptation.current_thresholds

                # Aplicar no sistema de trading
                self.trading_system.quantum_thresholds.update(intelligent_thresholds)

                # Sincronizar com current_thresholds local
                self.current_thresholds = ThresholdConfig(
                    consciousness=intelligent_thresholds['consciousness'],
                    coherence=intelligent_thresholds['coherence'],
                    confidence=intelligent_thresholds['confidence'],
                    volume_surge_min=intelligent_thresholds['volume_surge_min'],
                    momentum_min=intelligent_thresholds['momentum_min'],
                    spectral_phi_alignment_min=intelligent_thresholds.get('spectral_phi_alignment_min', 0.35),
                    golden_symmetry_min=intelligent_thresholds.get('golden_symmetry_min', 0.35)
                )

                logger.info("Thresholds sincronizados com sistema de trading")

                # Log das mudanças para debug
                for metric, value in intelligent_thresholds.items():
                    logger.debug(f"   {metric}: {value:.4f}")

            else:
                logger.warning("Sistema de trading não disponível para sincronização")

        except Exception as e:
            logger.error(f"Erro na sincronização com sistema de trading: {e}")
