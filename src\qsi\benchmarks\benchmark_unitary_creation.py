import timeit
import numpy as np
import sys
import os

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Adiciona o diretório raiz do projeto ao path para permitir a importação do qsi
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from qsi.geometry import build_phi_unitary

def run_benchmark():
    """
    Executa o benchmark de performance para a função build_phi_unitary
    em várias dimensões e imprime os resultados.
    """
    logger.info("--- Iniciando Benchmark de Performance: build_phi_unitary ---")
    
    dims_to_test = [32, 64, 128]
    num_executions = 5  # Número de execuções para tirar a média

    # Cabeçalho da tabela de resultados
    logger.info("%s | %s", "Dimensão".ljust(12), "Tempo Médio (ms)".ljust(20))
    logger.info("-" * 35)

    for dim in dims_to_test:
        # Configura o timer com a função a ser testada
        # O uso de uma lambda garante que a função seja chamada com o argumento correto
        timer = timeit.Timer(lambda: build_phi_unitary(dim))
        
        # Executa o timer e obtém o tempo total para as N execuções
        total_time = timer.timeit(number=num_executions)
        
        # Calcula o tempo médio por execução e converte para milissegundos
        avg_time_ms = (total_time / num_executions) * 1000
        
        # Imprime o resultado formatado
        logger.info("%s | %.4f", f"{dim:<12}", avg_time_ms)

    logger.info("\n--- Benchmark Concluído ---")

if __name__ == "__main__":
    run_benchmark()
