"""Indicator calculation utilities for :mod:`nova_estrategia_qualia`."""

from __future__ import annotations

import pandas as pd

from ...indicators import rsi, sma


def compute_sma(prices: pd.Series, window: int) -> pd.Series:
    """Compute Simple Moving Average (SMA)."""
    return pd.Series(sma(prices.values, window), index=prices.index)


def compute_rsi(prices: pd.Series, window: int) -> pd.Series:
    """Compute Relative Strength Index (RSI).

    Parameters
    ----------
    prices : pd.Series
        Price series used for RSI calculation.
    window : int
        Window size or period of the RSI.

    Returns
    -------
    pd.Series
        RSI values aligned with ``prices`` index.
    """

    return pd.Series(rsi(prices.values, window), index=prices.index)
