"""Utilities for integrating risk metacognition with trading components."""

from __future__ import annotations

from datetime import datetime, timezone
from typing import Any, Dict, TYPE_CHECKING
import asyncio

from ..utils.logger import get_logger

logger = get_logger(__name__)

if TYPE_CHECKING:  # pragma: no cover - type checking import
    from ..market.dynamic_risk_controller import RiskCalibrationResult


def _apply_metacognitive_insights(
    result: "RiskCalibrationResult", metacognitive_state: Dict[str, Any]
) -> "RiskCalibrationResult":
    """Apply metacognitive insights to a risk calibration result.

    Args:
        result: Resultado de calibração de risco a ser ajustado.
        metacognitive_state: Estado retornado pelo motor de metacognição.

    Returns:
        O resultado ajustado após aplicação dos insights.
    """
    try:
        active_insights = metacognitive_state.get("active_insights", [])
        consciousness_level = metacognitive_state.get("risk_consciousness_level", 0.0)
        consciousness_factor = 1.0 + (consciousness_level - 0.5) * 0.2

        for insight in active_insights:
            if insight.get("urgency_level") in {"high", "critical"}:
                desc = insight.get("description", "").lower()
                if "volatilidade" in desc:
                    result.stop_loss_distance *= 1.1
                    result.adjustment_reason += (
                        f"; Ajuste metacognitivo: {insight.get('insight_type')}"
                    )
                elif "regime" in desc:
                    regime_adj = 1.05 if result.market_regime == "volatile" else 0.95
                    result.stop_loss_distance *= regime_adj
                    result.adjustment_reason += "; Ajuste regime metacognitivo"
                elif "auto-avalia\u00e7\u00e3o" in desc:
                    result.confidence_level *= 0.9
                    result.adjustment_reason += (
                        "; Confian\u00e7a ajustada (metacogni\u00e7\u00e3o)"
                    )

        result.stop_loss_distance *= consciousness_factor
        result.take_profit_distance *= consciousness_factor

        if result.stop_loss_price is not None:
            current_price = result.stop_loss_price + result.stop_loss_distance
            result.stop_loss_price = current_price - result.stop_loss_distance
            result.take_profit_price = current_price + result.take_profit_distance

        logger.info(
            "Ajustes metacognitivos aplicados: consci\u00eancia=%.3f, insights=%d",
            consciousness_level,
            len(active_insights),
        )

        return result
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error("Erro ao aplicar insights metacognitivos: %s", exc)
        return result


async def process_with_metacognition(
    controller: Any, symbol: str, market_data: Dict[str, Any], current_price: float
) -> "RiskCalibrationResult":
    """Run risk calibration and apply metacognitive feedback.

    Args:
        controller: Instância do ``DynamicRiskController``.
        symbol: Símbolo do ativo.
        market_data: Dados de mercado utilizados na calibração.
        current_price: Preço atual do ativo.

    Returns:
        Resultado da calibração após ajustes metacognitivos.
    """
    result = controller.calibrate_risk_levels(symbol, market_data, current_price)

    if hasattr(controller, "metacognition_engine") and controller.metacognition_engine:
        data = {
            "symbol": symbol,
            "timestamp": datetime.now(timezone.utc),
            "actual_volatility": result.volatility_metric,
            "actual_regime": result.market_regime,
            "risk_calibration": {
                "predicted_volatility": result.volatility_metric,
                "predicted_regime": result.market_regime,
                "stop_distance": result.stop_loss_distance,
                "take_profit_distance": result.take_profit_distance,
                "atr_value": result.atr_value,
                "confidence_level": result.confidence_level,
            },
        }

        timeout = getattr(getattr(controller, "config", {}), "get", lambda k, d: d)(
            "METACOGNITION_TIMEOUT", 5
        )

        try:
            await asyncio.wait_for(
                controller.metacognition_engine.process_market_observation(data),
                timeout,
            )
        except asyncio.TimeoutError:
            logger.warning("process_market_observation timed out")

        try:
            metacognitive_state = await asyncio.wait_for(
                controller.metacognition_engine.get_metacognitive_state(), timeout
            )
        except asyncio.TimeoutError:
            logger.warning("get_metacognitive_state timed out")
            metacognitive_state = {}

        if metacognitive_state.get("active_insights"):
            result = _apply_metacognitive_insights(result, metacognitive_state)

    return result
