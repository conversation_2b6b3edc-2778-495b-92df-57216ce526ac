from __future__ import annotations

"""Funções auxiliares de métricas quânticas para QUALIA."""

from typing import Optional, Sequence, Mapping, Dict
from collections import deque

import numpy as np
from qiskit.quantum_info import Statevector

from ..utils.hardware_acceleration import shannon_entropy

from ..utils.logger import get_logger

metrics_logger = get_logger(__name__)


def calculate_entropy(qiskit_statevector: Optional[Statevector] = None) -> float:
    """Calcula a entropia de Shannon de um :class:`Statevector`.

    Parameters
    ----------
    qiskit_statevector:
        Estado quântico representado por ``Statevector``.

    Returns
    -------
    float
        Entropia de Shannon do estado.
    """
    if qiskit_statevector is None:
        metrics_logger.debug("Statevector é None em calculate_entropy. Retornando 0.0")
        return 0.0

    try:
        state_data = qiskit_statevector.data
        if (
            not isinstance(state_data, np.ndarray)
            or state_data.ndim != 1
            or state_data.size == 0
        ):
            metrics_logger.warning(
                "calculate_entropy: statevector.data não é um array numpy 1D válido ou está vazio."
            )
            return 0.0
    except AttributeError:
        metrics_logger.warning(
            "calculate_entropy: qiskit_statevector não possui atributo .data. Retornando 0.0"
        )
        return 0.0
    except (TypeError, ValueError) as exc:
        metrics_logger.error(
            "calculate_entropy: erro ao acessar statevector.data: %s", exc
        )
        return 0.0

    probabilities = np.abs(state_data) ** 2
    return shannon_entropy(probabilities)


def calculate_coherence(qiskit_statevector: Optional[Statevector] = None) -> float:
    """Calcula a coerência L1 (``l1``-norm of coherence) de um ``Statevector``.

    Parameters
    ----------
    qiskit_statevector:
        Estado quântico utilizado na medição de coerência.

    Returns
    -------
    float
        Valor da coerência L1 do estado.
    """
    if qiskit_statevector is None:
        metrics_logger.debug(
            "Statevector é None em calculate_coherence. Retornando 0.0"
        )
        return 0.0

    try:
        state_data = qiskit_statevector.data
        if (
            not isinstance(state_data, np.ndarray)
            or state_data.ndim != 1
            or state_data.size == 0
        ):
            metrics_logger.warning(
                "calculate_coherence: statevector.data não é um array numpy 1D válido ou está vazio."
            )
            return 0.0
    except AttributeError:
        metrics_logger.warning(
            "calculate_coherence: qiskit_statevector não possui atributo .data. Retornando 0.0"
        )
        return 0.0
    except (TypeError, ValueError) as exc:
        metrics_logger.error(
            "calculate_coherence: erro ao acessar statevector.data: %s", exc
        )
        return 0.0

    abs_amplitudes = np.abs(state_data)
    sum_abs_amplitudes = np.sum(abs_amplitudes)
    sum_sq_abs_amplitudes = np.sum(abs_amplitudes**2)
    l1_coherence = sum_abs_amplitudes**2 - sum_sq_abs_amplitudes
    return float(l1_coherence)


def calculate_von_neumann_entropy(
    qiskit_statevector: Optional[Statevector] = None,
) -> float:
    """Calcula a entropia de von Neumann de um ``Statevector``.

    Parameters
    ----------
    qiskit_statevector:
        Estado quântico utilizado na medição de entropia.

    Returns
    -------
    float
        Entropia de von Neumann do estado.
    """

    if qiskit_statevector is None:
        metrics_logger.debug(
            "Statevector é None em calculate_von_neumann_entropy. Retornando 0.0"
        )
        return 0.0

    try:
        from .entanglement import von_neumann_entropy as _vn

        return float(_vn(qiskit_statevector))
    except Exception as exc:  # pragma: no cover - defensive
        metrics_logger.error(
            "calculate_von_neumann_entropy: falha ao calcular: %s", exc
        )
        return 0.0


def calculate_entanglement(qiskit_statevector: Optional[Statevector] = None) -> float:
    """Calcula o emaranhamento multipartite de um ``Statevector``."""

    if qiskit_statevector is None:
        metrics_logger.debug(
            "Statevector é None em calculate_entanglement. Retornando 0.0"
        )
        return 0.0

    try:
        from .entanglement import multipartite_entanglement as _mp

        return float(_mp(qiskit_statevector))
    except Exception as exc:  # pragma: no cover - defensive
        metrics_logger.error("calculate_entanglement: %s", exc)
        return 0.0


class TranscendencePulseDetector:
    """Detector do Pulso de Transcendência (H4).

    Monitora a série temporal de variações de entropia (Δ‑entropia) e
    dispara quando ocorre uma queda anômala, definida como um valor
    inferior a ``sigma_threshold`` desvios padrão abaixo da média móvel
    da janela.
    """

    def __init__(self, window_size: int = 20, sigma_threshold: float = 3.0) -> None:
        self.window_size = int(window_size)
        self.sigma_threshold = float(sigma_threshold)
        self._history: "deque[float]" = deque(maxlen=self.window_size)

    def update(self, delta_entropy: float) -> bool:
        """Atualiza a janela e verifica se há pulso."""

        if not np.isfinite(delta_entropy):
            return False

        self._history.append(float(delta_entropy))
        if len(self._history) < self.window_size:
            metrics_logger.debug(
                "TranscendencePulseDetector: histórico insuficiente (%s/%s)",
                len(self._history),
                self.window_size,
            )
            return False

        window = np.asarray(self._history, dtype=float)
        mean = float(np.mean(window))
        std = float(np.std(window))
        if std <= 1e-9:
            return False

        z_score = (window[-1] - mean) / std
        triggered = z_score < -self.sigma_threshold
        metrics_logger.debug(
            "Pulse update: delta=%.6f, z=%.3f, triggered=%s",
            delta_entropy,
            z_score,
            triggered,
        )
        return bool(triggered)


def calculate_symbolic_coherence(
    current_entropy: float, token_freq: Mapping[str, int] | Sequence[str]
) -> float:
    """Calcula a coerência simbólica baseada na entropia e na frequência de tokens.

    A distribuição dos tokens é convertida em probabilidades e sua entropia
    normalizada é comparada à entropia ``current_entropy``. A coerência simbólica
    é ``1 - |current_entropy - entropy_freq|`` limitada ao intervalo ``[0, 1]``.

    Parameters
    ----------
    current_entropy:
        Entropia do estado atual normalizada entre ``0`` e ``1``.
    token_freq:
        Frequências observadas dos tokens. Pode ser uma sequência de tokens ou
        um mapeamento ``token`` ``->`` ``contagem``.

    Returns
    -------
    float
        Valor de coerência simbólica normalizado.
    """

    if not token_freq:
        return 0.0

    if isinstance(token_freq, Mapping):
        counts = np.asarray(list(token_freq.values()), dtype=float)
    else:
        freq: Dict[str, int] = {}
        for tok in token_freq:
            freq[tok] = freq.get(tok, 0) + 1
        counts = np.asarray(list(freq.values()), dtype=float)

    total = float(np.sum(counts))
    if total <= 0.0:
        return 0.0

    probs = counts / total
    probs = probs[probs > 0]
    if probs.size <= 1:
        entropy_freq = 0.0
    else:
        entropy_raw = -float(np.sum(probs * np.log2(probs)))
        entropy_freq = entropy_raw / np.log2(len(counts))

    coherence = 1.0 - abs(float(current_entropy) - entropy_freq)
    return float(np.clip(coherence, 0.0, 1.0))
