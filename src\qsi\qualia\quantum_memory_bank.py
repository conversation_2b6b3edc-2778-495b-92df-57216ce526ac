import os
import json
import tempfile
from dataclasses import dataclass, field
from typing import Dict, List, Any


@dataclass
class QuantumMemoryBank:
    """Versão simplificada usada nos testes"""
    file_path: str | None = None
    records: List[Dict[str, float]] = field(default_factory=list)

    def __post_init__(self) -> None:
        if self.file_path is None:
            self.file_path = os.path.join(tempfile.gettempdir(), "qsi_memory.json")
        self._load()

    def _load(self) -> None:
        try:
            with open(self.file_path, "r", encoding="utf-8") as fh:
                data = json.load(fh)
            if isinstance(data, list):
                self.records = [dict(item) for item in data]
        except Exception:
            self.records = []

    def _save(self) -> None:
        try:
            with open(self.file_path, "w", encoding="utf-8") as fh:
                json.dump(self.records, fh, indent=2, ensure_ascii=False)
        except Exception:
            pass

    def add_metrics(self, metrics: Dict[str, float]) -> None:
        self.records.append(metrics)
        self._save()

    def aggregate_metrics(self) -> Dict[str, float]:
        if not self.records:
            return {}
        keys = set().union(*(rec.keys() for rec in self.records))
        sums = {k: 0.0 for k in keys}
        for rec in self.records:
            for k in keys:
                sums[k] += rec.get(k, 0.0)
        return {k: sums[k] / len(self.records) for k in keys}
