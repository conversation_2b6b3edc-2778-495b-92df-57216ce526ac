"""Funções auxiliares para cálculos de trading."""

from __future__ import annotations

from typing import Any, Iterable, Literal, Optional
from datetime import datetime, timezone

from .logger import get_logger

logger = get_logger(__name__)


def _extract_timestamp_utc(obj: Any) -> Optional[datetime]:
    """Return a UTC-aware timestamp extracted from ``obj``.

    Parameters
    ----------
    obj
        Object potentially carrying ``entry_time`` or ``timestamp`` attributes.

    Returns
    -------
    Optional[datetime]
        Parsed timestamp in UTC, or ``None`` when not available or invalid.
    """

    ts = getattr(obj, "entry_time", None) or getattr(obj, "timestamp", None)
    if ts is None:
        return None

    if isinstance(ts, str):
        try:
            ts = datetime.fromisoformat(ts)
        except Exception:
            return None

    if isinstance(ts, datetime):
        if ts.tzinfo is None:
            ts = ts.replace(tzinfo=timezone.utc)
        else:
            ts = ts.astimezone(timezone.utc)
        return ts

    return None


def calculate_trade_pnl(
    entry_price: float,
    exit_price: float,
    size: float,
    fee_pct: float,
    side: Literal["buy", "sell"] = "buy",
) -> float:
    """Calcula o PnL líquido de uma operação considerando taxas.

    Parameters
    ----------
    entry_price : float
        Preço de entrada da operação.
    exit_price : float
        Preço de saída da operação.
    size : float
        Quantidade negociada (em unidades do ativo).
    fee_pct : float
        Percentual de taxa cobrado pela exchange (ex: 0.0026 para 0.26%).
    side : Literal["buy", "sell"], optional
        Direção da operação, por padrão "buy".

    Returns
    -------
    float
        Valor líquido obtido após deduzir as taxas.
    """
    gross_pnl = (
        (exit_price - entry_price) * size
        if side == "buy"
        else ((entry_price - exit_price) * size)
    )
    exit_fee = exit_price * size * fee_pct
    return gross_pnl - exit_fee


def parse_reduction_pct(signal: Any, default: float = 50.0) -> float:
    """Valida e converte percentual de redução de exposição.

    O valor pode ser informado como string terminada em ``%`` ou como número.
    Frações no intervalo ``(0, 1]`` são convertidas para porcentagem. Valores
    fora de ``(0, 100]`` resultam em ``default`` e um aviso no log.

    Parameters
    ----------
    signal : Any
        Objeto potencialmente contendo ``suggested_quantity_specifier``.
    default : float, optional
        Percentual padrão caso o valor informado seja inválido, ``50.0`` por
        padrão.

    Returns
    -------
    float
        Percentual de redução a ser aplicado.
    """

    pct = default
    spec = getattr(signal, "suggested_quantity_specifier", None)

    pct_value: Optional[float]
    if isinstance(spec, str):
        if spec.endswith("%"):
            try:
                pct_value = float(spec.strip("%"))
            except ValueError:
                logger.warning(
                    "Valor de redução inválido '%s', usando %.1f%%", spec, default
                )
                return pct
        else:
            logger.warning(
                "String de redução deve terminar com %s, usando %.1f%%",
                "%",
                default,
            )
            return pct
    elif isinstance(spec, (int, float)):
        pct_value = float(spec)
    else:
        if spec is not None:
            logger.warning(
                "Tipo de redução não reconhecido (%s), usando %.1f%%",
                type(spec).__name__,
                default,
            )
        return pct

    if 0 < pct_value <= 1:
        pct = pct_value * 100
    elif 1 < pct_value <= 100:
        pct = pct_value
    else:
        logger.warning(
            "Percentual fora do intervalo permitido (0, 100]: %.2f, usando %.1f%%",
            pct_value,
            default,
        )
        pct = default

    return pct


def should_reduce_exposure(
    signal: Any,
    positions: list[Any],
    min_confidence: float,
    min_age_minutes: float,
    allow_single_position: bool = False,
) -> bool:
    """Return ``True`` if exposure reduction is recommended.

    Parameters
    ----------
    signal
        Metacognitive signal which may contain a ``confidence`` attribute.
    positions
        Iterable of open positions providing ``entry_time`` or ``timestamp``.
    min_confidence
        Threshold above which ``confidence`` triggers reduction.
    min_age_minutes
        Minimum age in minutes for any position to independently trigger
        reduction.
    allow_single_position
        If ``True`` and exactly one position is open, exposure reduction is
        recommended regardless of confidence or age thresholds.
    """

    confidence = getattr(signal, "confidence", 0.0)
    if confidence >= min_confidence:
        return True

    if allow_single_position and len(positions) == 1:
        return True

    now = datetime.now(timezone.utc)
    for pos in positions:
        ts = _extract_timestamp_utc(pos)
        if ts is None:
            continue

        if (now - ts).total_seconds() >= min_age_minutes * 60:
            return True

    return False


def get_max_position_age_minutes(positions: Iterable[Any]) -> float:
    """Return the maximum age in minutes among the provided positions.

    Parameters
    ----------
    positions
        Iterable of open positions with ``entry_time`` or ``timestamp``.

    Returns
    -------
    float
        Age in minutes of the oldest position. ``0.0`` if none contain a valid
        timestamp.
    """

    now = datetime.now(timezone.utc)
    max_minutes = 0.0

    for pos in positions:
        ts = _extract_timestamp_utc(pos)
        if ts is None:
            continue

        minutes = (now - ts).total_seconds() / 60.0
        if minutes > max_minutes:
            max_minutes = minutes

    return max_minutes
