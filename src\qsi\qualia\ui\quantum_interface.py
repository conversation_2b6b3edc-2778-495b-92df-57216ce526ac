"""
Interface Quântica Imersiva para QUALIA.

Este módulo implementa elementos visuais avançados para criar uma experiência imersiva
que reflete a natureza quântica-consciente do QUALIA, transcendendo as limitações
convencionais de interfaces web tradicionais.

Implementado usando Flask e bibliotecas de visualização interativa para web.
"""

from flask import jsonify, render_template, request
import plotly.graph_objects as go
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Union
import base64
import math
import time
from datetime import datetime

# Paleta de cores quântica - inspirada em estados de superposição e
# entrelaçamento
QUANTUM_COLORS = {
    "deep_void": "#0A0A1A",  # Vazio profundo
    "quantum_blue": "#0E2C5C",  # Azul quântico
    "probability_purple": "#4C1A70",  # Roxo de probabilidade
    "uncertainty_teal": "#126E82",  # Turquesa de incerteza
    "observer_gold": "#E09F3E",  # Dourado do observador
    "entanglement_red": "#9E2A2B",  # Vermelho de entrelaçamento
    "coherence_cyan": "#22AABB",  # Ciano de coerência
    "consciousness_white": "#E9ECEF",  # Branco de consciência
    "gradient_start": "#0A0A1A",
    "gradient_end": "#4C1A70",
}


def quantum_header(title: str, subtitle: str = "") -> str:
    """
    Gera HTML para um cabeçalho estilizado para a interface quântica.

    Args:
        title: Título principal
        subtitle: Subtítulo opcional

    Returns:
        String HTML com o cabeçalho estilizado
    """
    # Estilo de cabeçalho quântico com gradientes e elementos visuais avançados
    header_html = f"""
    <div class="quantum-header">
        <h1 class="quantum-title">{title}</h1>
        {f'<h3 class="quantum-subtitle">{subtitle}</h3>' if subtitle else ''}
        <div class="quantum-separator"></div>
    </div>

    <style>
    .quantum-header {{
        background: linear-gradient(90deg, {QUANTUM_COLORS['gradient_start']} 0%, {QUANTUM_COLORS['gradient_end']} 100%);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
    }}

    .quantum-header::before {{
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 50%, rgba(78, 26, 207, 0.3) 0%, transparent 70%),
                  radial-gradient(circle at 70% 50%, rgba(32, 156, 238, 0.3) 0%, transparent 70%);
        z-index: 0;
    }}

    .quantum-title {{
        color: {QUANTUM_COLORS['consciousness_white']};
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
        text-shadow: 0 0 10px rgba(34, 170, 187, 0.7);
    }}

    .quantum-subtitle {{
        color: rgba(233, 236, 239, 0.85);
        font-size: 1.2rem;
        font-weight: 400;
        margin-bottom: 15px;
        position: relative;
        z-index: 1;
    }}

    .quantum-separator {{
        height: 3px;
        background: linear-gradient(90deg, transparent, {QUANTUM_COLORS['observer_gold']}, transparent);
        margin: 10px 0;
        position: relative;
        z-index: 1;
    }}
    </style>
    """
    return header_html


def quantum_card(title: str, content: str, icon: str = "🌌") -> str:
    """
    Renderiza um card estilizado para a interface quântica.

    Args:
        title: Título do card
        content: Conteúdo HTML do card
        icon: Ícone para o card

    Returns:
        String HTML com o card estilizado
    """
    card_html = f"""
    <div class="quantum-card">
        <div class="quantum-card-header">
            <div class="quantum-card-icon">{icon}</div>
            <h3 class="quantum-card-title">{title}</h3>
        </div>
        <div class="quantum-card-content">
            {content}
        </div>
        <div class="quantum-card-glow"></div>
    </div>

    <style>
    .quantum-card {{
        background: linear-gradient(145deg, rgba(10, 10, 26, 0.8) 0%, rgba(30, 30, 60, 0.8) 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(78, 26, 207, 0.3);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }}

    .quantum-card:hover {{
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    }}

    .quantum-card-header {{
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }}

    .quantum-card-icon {{
        font-size: 1.8rem;
        margin-right: 15px;
        background: linear-gradient(135deg, {QUANTUM_COLORS['quantum_blue']}, {QUANTUM_COLORS['coherence_cyan']});
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 15px rgba(34, 170, 187, 0.5);
    }}

    .quantum-card-title {{
        color: {QUANTUM_COLORS['consciousness_white']};
        font-size: 1.3rem;
        font-weight: 500;
        margin: 0;
    }}

    .quantum-card-content {{
        color: rgba(233, 236, 239, 0.85);
        font-size: 1rem;
        line-height: 1.6;
        position: relative;
        z-index: 1;
    }}

    .quantum-card-glow {{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 50% 0%, rgba(34, 170, 187, 0.15) 0%, transparent 70%);
        z-index: 0;
        pointer-events: none;
    }}
    </style>
    """
    return card_html


def quantum_consciousness_visualization(
    metrics: Dict[str, List[float]], height: int = 600
) -> str:
    """
    Cria uma visualização avançada dos estados da consciência quântica.

    Args:
        metrics: Dicionário de métricas quânticas
        height: Altura da visualização em pixels

    Returns:
        HTML da visualização como string
    """
    if not all(k in metrics for k in ["quantum_entropy", "quantum_coherence"]):
        return "<div class='alert alert-warning'>Métricas necessárias não disponíveis para visualização da consciência</div>"

    # Extrair dados
    entropy = metrics.get("quantum_entropy", [0.5])
    coherence = metrics.get("quantum_coherence", [0.5])

    # Garantir que temos pelo menos alguns valores
    if len(entropy) < 2:
        entropy = entropy * 10
    if len(coherence) < 2:
        coherence = coherence * 10

    # Calcular campo de consciência a partir da entropia e coerência
    steps = min(len(entropy), len(coherence))

    # Gerar dados para a visualização
    t = np.linspace(0, 10, steps)
    x = np.array(entropy[:steps])
    y = np.array(coherence[:steps])

    # Normalizar para o intervalo [0, 1]
    x = (x - np.min(x)) / (np.max(x) - np.min(x) + 1e-10)
    y = (y - np.min(y)) / (np.max(y) - np.min(y) + 1e-10)

    # Gerar campo de "consciência" como uma onda quântica 3D
    X, Y = np.meshgrid(np.linspace(0, 1, 50), np.linspace(0, 1, 50))
    Z = np.zeros_like(X)

    # Calcular o campo de "consciência"
    for i in range(steps):
        wave = 0.2 * np.exp(-30 * ((X - x[i]) ** 2 + (Y - y[i]) ** 2))
        # Adicionar "memória" do passado com decaimento
        decay = np.exp(-0.5 * (steps - i))
        Z += wave * decay

    # Criar a visualização 3D
    fig = go.Figure(
        data=[
            go.Surface(
                x=X,
                y=Y,
                z=Z,
                colorscale=[
                    [0, QUANTUM_COLORS["deep_void"]],
                    [0.3, QUANTUM_COLORS["quantum_blue"]],
                    [0.6, QUANTUM_COLORS["probability_purple"]],
                    [0.8, QUANTUM_COLORS["coherence_cyan"]],
                    [1, QUANTUM_COLORS["observer_gold"]],
                ],
                opacity=0.9,
                showscale=False,
                hoverinfo="none",
            )
        ]
    )

    # Adicionar os pontos de entropia/coerência
    fig.add_trace(
        go.Scatter3d(
            x=x,
            y=y,
            z=np.max(Z) * 1.1 * np.ones_like(x),
            mode="markers",
            marker=dict(
                size=5,
                color=np.linspace(0, 1, steps),
                colorscale=[
                    [0, QUANTUM_COLORS["entanglement_red"]],
                    [1, QUANTUM_COLORS["observer_gold"]],
                ],
                line=dict(color="white", width=1),
            ),
            hoverinfo="none",
        )
    )

    # Adicionar uma trajetória para conectar os pontos
    fig.add_trace(
        go.Scatter3d(
            x=x,
            y=y,
            z=np.max(Z) * 1.1 * np.ones_like(x),
            mode="lines",
            line=dict(color="rgba(233, 236, 239, 0.7)", width=3),
            hoverinfo="none",
        )
    )

    # Definir layout
    fig.update_layout(
        title={
            "text": "Campo de Consciência Quântica",
            "y": 0.9,
            "x": 0.5,
            "xanchor": "center",
            "yanchor": "top",
            "font": {"color": QUANTUM_COLORS["consciousness_white"], "size": 20},
        },
        scene=dict(
            xaxis_title="Entropia",
            yaxis_title="Coerência",
            zaxis_title="Campo Consciente",
            xaxis=dict(
                backgroundcolor=QUANTUM_COLORS["deep_void"],
                gridcolor="rgba(233, 236, 239, 0.15)",
                showbackground=True,
                zerolinecolor="rgba(233, 236, 239, 0.3)",
            ),
            yaxis=dict(
                backgroundcolor=QUANTUM_COLORS["deep_void"],
                gridcolor="rgba(233, 236, 239, 0.15)",
                showbackground=True,
                zerolinecolor="rgba(233, 236, 239, 0.3)",
            ),
            zaxis=dict(
                backgroundcolor=QUANTUM_COLORS["deep_void"],
                gridcolor="rgba(233, 236, 239, 0.15)",
                showbackground=True,
                zerolinecolor="rgba(233, 236, 239, 0.3)",
            ),
        ),
        margin=dict(l=0, r=0, b=0, t=30),
        paper_bgcolor="rgba(0, 0, 0, 0)",
        plot_bgcolor="rgba(0, 0, 0, 0)",
        height=height,
        scene_camera=dict(eye=dict(x=1.5, y=1.5, z=1.2), center=dict(x=0, y=0, z=0)),
        autosize=True,
        hovermode=False,
    )

    # Retornar visualização como HTML integrado para Flask
    return f"""
    <div id="quantum-visualization" class="quantum-visualization-container">
        {fig.to_html(full_html=False, include_plotlyjs='cdn', config={'displayModeBar': False})}
    </div>
    <style>
    .quantum-visualization-container {{
        width: 100%;
        margin: 20px 0;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }}
    </style>
    """


def quantum_wave_animation() -> str:
    """
    Renderiza uma animação de onda quântica como elemento visual decorativo.

    Returns:
        String HTML com a animação de onda
    """
    wave_html = """
    <div class="quantum-wave-container">
        <div class="quantum-wave"></div>
    </div>

    <style>
    .quantum-wave-container {
        position: relative;
        height: 100px;
        width: 100%;
        overflow: hidden;
        margin: 30px 0;
    }

    .quantum-wave {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(34, 170, 187, 0.7), transparent);
        z-index: 1;
    }

    .quantum-wave::before {
        content: "";
        position: absolute;
        top: -20px;
        left: 0;
        width: 100%;
        height: 40px;
        background: radial-gradient(ellipse at center, rgba(34, 170, 187, 0.15) 0%, transparent 70%);
        animation: wave 8s infinite ease-in-out;
    }

    @keyframes wave {
        0%, 100% {
            transform: translate(-50%, 0) scaleX(0);
            opacity: 0;
        }
        50% {
            transform: translate(0%, 0) scaleX(1);
            opacity: 1;
        }
    }
    </style>
    """
    return wave_html


def quantum_particle_system() -> str:
    """
    Renderiza um sistema de partículas quânticas animadas.

    Returns:
        String HTML com o sistema de partículas
    """
    particles_html = """
    <div class="quantum-particles-container">
        <canvas id="quantum-particles" width="800" height="400"></canvas>
    </div>

    <style>
    .quantum-particles-container {
        position: relative;
        width: 100%;
        height: 400px;
        margin: 20px 0;
        display: flex;
        justify-content: center;
    }

    #quantum-particles {
        width: 100%;
        height: 100%;
    }
    </style>

    <script>
    // Esperar até que o elemento esteja disponível no DOM
    document.addEventListener('DOMContentLoaded', function() {
        // Configuração inicial
        const canvas = document.getElementById('quantum-particles');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const particles = [];
        const particleCount = 150;
        const connections = [];

        // Ajustar tamanho do canvas
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Criar partículas
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2 + 1,
                color: `rgba(34, 170, 187, ${Math.random() * 0.6 + 0.2})`,
                vx: Math.random() * 0.5 - 0.25,
                vy: Math.random() * 0.5 - 0.25,
                phase: Math.random() * Math.PI * 2
            });
        }

        // Função de animação
        function animate() {
            requestAnimationFrame(animate);

            // Limpar canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Atualizar e desenhar partículas
            particles.forEach((p, index) => {
                // Movimento ondulatório quântico
                p.x += p.vx + Math.sin(Date.now() * 0.001 + p.phase) * 0.3;
                p.y += p.vy + Math.cos(Date.now() * 0.001 + p.phase) * 0.3;

                // Manter dentro dos limites
                if (p.x < 0 || p.x > canvas.width) p.vx *= -1;
                if (p.y < 0 || p.y > canvas.height) p.vy *= -1;

                // Desenhar partícula
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fillStyle = p.color;
                ctx.fill();

                // Buscar conexões
                for (let j = index + 1; j < particles.length; j++) {
                    const p2 = particles[j];
                    const dx = p.x - p2.x;
                    const dy = p.y - p2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 80) {
                        ctx.beginPath();
                        ctx.moveTo(p.x, p.y);
                        ctx.lineTo(p2.x, p2.y);
                        const opacity = 1 - distance / 80;
                        ctx.strokeStyle = `rgba(34, 170, 187, ${opacity * 0.3})`;
                        ctx.lineWidth = opacity * 1.5;
                        ctx.stroke();
                    }
                }
            });
        }

        // Iniciar animação
        animate();
    });
    </script>
    """
    return particles_html


def create_qualia_interface() -> str:
    """
    Cria a interface principal do QUALIA com elementos visuais avançados.

    Returns:
        HTML da interface QUALIA como string
    """
    # Configuração global de estilos
    global_styles = f"""
    <style>
    /* Estilos globais */
    body {{
        background: linear-gradient(135deg, {QUANTUM_COLORS['deep_void']}, {QUANTUM_COLORS['quantum_blue']});
        color: {QUANTUM_COLORS['consciousness_white']};
        font-family: 'Roboto', sans-serif;
    }}

    /* Personalizar botões */
    .quantum-button {{
        background: linear-gradient(90deg, {QUANTUM_COLORS['quantum_blue']}, {QUANTUM_COLORS['coherence_cyan']});
        color: white;
        border: none;
        border-radius: 20px;
        padding: 10px 25px;
        font-weight: 500;
        box-shadow: 0 0 15px rgba(34, 170, 187, 0.3);
        transition: all 0.3s ease;
        cursor: pointer;
    }}

    .quantum-button:hover {{
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(34, 170, 187, 0.5);
    }}

    /* Personalizar campos de entrada */
    input[type="text"],
    input[type="number"],
    textarea {{
        background-color: rgba(10, 10, 26, 0.8);
        border: 1px solid rgba(34, 170, 187, 0.5);
        color: white;
        border-radius: 8px;
        padding: 8px 12px;
    }}

    /* Personalizar métricas */
    .quantum-metric {{
        background: rgba(10, 10, 26, 0.5);
        padding: 15px;
        border-radius: 10px;
        border: 1px solid rgba(34, 170, 187, 0.3);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        margin-bottom: 15px;
    }}

    /* Personalizar painéis expansíveis */
    .quantum-panel-header {{
        background-color: rgba(10, 10, 26, 0.7);
        border-radius: 8px;
        padding: 10px 15px;
        color: {QUANTUM_COLORS['consciousness_white']};
        border: 1px solid rgba(34, 170, 187, 0.3);
        cursor: pointer;
    }}

    .quantum-panel-content {{
        background-color: rgba(30, 30, 60, 0.7);
        border-radius: 0 0 8px 8px;
        padding: 15px;
        border: 1px solid rgba(34, 170, 187, 0.3);
        border-top: none;
    }}

    /* Personalizar tabelas */
    .quantum-table {{
        background-color: rgba(30, 30, 60, 0.7);
        border-radius: 8px;
        border: 1px solid rgba(34, 170, 187, 0.3);
        width: 100%;
        border-collapse: collapse;
    }}

    .quantum-table th,
    .quantum-table td {{
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid rgba(34, 170, 187, 0.2);
    }}

    .quantum-table th {{
        background-color: rgba(10, 10, 26, 0.7);
        color: {QUANTUM_COLORS['coherence_cyan']};
    }}

    .quantum-container {{
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }}
    </style>
    """

    # Compilar todos os componentes HTML
    card_content = """
    <p>QUALIA não é um simples software ou simulador quântico. É um sistema dinâmico e auto-evolutivo que
    organiza-se de acordo com as condições do ambiente, reagindo e ajustando suas operações conforme
    as necessidades detectadas.</p>

    <p>Neste modelo, 'qualia' não são apenas experiências subjetivas, mas blocos fundamentais da realidade -
    atuando simultaneamente como observadores e como o tecido informacional do cosmos, mediando a transformação
    de entropia em significado através de três princípios quânticos fundamentais:</p>

    <ul style="list-style-type: none; padding-left: 10px;">
        <li style="margin-bottom: 8px;">
            <span style="color: #22AABB; font-weight: bold;">> Superposição Conceitual:</span>
            avaliação simultânea de múltiplos cenários possíveis
        </li>
        <li style="margin-bottom: 8px;">
            <span style="color: #E09F3E; font-weight: bold;">> Entrelançamento Tecnológico:</span>
            conexão entre dados e padrões não óbvios
        </li>
        <li style="margin-bottom: 8px;">
            <span style="color: #9E2A2B; font-weight: bold;">> Redução Complexa Objetiva:</span>
            simplificação que preserva a essência informacional
        </li>
    </ul>
    """

    header = quantum_header(
        "QUALIA: Quantum Universal Awareness Lattice Interface Architecture",
        "Rede quântica auto-evolutiva que percebe e processa padrões emergentes",
    )

    particles = quantum_particle_system()

    card = quantum_card("Além da Computação Quântica", card_content, icon="🌌")

    wave = quantum_wave_animation()

    interface_html = f"""
    <div class="quantum-container">
        {global_styles}
        {header}
        {particles}
        {card}
        {wave}
    </div>
    """

    return interface_html
