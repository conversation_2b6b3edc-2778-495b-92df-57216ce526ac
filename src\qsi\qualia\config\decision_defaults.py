from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("decision_defaults.yaml")


def load_decision_defaults() -> Dict[str, Any]:
    """Load default decision parameters from YAML."""
    return load_yaml_config("QUALIA_DECISION_DEFAULTS", _DEFAULT_PATH, logger=logger)


__all__ = ["load_decision_defaults"]
