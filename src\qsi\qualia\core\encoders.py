# coding: utf-8
"""
Quantum Encoders for QUALIA System

Este módulo define a interface para encoders quânticos e implementações
específicas para transformar dados clássicos de mercado em representações
vetoriais (normalizadas) para futura aplicação em circuitos quânticos.
"""
from abc import ABC, abstractmethod
from typing import (
    Any,
    Dict,
    Union,
    List,
    Optional,
    Sequence,
    TypeVar,
    Generic,
    Tuple,
)

try:
    from typing import TypedDict  # Python 3.8+
except ImportError:
    from typing_extensions import TypedDict  # Fallback para versões anteriores

import numpy as np

try:
    import cupy as cp  # type: ignore

    _CUPY_AVAILABLE = True
except Exception:  # pragma: no cover - optional dependency
    cp = None  # type: ignore
    _CUPY_AVAILABLE = False

from qiskit import QuantumCircuit
from qiskit_aer import Aer
from datetime import datetime, timezone
import time
from contextlib import nullcontext

try:  # pragma: no cover - optional dependency
    from datadog import DogStatsd
except Exception:  # pragma: no cover - datadog not installed

    class DogStatsd:  # type: ignore[misc]
        def timer(self, *args: Any, **kwargs: Any):
            return nullcontext()

        def increment(self, *args: Any, **kwargs: Any) -> None:
            pass


from typing import TYPE_CHECKING
from ..utils.tracing import get_tracer

if TYPE_CHECKING:  # pragma: no cover - avoid circular imports at runtime
    from ..memory.event_bus import SimpleEventBus

# Qiskit não é mais diretamente usado nos encoders individuais para aplicar rotações.
# Apenas numpy para retornar o vetor de estado.
# from qiskit import QuantumCircuit
import logging
from ..utils.logger import get_logger
from ..utils.logging_initializer import initialize_logging
from ..events import EncoderStartedEvent, EncoderFinishedEvent

# Configuração do logger para este módulo
encoder_logger = get_logger(__name__)  # __name__ será src.qualia.core.encoders
# Permite que logs sejam capturados por handlers configurados no root logger
# (por exemplo, via pytest.caplog). Mantê-lo propagando evita que mensagens
# se percam caso nenhum handler específico seja adicionado aqui.
encoder_logger.propagate = True

# Limits for circuit complexity when building batch circuits
MAX_CIRCUIT_DEPTH = 32
MAX_CIRCUIT_OPERATIONS = 64

# Tipos de Snapshot de Entrada


class OrderBookSnapshot(TypedDict):
    bids: List[tuple[float, float]]  # [(price, size), ...]
    asks: List[tuple[float, float]]
    timestamp: float  # epoch ms
    # Adicionar raio N opcional para cálculo de OBI, ou assumir que bids/asks já são filtrados
    # mid_price_radius_levels: Optional[int] = None


class LiquidityVelocityData(TypedDict):
    # Exemplo: velocidade da liquidez já processada e normalizada externamente
    # ou o encoder faz o cálculo a partir de dados mais brutos se a spec evoluir.
    # Por agora, seguindo a ideia de receber o valor numérico principal.
    lv_metric: float
    timestamp: float


class FundingRateDeviationData(TypedDict):
    # Exemplo: desvio da taxa de funding
    frd_metric: float
    timestamp: float


class PriceMomentumData(TypedDict):
    price_percentage_change: float
    timestamp: float


class VolatilityData(TypedDict):
    volatility_metric: float
    timestamp: float


class VolumeRatioData(TypedDict):
    """Dados de relação de volume para o encoder de volume."""

    volume_ratio: float  # Relação de volume (ex: volume atual / volume médio)
    timestamp: float


class RSIData(TypedDict):
    """Dados de RSI para o encoder de fase."""

    rsi: float  # RSI no intervalo [0, 100]
    timestamp: float


class TimeSeriesEmbeddingData(TypedDict):
    """Dados de embedding para séries temporais."""

    embedding: List[float]
    timestamp: float


# Tipo genérico para o snapshot de entrada que cada encoder pode especializar
InputSnapshotType = TypeVar("InputSnapshotType", bound=Dict[str, Any])
# Tipo para o vetor de saída (NDArrayFloat não é um tipo padrão, usando
# np.ndarray)
NDArrayFloat = np.ndarray


class QuantumEncoder(Generic[InputSnapshotType], ABC):
    """
    Classe base abstrata para todos os encoders quânticos.
    Cada encoder é responsável por transformar um snapshot específico de
    informação clássica de mercado em um vetor de estado normalizado (np.ndarray)
    ou fornecer parâmetros para uma operação quântica.
    """

    def __init__(
        self,
        name: str = "BaseEncoder",
        *,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional["SimpleEventBus"] = None,
        use_hardware_acceleration: bool = False,
    ) -> None:
        self.name = name
        self.output_dim: int = (
            2  # Cada encoder produz um vetor 2D [cos, sin] - pode ser obsoleto com get_quantum_operation
        )
        # YAA CORREÇÃO: Adicionado atributos internos para tracking de estado
        self._internal_rotation_angle: Optional[float] = None
        self._last_valid_snapshot: Optional[Dict[str, Any]] = None
        self._fallback_count: int = 0
        self.statsd: DogStatsd | None = statsd_client
        self.event_bus: Optional["SimpleEventBus"] = event_bus
        self.use_hardware_acceleration = use_hardware_acceleration
        self.tracer = get_tracer(__name__)
        encoder_logger.info(f"QuantumEncoder '{self.name}' inicializado.")

    def _validate_snapshot_structure(self, snapshot: Dict[str, Any]) -> bool:
        """YAA CORREÇÃO: Validação rigorosa de estrutura de snapshot."""
        if not isinstance(snapshot, dict):
            encoder_logger.warning(
                f"Encoder '{self.name}': snapshot não é um dict: {type(snapshot)}"
            )
            return False

        # Verificar campos básicos obrigatórios
        required_fields = ["timestamp"]
        for field in required_fields:
            if field not in snapshot:
                encoder_logger.debug(
                    f"Encoder '{self.name}': campo obrigatório '{field}' ausente"
                )
                return False

        # Verificar se tem dados utilizáveis
        has_metrics_flat = "metrics_flat" in snapshot and isinstance(
            snapshot["metrics_flat"], dict
        )
        has_direct_data = any(key in snapshot for key in getattr(self, "data_keys", []))

        if not (has_metrics_flat or has_direct_data):
            encoder_logger.debug(
                f"Encoder '{self.name}': nem metrics_flat nem dados diretos encontrados"
            )
            return False

        return True

    def _extract_data_from_snapshot(
        self, snapshot: Dict[str, Any], key: str
    ) -> Optional[float]:
        """YAA CORREÇÃO: Extração robusta de dados do snapshot."""
        # Tentar metrics_flat primeiro
        if "metrics_flat" in snapshot and isinstance(snapshot["metrics_flat"], dict):
            if key in snapshot["metrics_flat"]:
                value = snapshot["metrics_flat"][key]
                if isinstance(value, (int, float)) and not np.isnan(value):
                    return float(value)

        # Tentar dados diretos
        if key in snapshot:
            value = snapshot[key]
            if isinstance(value, (int, float)) and not np.isnan(value):
                return float(value)

        # Tentar variações da chave
        alternative_keys = [
            key.replace("_1m_", "_"),
            key.replace("_5m_", "_"),
            key.split("_")[-1],  # última parte da chave
        ]

        for alt_key in alternative_keys:
            if "metrics_flat" in snapshot and alt_key in snapshot["metrics_flat"]:
                value = snapshot["metrics_flat"][alt_key]
                if isinstance(value, (int, float)) and not np.isnan(value):
                    return float(value)

        return None

    def _get_fallback_state(self) -> np.ndarray:
        """YAA CORREÇÃO: Estado de fallback mais inteligente."""
        self._fallback_count += 1

        xp = cp if self.use_hardware_acceleration and _CUPY_AVAILABLE else np
        vec = xp.array([1.0, 0.0], dtype=xp.float32)
        if xp is cp:  # pragma: no cover - optional path
            vec = cp.asnumpy(vec)
        return np.asarray(vec, dtype=np.float32)

    def _validate_quantum_normalization(
        self, amplitude_vector: np.ndarray, tolerance: float = 1e-10
    ) -> np.ndarray:
        """
        YAA CORREÇÃO: Valida e corrige a normalização quântica de um vetor de amplitudes.

        Args:
            amplitude_vector: Vetor de amplitudes a ser validado
            tolerance: Tolerância para verificação de normalização

        Returns:
            np.ndarray: Vetor de amplitudes devidamente normalizado

        Raises:
            ValueError: Se o vetor não puder ser normalizado
        """
        if amplitude_vector is None or len(amplitude_vector) == 0:
            encoder_logger.warning(
                f"Encoder '{self.name}': vetor de amplitudes vazio, usando fallback"
            )
            return self._get_fallback_state()

        # Converter para array numpy se necessário
        xp = cp if self.use_hardware_acceleration and _CUPY_AVAILABLE else np
        amplitude_vector = xp.asarray(amplitude_vector, dtype=xp.float64)

        # Calcular norma quadrada
        norm_squared = xp.sum(amplitude_vector**2)

        # Verificar se a norma é válida
        if float(norm_squared) <= 1e-12:
            encoder_logger.warning(
                f"Encoder '{self.name}': norma muito pequena ({norm_squared:.2e}), usando fallback"
            )
            return self._get_fallback_state()

        # Normalizar se necessário
        if not xp.isclose(norm_squared, 1.0, atol=tolerance):
            amplitude_vector = amplitude_vector / xp.sqrt(norm_squared)
            encoder_logger.debug(
                f"Encoder '{self.name}': vetor normalizado de {norm_squared:.12f} para 1.0"
            )

        # Verificação final
        final_norm_squared = xp.sum(amplitude_vector**2)
        if not xp.isclose(final_norm_squared, 1.0, atol=tolerance):
            encoder_logger.error(
                f"Encoder '{self.name}': FALHA CRÍTICA na normalização! "
                f"Norm² = {final_norm_squared:.12f}, esperado 1.0"
            )
            raise ValueError(
                f"Impossível normalizar vetor: norm² = {final_norm_squared}"
            )

        if xp is cp:  # pragma: no cover - optional path
            amplitude_vector = cp.asnumpy(amplitude_vector)
        return np.asarray(amplitude_vector, dtype=np.float32)

    @abstractmethod
    def _encode_single(self, snapshot: InputSnapshotType) -> NDArrayFloat:
        """
        Processa um único snapshot de dados e retorna o vetor de estado [cos_angle, sin_angle].
        Este método deve ser implementado por subclasses e deve sempre retornar
        um np.ndarray de shape (self.output_dim,), mesmo em caso de fallback.
        Mantido para compatibilidade e uso potencial em outros contextos.
        """
        pass

    @abstractmethod
    def get_quantum_operation(
        self, snapshot: InputSnapshotType
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """
        Retorna o tipo de operação quântica, seus parâmetros e os índices dos qubits alvo (relativos ao encoder, tipicamente [0] para encoders de 1 qubit).
        Ex: ('ry', [theta], [0]) ou ('initialize', [alpha, beta], [0])
        Retorna None se nenhuma operação deve ser aplicada ou em caso de erro.
        Os target_qubits referem-se aos qubits que o encoder usaria se operasse isoladamente (ex: [0] para um encoder de 1 qubit).
        """
        pass

    def encode(
        self, snapshot_or_batch: Union[InputSnapshotType, Sequence[InputSnapshotType]]
    ) -> NDArrayFloat:
        """Codifica um único snapshot ou um batch de snapshots.

        Para batches, tenta utilizar o método ``_encode_batch`` da subclasse
        caso ele esteja implementado. Caso contrário, processa cada item
        sequencialmente, preservando o comportamento anterior.
        """

        tags = [f"class:{self.__class__.__name__}"]
        payload = EncoderStartedEvent(name=self.name, cls=self.__class__.__name__)
        if self.event_bus:
            self.event_bus.publish("encoder.started", payload)

        span_cm = self.tracer.start_as_current_span(f"{self.__class__.__name__}.encode")
        timer_cm = (
            self.statsd.timer("encoder.encode_ms", tags=tags)
            if self.statsd
            else nullcontext()
        )

        result: NDArrayFloat
        with span_cm, timer_cm:
            if isinstance(snapshot_or_batch, Sequence) and not isinstance(
                snapshot_or_batch, str
            ):
                num_snapshots = len(snapshot_or_batch)
                if num_snapshots == 0:
                    encoder_logger.warning(
                        f"Encoder '{self.name}' recebeu um batch vazio. Retornando array vazio de shape (0, {self.output_dim})."
                    )
                    result = np.empty((0, self.output_dim), dtype=np.float32)
                else:
                    batch_result: Optional[np.ndarray] = None
                    if hasattr(self, "_encode_batch"):
                        try:
                            batch_result = getattr(self, "_encode_batch")(
                                snapshot_or_batch
                            )
                        except Exception as e:  # Fallback para qualquer erro no vetor
                            encoder_logger.exception(
                                f"Encoder '{self.name}': erro em _encode_batch: {e}. Revertendo para processamento sequencial."
                            )
                    if batch_result is not None:
                        result = batch_result
                    else:
                        output_array = np.empty(
                            (num_snapshots, self.output_dim), dtype=np.float32
                        )
                        for i, snapshot in enumerate(snapshot_or_batch):
                            try:
                                if not self._validate_snapshot_structure(snapshot):
                                    output_array[i] = self._get_fallback_state()
                                    continue

                                encoded_vector = self._encode_single(snapshot)
                                if encoded_vector.shape == (self.output_dim,):
                                    output_array[i] = encoded_vector
                                else:
                                    encoder_logger.error(
                                        f"Encoder '{self.name}': _encode_single para {snapshot} retornou shape inesperado {encoded_vector.shape}. Usando fallback."
                                    )
                                    output_array[i] = self._get_fallback_state()
                            except Exception as e:
                                encoder_logger.exception(
                                    f"Encoder '{self.name}': Erro em _encode_single para {snapshot}: {e}. Usando fallback."
                                )
                                output_array[i] = self._get_fallback_state()
                        result = output_array

            elif isinstance(snapshot_or_batch, dict):
                if not self._validate_snapshot_structure(snapshot_or_batch):
                    result = self._get_fallback_state()
                else:
                    result = self._encode_single(snapshot_or_batch)
            else:
                encoder_logger.error(
                    f"Encoder '{self.name}' recebeu entrada de tipo inválido: {type(snapshot_or_batch)}. Esperava Dict ou Sequence[Dict]. Retornando estado base."
                )
                result = self._get_fallback_state()

        if self.statsd:
            self.statsd.increment("encoder.encode_count", tags=tags)
        if self.event_bus:
            finished = EncoderFinishedEvent(name=self.name, cls=self.__class__.__name__)
            self.event_bus.publish("encoder.finished", finished)

        return result

    def get_name(self) -> str:
        return self.name

    def __repr__(self) -> str:
        # qubits_required não existe mais no self
        return f"<{self.__class__.__name__}(name='{self.name}')>"


class PriceMomentumEncoder(
    QuantumEncoder[PriceMomentumData]
):  # Especializar com o tipo esperado

    def __init__(
        self,
        name: str = "PriceMomentumEncoder",
        scaling_factor: float = 1.0,
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.scaling_factor = scaling_factor
        # YAA CORREÇÃO: Garantir data_keys sempre disponível
        self.data_keys = data_keys or ["price_percentage_change"]
        self.target_qubits = target_qubits or [0]
        self.n_qubits_required = len(self.target_qubits)

        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com scaling_factor={self.scaling_factor}, "
            f"data_keys={self.data_keys}, target_qubits={self.target_qubits}, "
            f"n_qubits_required={self.n_qubits_required}."
        )

    def _encode_single(self, data_snapshot: PriceMomentumData) -> NDArrayFloat:
        """YAA CORREÇÃO: Implementação robusta com validação."""
        # Tentar extrair dados usando método robusto
        primary_key = self.data_keys[0] if self.data_keys else "price_percentage_change"

        # YAA CORREÇÃO: Usar novo método de extração
        if isinstance(data_snapshot, dict):
            price_change = self._extract_data_from_snapshot(data_snapshot, primary_key)

            # Fallbacks adicionais para price change
            if price_change is None:
                fallback_keys = [
                    "price_percentage_change",
                    "price_change",
                    "percentage_change",
                    "pct_change",
                ]
                for fallback_key in fallback_keys:
                    price_change = self._extract_data_from_snapshot(
                        data_snapshot, fallback_key
                    )
                    if price_change is not None:
                        break
        else:
            # Snapshot direto (formato antigo)
            price_change = data_snapshot.get("price_percentage_change")

        if price_change is None:
            encoder_logger.debug(
                f"Encoder '{self.name}': nenhum price_change encontrado no snapshot. Usando fallback."
            )
            return self._get_fallback_state()

        try:
            # Aplicar scaling e normalização
            scaled_change = float(price_change) * self.scaling_factor
            # Mapear para [0, 2π) com clipping suave
            normalized_angle = np.tanh(scaled_change) * np.pi

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = normalized_angle

            cos_angle = np.cos(normalized_angle)
            sin_angle = np.sin(normalized_angle)

            return np.array([cos_angle, sin_angle], dtype=np.float32)

        except (ValueError, TypeError) as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro ao processar price_change={price_change}: {e}"
            )
            return self._get_fallback_state()

    def _encode_batch(self, snapshots: Sequence[PriceMomentumData]) -> NDArrayFloat:
        """YAA CORREÇÃO: Processamento em batch otimizado."""
        num_snapshots = len(snapshots)
        output_array = np.empty((num_snapshots, self.output_dim), dtype=np.float32)

        primary_key = self.data_keys[0] if self.data_keys else "price_percentage_change"

        for i, snapshot in enumerate(snapshots):
            try:
                if not self._validate_snapshot_structure(snapshot):
                    output_array[i] = self._get_fallback_state()
                    continue

                price_change = self._extract_data_from_snapshot(snapshot, primary_key)
                if price_change is None:
                    output_array[i] = self._get_fallback_state()
                    continue

                scaled_change = float(price_change) * self.scaling_factor
                normalized_angle = np.tanh(scaled_change) * np.pi

                output_array[i] = np.array(
                    [np.cos(normalized_angle), np.sin(normalized_angle)],
                    dtype=np.float32,
                )

            except Exception as e:
                encoder_logger.warning(
                    f"Encoder '{self.name}': erro no batch index {i}: {e}"
                )
                output_array[i] = self._get_fallback_state()

        return output_array

    def get_quantum_operation(
        self, data_snapshot: PriceMomentumData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """YAA CORREÇÃO: Retorno garantido de operação quântica."""
        try:
            # Validar snapshot primeiro
            if not self._validate_snapshot_structure(data_snapshot):
                # Retornar operação neutra
                return ("ry", [0.1], self.target_qubits)

            primary_key = (
                self.data_keys[0] if self.data_keys else "price_percentage_change"
            )
            price_change = self._extract_data_from_snapshot(data_snapshot, primary_key)

            if price_change is None:
                return ("ry", [0.1], self.target_qubits)

            # Calcular ângulo
            scaled_change = float(price_change) * self.scaling_factor
            theta = np.tanh(scaled_change) * np.pi

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = theta

            return ("ry", [theta], self.target_qubits)

        except Exception as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro em get_quantum_operation: {e}"
            )
            return ("ry", [0.1], self.target_qubits)


class VolatilityEncoder(QuantumEncoder[VolatilityData]):  # Snapshot genérico
    """
    Codifica a volatilidade (ex: desvio padrão normalizado).
    Fornece uma operação 'ry' com o ângulo de rotação calculado.
    """

    def __init__(
        self,
        name: str = "VolatilityEncoder",
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.data_keys = data_keys if data_keys else []
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        # Consumir kwargs não utilizados
        for key, value in kwargs.items():
            encoder_logger.debug(
                f"Encoder '{self.name}' recebeu param extra '{key}' no __init__."
            )

        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com data_keys={self.data_keys}, target_qubits={self.target_qubits}, n_qubits_required={self.n_qubits_required}."
        )
        # Validação de n_qubits_required removida
        self._internal_rotation_angle: Optional[float] = None

    def _encode_single(self, data_snapshot: VolatilityData) -> NDArrayFloat:
        """
        Processa um único VolatilityData.
        Retorna np.ndarray([cos_angle, sin_angle]).
        Calcula e armazena self._internal_rotation_angle.
        """
        self._internal_rotation_angle = None  # Reset

        # YAA CORREÇÃO: Usar extração robusta de dados
        if not self._validate_snapshot_structure(data_snapshot):
            encoder_logger.warning(
                f"Encoder '{self.name}': Snapshot inválido, usando fallback"
            )
            return self._get_fallback_state()

        # Tentar extrair dados usando método robusto
        key_to_use = self.data_keys[0] if self.data_keys else "volatility_metric"
        volatility_metric = self._extract_data_from_snapshot(data_snapshot, key_to_use)

        if volatility_metric is None:
            encoder_logger.warning(
                f"Encoder '{self.name}': '{key_to_use}' não encontrada no snapshot, usando fallback"
            )
            # YAA CORREÇÃO: Definir ângulo mesmo em fallback
            self._internal_rotation_angle = 0.0
            return self._get_fallback_state()

        try:
            # Mapear volatilidade (0-1) para um ângulo de rotação Ry [0, pi/2]
            # (para cobrir um quadrante)
            normalized_volatility = np.clip(float(volatility_metric), 0.0, 1.0)
            rotation_angle = normalized_volatility * (np.pi / 2.0)
            self._internal_rotation_angle = (
                rotation_angle  # Store for get_quantum_operation
            )

            encoder_logger.debug(
                f"Encoder '{self.name}': Dado entrada {key_to_use}={volatility_metric:.6f}, "
                f"Ângulo calculado={rotation_angle:.6f}"
            )

            vector = np.array(
                [np.cos(rotation_angle), np.sin(rotation_angle)], dtype=np.float32
            )
            return self._validate_quantum_normalization(vector)

        except Exception as e:
            encoder_logger.exception(
                f"Encoder {self.name} ({self.__class__.__name__}) falhou durante a codificação de {key_to_use}={volatility_metric}: {e}"
            )
            # YAA CORREÇÃO: Definir ângulo mesmo em caso de erro
            self._internal_rotation_angle = 0.0
            return self._get_fallback_state()

    def get_quantum_operation(
        self, data_snapshot: VolatilityData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """
        Retorna a operação Ry e o ângulo de rotação calculado por _encode_single.
        Chama _encode_single para garantir que _internal_rotation_angle seja calculado com o snapshot atual.
        """
        self._encode_single(
            data_snapshot
        )  # Garante que _internal_rotation_angle é calculado/atualizado

        if self._internal_rotation_angle is not None:
            # Para um encoder de 1 qubit, o target_qubit relativo é [0]
            return ("ry", [self._internal_rotation_angle], [0])
        else:
            encoder_logger.warning(
                f"Encoder '{self.name}': _internal_rotation_angle não foi definido após _encode_single. Não é possível fornecer operação quântica."
            )
            return None


class OBIEncoder(QuantumEncoder[OrderBookSnapshot]):
    """
    Codifica o Order-Book Imbalance (OBI) de um OrderBookSnapshot.
    Retorna um vetor normalizado np.ndarray [cos θ, sin θ].
    """

    def __init__(
        self,
        name: str = "OBIEncoder",
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.data_keys = data_keys if data_keys else []
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        self._internal_rotation_angle: Optional[float] = None
        for key, value in kwargs.items():
            encoder_logger.debug(
                f"Encoder '{self.name}' recebeu param extra '{key}' no __init__."
            )
        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com data_keys={self.data_keys}, target_qubits={self.target_qubits}, n_qubits_required={self.n_qubits_required}."
        )

    def get_quantum_operation(
        self, snapshot: OrderBookSnapshot
    ) -> Optional[tuple[str, list[float], list[int]]]:
        """
        Retorna a operação Ry e o ângulo de rotação calculado por _encode_single.
        """
        self._internal_rotation_angle = None
        vector = self._encode_single(snapshot)
        if (
            hasattr(self, "_internal_rotation_angle")
            and self._internal_rotation_angle is not None
        ):
            return ("ry", [self._internal_rotation_angle], [0])
        else:
            encoder_logger.warning(
                f"Encoder '{self.name}': _internal_rotation_angle não foi definido após _encode_single. Não é possível fornecer operação quântica."
            )
            return None

    def _calculate_obi_from_snapshot(self, snapshot: OrderBookSnapshot) -> float:
        """Calcula o Order Book Imbalance a partir de um snapshot."""
        bids_data = snapshot.get("bids", [])
        asks_data = snapshot.get("asks", [])

        total_bid_volume = 0.0
        for item in bids_data:
            if isinstance(item, (list, tuple)) and len(item) == 2:
                try:
                    size = float(item[1])
                    if size < 0:  # Volume não pode ser negativo
                        encoder_logger.warning(
                            f"Volume de bid negativo ignorado: {size} no snapshot ts={snapshot.get('timestamp')}"
                        )
                        continue
                    total_bid_volume += size
                except (TypeError, ValueError):
                    encoder_logger.warning(
                        f"Item de bid com valor de size inválido ignorado: {item} no snapshot ts={snapshot.get('timestamp')}"
                    )
            else:
                encoder_logger.warning(
                    f"Item de bid com estrutura inesperada ignorado: {item} no snapshot ts={snapshot.get('timestamp')}"
                )

        total_ask_volume = 0.0
        for item in asks_data:
            if isinstance(item, (list, tuple)) and len(item) == 2:
                try:
                    size = float(item[1])
                    if size < 0:  # Volume não pode ser negativo
                        encoder_logger.warning(
                            f"Volume de ask negativo ignorado: {size} no snapshot ts={snapshot.get('timestamp')}"
                        )
                        continue
                    total_ask_volume += size
                except (TypeError, ValueError):
                    encoder_logger.warning(
                        f"Item de ask com valor de size inválido ignorado: {item} no snapshot ts={snapshot.get('timestamp')}"
                    )
            else:
                encoder_logger.warning(
                    f"Item de ask com estrutura inesperada ignorado: {item} no snapshot ts={snapshot.get('timestamp')}"
                )

        if total_bid_volume + total_ask_volume == 0:
            encoder_logger.warning(
                f"Encoder '{self.name}': Volume total (bid+ask) é zero para snapshot ts={snapshot.get('timestamp')}. Não é possível calcular OBI."
            )
            return 0.0  # Retorna OBI neutro, resultará em estado base [1,0]

        obi_value = (total_bid_volume - total_ask_volume) / (
            total_bid_volume + total_ask_volume
        )
        return np.clip(obi_value, -1.0, 1.0)

    def _encode_single(self, snapshot: OrderBookSnapshot) -> NDArrayFloat:
        """
        Processa um único OrderBookSnapshot e retorna o vetor [cos θ, sin θ].
        """
        # Reset _internal_rotation_angle
        self._internal_rotation_angle = None

        # Validação preliminar do snapshot
        if not isinstance(snapshot, dict) or not all(
            k in snapshot for k in ["bids", "asks", "timestamp"]
        ):
            encoder_logger.warning(
                f"Encoder '{self.name}' snapshot de entrada não é um dicionário válido ou faltam chaves essenciais (bids, asks, timestamp): {snapshot}. Retornando estado base."
            )
            return np.array([1.0, 0.0])

        # Validação mais detalhada dos tipos das chaves
        if not isinstance(snapshot.get("bids"), list) or not isinstance(
            snapshot.get("asks"), list
        ):
            encoder_logger.warning(
                f"Encoder '{self.name}' campos 'bids' ou 'asks' não são listas no snapshot: {snapshot}. Retornando estado base."
            )
            return np.array([1.0, 0.0])

        try:
            # Calcular OBI
            order_book_imbalance = self._calculate_obi_from_snapshot(snapshot)

            # Mapear obi ∈ [-1,1] → rotação θ = obi * π/2. (Conforme spec 6.1)
            theta = order_book_imbalance * (np.pi / 2.0)

            # Armazenar ângulo para get_quantum_operation
            self._internal_rotation_angle = theta

            vector = np.array([np.cos(theta), np.sin(theta)])

            encoder_logger.debug(
                f"Encoder '{self.name}': Snapshot ts={snapshot.get('timestamp')}, OBI={order_book_imbalance:.6f}, "
                f"Theta={theta:.6f}, Vetor gerado={vector}"
            )
            return vector

        except Exception as e:
            encoder_logger.exception(
                f"Encoder {self.name} ({self.__class__.__name__}) falhou durante a codificação do snapshot: {snapshot}: {e}"
            )
            return np.array([1.0, 0.0])  # RF-03 Fallback


class LiquidityVelocityEncoder(QuantumEncoder[LiquidityVelocityData]):
    """
    Codifica a Velocidade da Liquidez (normalizada) de LiquidityVelocityData.
    Retorna um vetor normalizado np.ndarray [cos θ, sin θ].
    """

    def __init__(
        self,
        name: str = "LiquidityVelocityEncoder",
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.data_keys = data_keys if data_keys else []
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        self._internal_rotation_angle: Optional[float] = None
        for key, value in kwargs.items():
            encoder_logger.debug(
                f"Encoder '{self.name}' recebeu param extra '{key}' no __init__."
            )
        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com data_keys={self.data_keys}, target_qubits={self.target_qubits}, n_qubits_required={self.n_qubits_required}."
        )

    def get_quantum_operation(
        self, snapshot: LiquidityVelocityData
    ) -> Optional[tuple[str, list[float], list[int]]]:
        """
        Retorna a operação Ry e o ângulo de rotação calculado por _encode_single.
        """
        self._internal_rotation_angle = None
        vector = self._encode_single(snapshot)
        if (
            hasattr(self, "_internal_rotation_angle")
            and self._internal_rotation_angle is not None
        ):
            return ("ry", [self._internal_rotation_angle], [0])
        else:
            encoder_logger.warning(
                f"Encoder '{self.name}': _internal_rotation_angle não foi definido após _encode_single. Não é possível fornecer operação quântica."
            )
            return None

    def _encode_single(self, snapshot: LiquidityVelocityData) -> NDArrayFloat:
        """
        Processa um único LiquidityVelocityData.
        Assume que 'lv_metric' já está normalizada (ex: 0 a 1).
        """
        # Reset _internal_rotation_angle
        self._internal_rotation_angle = None

        try:
            if not isinstance(snapshot, dict) or "lv_metric" not in snapshot:
                encoder_logger.warning(
                    f"Encoder '{self.name}' recebeu snapshot malformado ou sem 'lv_metric': {snapshot}. Retornando estado base."
                )
                return np.array([1.0, 0.0])

            liquidity_velocity = snapshot["lv_metric"]
            if not isinstance(liquidity_velocity, (float, int)):
                encoder_logger.warning(
                    f"Encoder '{self.name}': 'lv_metric' não é float ou int em {snapshot}. Retornando estado base."
                )
                return np.array([1.0, 0.0])

            # Clip para garantir que está no intervalo esperado [0, 1]
            normalized_velocity = np.clip(liquidity_velocity, 0.0, 1.0)

            # Mapear para ângulo em [0, pi/2] para Ry
            theta = normalized_velocity * (np.pi / 2.0)

            # Armazenar ângulo para get_quantum_operation
            self._internal_rotation_angle = theta

            vector = np.array([np.cos(theta), np.sin(theta)])

            encoder_logger.debug(
                f"Encoder '{self.name}': Snapshot ts={snapshot.get('timestamp')}, LV_metric={liquidity_velocity:.6f} (normalizada para {normalized_velocity:.6f}), "
                f"Theta={theta:.6f}, Vetor gerado={vector}"
            )
            return vector

        except Exception as e:
            encoder_logger.exception(
                f"Encoder {self.name} ({self.__class__.__name__}) falhou durante a codificação do snapshot: {snapshot}: {e}"
            )
            return np.array([1.0, 0.0])


class FundingRateDeviationEncoder(QuantumEncoder[FundingRateDeviationData]):
    """
    Codifica o desvio da taxa de funding (Funding Rate Deviation - FRD).
    Retorna um vetor normalizado np.ndarray [cos θ, sin θ].
    """

    def __init__(
        self,
        name: str = "FundingRateDeviationEncoder",
        scaling_factor: float = 10.0,
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.scaling_factor = scaling_factor
        self.data_keys = data_keys if data_keys else []
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        self._internal_rotation_angle: Optional[float] = None
        for key, value in kwargs.items():
            encoder_logger.debug(
                f"Encoder '{self.name}' recebeu param extra '{key}' no __init__."
            )
        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com scaling_factor={self.scaling_factor}, data_keys={self.data_keys}, target_qubits={self.target_qubits}, n_qubits_required={self.n_qubits_required}."
        )

    def get_quantum_operation(
        self, snapshot: FundingRateDeviationData
    ) -> Optional[tuple[str, list[float], list[int]]]:
        """
        Retorna a operação Ry e o ângulo de rotação calculado por _encode_single.
        """
        self._internal_rotation_angle = None
        vector = self._encode_single(snapshot)
        if (
            hasattr(self, "_internal_rotation_angle")
            and self._internal_rotation_angle is not None
        ):
            return ("ry", [self._internal_rotation_angle], [0])
        else:
            encoder_logger.warning(
                f"Encoder '{self.name}': _internal_rotation_angle não foi definido após _encode_single. Não é possível fornecer operação quântica."
            )
            return None

    def _encode_single(self, snapshot: FundingRateDeviationData) -> NDArrayFloat:
        """
        Processa um único FundingRateDeviationData.
        """
        # Reset _internal_rotation_angle
        self._internal_rotation_angle = None

        try:
            if not isinstance(snapshot, dict) or "frd_metric" not in snapshot:
                encoder_logger.warning(
                    f"Encoder '{self.name}' recebeu snapshot malformado ou sem 'frd_metric': {snapshot}. Retornando estado base."
                )
                return np.array([1.0, 0.0])

            funding_rate_deviation = snapshot["frd_metric"]
            if not isinstance(funding_rate_deviation, (float, int)):
                encoder_logger.warning(
                    f"Encoder '{self.name}': 'frd_metric' não é float ou int em {snapshot}. Retornando estado base."
                )
                return np.array([1.0, 0.0])

            raw_angle = funding_rate_deviation * self.scaling_factor
            theta = np.clip(raw_angle, -np.pi / 2.0, np.pi / 2.0)

            # Armazenar ângulo para get_quantum_operation
            self._internal_rotation_angle = theta

            vector = np.array([np.cos(theta), np.sin(theta)])

            encoder_logger.debug(
                f"Encoder '{self.name}': Snapshot ts={snapshot.get('timestamp')}, FRD_metric={funding_rate_deviation:.8f}, scaling_factor={self.scaling_factor}, raw_angle={raw_angle:.6f}, "
                f"Theta={theta:.6f}, Vetor gerado={vector}"
            )
            return vector

        except Exception as e:
            encoder_logger.exception(
                f"Encoder {self.name} ({self.__class__.__name__}) falhou durante a codificação do snapshot: {snapshot}: {e}"
            )
            return np.array([1.0, 0.0])


class VolumeRatioEncoder(QuantumEncoder[VolumeRatioData]):
    """YAA CORREÇÃO: Encoder para razão de volume com validação robusta."""

    def __init__(
        self,
        name: str = "VolumeRatioEncoder",
        scaling_factor: float = 1.0,
        max_ratio: float = 3.0,
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        **kwargs,
    ):
        super().__init__(name)
        self.scaling_factor = scaling_factor
        self.max_ratio = max_ratio
        # YAA CORREÇÃO: data_keys robusto
        self.data_keys = data_keys or ["volume_ratio"]
        self.target_qubits = target_qubits or [0]
        self.n_qubits_required = len(self.target_qubits)

        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com scaling_factor={self.scaling_factor}, "
            f"max_ratio={self.max_ratio}, data_keys={self.data_keys}, "
            f"target_qubits={self.target_qubits}, n_qubits_required={self.n_qubits_required}."
        )

    def get_quantum_operation(
        self, snapshot: VolumeRatioData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """YAA CORREÇÃO: Operação quântica robusta."""
        try:
            if not self._validate_snapshot_structure(snapshot):
                return ("ry", [0.1], self.target_qubits)

            # Buscar chave apropriada para volume_ratio
            volume_ratio = None
            for key in self.data_keys:
                volume_ratio = self._extract_data_from_snapshot(snapshot, key)
                if volume_ratio is not None:
                    break

            if volume_ratio is None:
                # Tentar chaves alternativas específicas para volume
                alt_keys = ["BTC/USD_1m_volume_ratio", "volume_ratio", "vol_ratio"]
                for alt_key in alt_keys:
                    volume_ratio = self._extract_data_from_snapshot(snapshot, alt_key)
                    if volume_ratio is not None:
                        break

            if volume_ratio is None:
                encoder_logger.debug(
                    f"Encoder '{self.name}': volume_ratio não encontrado"
                )
                return ("ry", [0.1], self.target_qubits)

            # Normalizar razão de volume
            normalized_ratio = min(float(volume_ratio), self.max_ratio) / self.max_ratio
            theta = normalized_ratio * np.pi * self.scaling_factor

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = theta

            return ("ry", [theta], self.target_qubits)

        except Exception as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro em get_quantum_operation: {e}"
            )
            return ("ry", [0.1], self.target_qubits)

    def _encode_single(self, snapshot: VolumeRatioData) -> NDArrayFloat:
        """YAA CORREÇÃO: Codificação robusta."""
        if not self._validate_snapshot_structure(snapshot):
            return self._get_fallback_state()

        # Buscar volume_ratio
        volume_ratio = None
        for key in self.data_keys:
            volume_ratio = self._extract_data_from_snapshot(snapshot, key)
            if volume_ratio is not None:
                break

        if volume_ratio is None:
            # Tentativas adicionais
            alt_keys = ["BTC/USD_1m_volume_ratio", "volume_ratio", "vol_ratio"]
            for alt_key in alt_keys:
                volume_ratio = self._extract_data_from_snapshot(snapshot, alt_key)
                if volume_ratio is not None:
                    break

        if volume_ratio is None:
            encoder_logger.debug(
                f"Encoder '{self.name}': volume_ratio não encontrado. Retornando estado base."
            )
            return self._get_fallback_state()

        try:
            # Processar valor
            normalized_ratio = min(float(volume_ratio), self.max_ratio) / self.max_ratio
            angle = normalized_ratio * np.pi * self.scaling_factor

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = angle

            return np.array([np.cos(angle), np.sin(angle)], dtype=np.float32)

        except (ValueError, TypeError) as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro ao processar volume_ratio: {e}"
            )
            return self._get_fallback_state()


class VolumeRatioAmplitudeEncoder(QuantumEncoder[VolumeRatioData]):
    """YAA CORREÇÃO: Encoder de amplitude para volume ratio."""

    def __init__(
        self,
        name: str = "VolumeRatioAmplitudeEncoder",
        max_ratio: float = 3.0,
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        *,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional["SimpleEventBus"] = None,
        use_hardware_acceleration: bool = False,
        **kwargs,
    ) -> None:
        super().__init__(
            name,
            statsd_client=statsd_client,
            event_bus=event_bus,
            use_hardware_acceleration=use_hardware_acceleration,
            **kwargs,
        )
        self.max_ratio = max_ratio
        self.data_keys = data_keys or ["volume_ratio"]
        self.target_qubits = target_qubits or [0]
        self.n_qubits_required = len(self.target_qubits)
        # YAA CORREÇÃO: Inicializar vetor interno
        self._internal_vector: Optional[np.ndarray] = None

        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com max_ratio={self.max_ratio}, "
            f"data_keys={self.data_keys}, target_qubits={self.target_qubits}, "
            f"n_qubits_required={self.n_qubits_required}."
        )

    def get_quantum_operation(
        self, snapshot: VolumeRatioData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """YAA CORREÇÃO: Operação robusta para amplitude encoding com normalização obrigatória."""
        try:
            if not self._validate_snapshot_structure(snapshot):
                return ("initialize", [1.0, 0.0], self.target_qubits)

            volume_ratio = None
            for key in self.data_keys:
                volume_ratio = self._extract_data_from_snapshot(snapshot, key)
                if volume_ratio is not None:
                    break

            if volume_ratio is None:
                alt_keys = ["BTC/USD_1m_volume_ratio", "volume_ratio"]
                for alt_key in alt_keys:
                    volume_ratio = self._extract_data_from_snapshot(snapshot, alt_key)
                    if volume_ratio is not None:
                        break

            if volume_ratio is None:
                return ("initialize", [1.0, 0.0], self.target_qubits)

            # YAA CORREÇÃO CRÍTICA: Garantir normalização quântica obrigatória
            normalized_ratio = min(float(volume_ratio), self.max_ratio) / self.max_ratio

            # Calcular amplitudes brutas
            alpha_raw = (
                np.sqrt(1 - normalized_ratio) if normalized_ratio <= 1.0 else 0.0
            )
            beta_raw = np.sqrt(normalized_ratio) if normalized_ratio >= 0.0 else 0.0

            # NORMALIZAÇÃO OBRIGATÓRIA: Usar método validado
            amplitude_vector = np.array([alpha_raw, beta_raw], dtype=np.float64)
            amplitude_vector = self._validate_quantum_normalization(amplitude_vector)

            # YAA CORREÇÃO: Armazenar vetor interno normalizado
            self._internal_vector = amplitude_vector.copy()

            return ("initialize", amplitude_vector.tolist(), self.target_qubits)

        except Exception as e:
            encoder_logger.error(
                f"Encoder '{self.name}': erro crítico em get_quantum_operation: {e}"
            )
            return ("initialize", [1.0, 0.0], self.target_qubits)

    def _encode_single(self, snapshot: VolumeRatioData) -> NDArrayFloat:
        """YAA CORREÇÃO: Amplitude encoding robusto com normalização obrigatória."""
        if not self._validate_snapshot_structure(snapshot):
            return self._get_fallback_state()

        volume_ratio = None
        for key in self.data_keys:
            volume_ratio = self._extract_data_from_snapshot(snapshot, key)
            if volume_ratio is not None:
                break

        if volume_ratio is None:
            alt_keys = ["BTC/USD_1m_volume_ratio", "volume_ratio"]
            for alt_key in alt_keys:
                volume_ratio = self._extract_data_from_snapshot(snapshot, alt_key)
                if volume_ratio is not None:
                    break

        if volume_ratio is None:
            encoder_logger.debug(
                f"Encoder '{self.name}': volume_ratio não encontrado. Retornando estado base."
            )
            return self._get_fallback_state()

        try:
            # YAA CORREÇÃO CRÍTICA: Garantir normalização quântica obrigatória
            normalized_ratio = min(float(volume_ratio), self.max_ratio) / self.max_ratio

            # Calcular amplitudes brutas
            alpha_raw = (
                np.sqrt(1 - normalized_ratio) if normalized_ratio <= 1.0 else 0.0
            )
            beta_raw = np.sqrt(normalized_ratio) if normalized_ratio >= 0.0 else 0.0

            # NORMALIZAÇÃO OBRIGATÓRIA: Usar método validado
            amplitude_vector = np.array([alpha_raw, beta_raw], dtype=np.float64)
            amplitude_vector = self._validate_quantum_normalization(amplitude_vector)

            # YAA CORREÇÃO: Armazenar vetor interno normalizado
            self._internal_vector = amplitude_vector.copy()

            # Log de sucesso
            encoder_logger.debug(
                f"Encoder '{self.name}': volume_ratio={volume_ratio:.6f}, "
                f"normalized={normalized_ratio:.6f}, "
                f"amplitudes=[{amplitude_vector[0]:.8f}, {amplitude_vector[1]:.8f}], "
                f"norm²={np.sum(amplitude_vector ** 2):.12f}"
            )

            return amplitude_vector.astype(np.float32)

        except Exception as e:
            encoder_logger.error(
                f"Encoder '{self.name}': erro crítico ao processar: {e}"
            )
            return self._get_fallback_state()


class RSIPhaseEncoder(QuantumEncoder[RSIData]):
    """Codifica o RSI como rotação de fase em um único qubit."""

    def __init__(
        self,
        name: str = "RSIPhaseEncoder",
        data_keys: Optional[List[str]] = None,
        target_qubits: Optional[List[int]] = None,
        *,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional["SimpleEventBus"] = None,
        use_hardware_acceleration: bool = False,
        **kwargs,
    ) -> None:
        super().__init__(
            name,
            statsd_client=statsd_client,
            event_bus=event_bus,
            use_hardware_acceleration=use_hardware_acceleration,
            **kwargs,
        )
        self.data_keys = data_keys or ["rsi"]
        self.target_qubits = target_qubits if target_qubits else []
        self.n_qubits_required = len(self.target_qubits) if self.target_qubits else 1
        for key, value in kwargs.items():
            encoder_logger.debug(
                f"Encoder '{self.name}' recebeu param extra '{key}' no __init__."
            )
        encoder_logger.info(
            f"Encoder '{self.name}' inicializado com data_keys={self.data_keys}, target_qubits={self.target_qubits}, "
            f"n_qubits_required={self.n_qubits_required}."
        )
        self._internal_rotation_angle: Optional[float] = None

    def get_quantum_operation(
        self, snapshot: RSIData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        """YAA CORREÇÃO: Operação quântica robusta."""
        try:
            if not self._validate_snapshot_structure(snapshot):
                return ("rz", [0.1], [0])

            # Buscar RSI
            rsi_value = None
            key_to_use = self.data_keys[0] if self.data_keys else "rsi"

            rsi_value = self._extract_data_from_snapshot(snapshot, key_to_use)
            if rsi_value is None:
                # Tentar chaves alternativas
                alt_keys = ["rsi", "RSI", "relative_strength_index"]
                for alt_key in alt_keys:
                    rsi_value = self._extract_data_from_snapshot(snapshot, alt_key)
                    if rsi_value is not None:
                        break

            if rsi_value is None:
                encoder_logger.debug(f"Encoder '{self.name}': RSI não encontrado")
                return ("rz", [0.1], [0])

            # Calcular fase
            normalized = np.clip(float(rsi_value) / 100.0, 0.0, 1.0)
            phase_angle = normalized * 2.0 * np.pi

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = phase_angle

            return ("rz", [phase_angle], [0])

        except Exception as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro em get_quantum_operation: {e}"
            )
            return ("rz", [0.1], [0])

    def _encode_single(self, snapshot: RSIData) -> NDArrayFloat:
        """YAA CORREÇÃO: Codificação robusta com validação."""
        if not self._validate_snapshot_structure(snapshot):
            return self._get_fallback_state()

        # Buscar RSI
        key_to_use = self.data_keys[0] if self.data_keys else "rsi"
        rsi_value = self._extract_data_from_snapshot(snapshot, key_to_use)

        if rsi_value is None:
            # Tentar chaves alternativas
            alt_keys = ["rsi", "RSI", "relative_strength_index"]
            for alt_key in alt_keys:
                rsi_value = self._extract_data_from_snapshot(snapshot, alt_key)
                if rsi_value is not None:
                    break

        if rsi_value is None:
            encoder_logger.debug(
                f"Encoder '{self.name}': RSI não encontrado. Usando fallback."
            )
            return self._get_fallback_state()

        try:
            # Processar RSI
            normalized = np.clip(float(rsi_value) / 100.0, 0.0, 1.0)
            phase_angle = normalized * 2.0 * np.pi

            # YAA CORREÇÃO: Armazenar estado interno
            self._internal_rotation_angle = phase_angle

            vector = np.array(
                [np.cos(phase_angle), np.sin(phase_angle)], dtype=np.float32
            )

            encoder_logger.debug(
                f"Encoder '{self.name}': RSI={rsi_value:.2f}, phase={phase_angle:.6f}"
            )
            return vector

        except Exception as e:
            encoder_logger.warning(
                f"Encoder '{self.name}': erro ao processar RSI={rsi_value}: {e}"
            )
            return self._get_fallback_state()


class TimeSeriesEmbeddingEncoder(QuantumEncoder[TimeSeriesEmbeddingData]):
    """Codifica embeddings de séries temporais via amplitude encoding."""

    def __init__(self, name: str = "TimeSeriesEmbeddingEncoder"):
        super().__init__(name)
        self.n_qubits_required: Optional[int] = None

    def get_quantum_operation(
        self, snapshot: TimeSeriesEmbeddingData
    ) -> Optional[Tuple[str, List[float], List[int]]]:
        vector = self._encode_single(snapshot)
        if vector.size == 0 or self.n_qubits_required is None:
            return None
        return (
            "initialize",
            vector.tolist(),
            list(range(self.n_qubits_required)),
        )

    def _encode_single(self, snapshot: TimeSeriesEmbeddingData) -> NDArrayFloat:
        embedding = snapshot.get("embedding")
        if not isinstance(embedding, (list, np.ndarray)):
            encoder_logger.warning(
                f"Encoder '{self.name}' recebeu snapshot malformado: {snapshot}"
            )
            self.n_qubits_required = 1
            return np.array([1.0, 0.0])

        arr = np.asarray(embedding, dtype=float)
        if arr.size == 0:
            self.n_qubits_required = 1
            return np.array([1.0, 0.0])

        n_qubits = int(np.ceil(np.log2(len(arr)))) or 1
        target_len = 2**n_qubits
        if len(arr) < target_len:
            arr = np.pad(arr, (0, target_len - len(arr)), "constant")
        elif len(arr) > target_len:
            arr = arr[:target_len]

        norm = np.linalg.norm(arr)
        if norm > 1e-10:
            arr = arr / norm
        else:
            arr = np.concatenate(([1.0], np.zeros(target_len - 1)))

        self.n_qubits_required = n_qubits
        return arr


# Interface para gerenciar e aplicar múltiplos encoders
# Esta interface precisará ser adaptada para usar a nova API dos encoders.
# Seu método principal (anteriormente encode_market_state, agora em QuantumMetricsCalculator)
# receberia os snapshots, chamaria os encoders apropriados (que retornam vetores),
# e então orquestraria a aplicação desses vetores a um QuantumCircuit.
class QuantumEncodingInterface:
    """
    Interface principal para encoders quânticos com sensibilidade adaptativa.

    YAA REFINAMENTO: Implementa sensibilidade dinâmica baseada no contexto
    de mercado e análise de padrões emergentes.
    """

    def __init__(self, encoders: List[QuantumEncoder] = None):
        # QuantumEncoder[Any]
        self.encoders: Dict[str, QuantumEncoder[Any]] = {}
        self.encoder_sensitivities: Dict[str, float] = (
            {}
        )  # YAA: Sensibilidades dinâmicas
        self.pattern_history: List[Dict[str, Any]] = []  # YAA: Histórico de padrões
        self.context_analyzer = QuantumContextAnalyzer()  # YAA: Analisador contextual

        if encoders:
            for encoder in encoders:
                self.add_encoder(encoder)
        self._validate_encoders()

    def _validate_encoders(self):
        """Validate all encoders and set up dynamic sensitivity tracking."""
        for name, encoder in self.encoders.items():
            if not hasattr(encoder, "get_quantum_operation"):
                encoder_logger.error(
                    f"Encoder '{name}' não possui método get_quantum_operation. "
                    f"Ele deve implementar a interface QuantumEncoder corretamente."
                )
                continue

            # YAA REFINAMENTO: Inicializar sensibilidade adaptativa
            self.encoder_sensitivities[name] = self._calculate_base_sensitivity(encoder)
            encoder_logger.info(
                f"Encoder '{name}' inicializado com sensibilidade base: {self.encoder_sensitivities[name]:.3f}"
            )

    def _calculate_base_sensitivity(self, encoder: QuantumEncoder[Any]) -> float:
        """Calcular sensibilidade base do encoder baseada em suas características."""
        base_sensitivity = 1.0

        # Ajustar sensibilidade baseada no tipo de encoder
        if "Volatility" in encoder.name:
            base_sensitivity = 1.2  # Volatilidade requer maior sensibilidade
        elif "OBI" in encoder.name:
            base_sensitivity = 1.5  # Order Book Imbalance é crítico
        elif "Momentum" in encoder.name:
            base_sensitivity = 1.1  # Momentum moderadamente sensível
        elif "RSI" in encoder.name:
            base_sensitivity = 0.9  # RSI mais estável
        elif "Volume" in encoder.name:
            base_sensitivity = 1.3  # Volume muito importante

        return base_sensitivity

    def add_encoder(self, encoder: QuantumEncoder[Any]):  # QuantumEncoder[Any]
        """Adiciona um encoder com rastreamento de sensibilidade."""
        if encoder.name in self.encoders:
            encoder_logger.warning(f"Encoder '{encoder.name}' já existe. Substituindo.")
        self.encoders[encoder.name] = encoder

        # YAA REFINAMENTO: Configurar sensibilidade dinâmica
        self.encoder_sensitivities[encoder.name] = self._calculate_base_sensitivity(
            encoder
        )
        encoder_logger.info(f"Encoder '{encoder.name}' adicionado com sucesso.")

    def get_encoder(self, name: str) -> Optional[QuantumEncoder[Any]]:
        return self.encoders.get(name)

    def get_all_encoders(self) -> Dict[str, QuantumEncoder[Any]]:  # Retorna Dict
        return self.encoders

    def get_all_encoders_list(self) -> List[QuantumEncoder[Any]]:  # Retorna List
        return list(self.encoders.values())

    def has_encoders(self) -> bool:
        return len(self.encoders) > 0

    def clear_encoders(self):
        """Limpa encoders e redefine sensibilidades."""
        self.encoders.clear()
        self.encoder_sensitivities.clear()
        self.pattern_history.clear()

    def encode_market_state(
        self, perception_data: Dict[str, Any], num_qubits_for_circuit: int
    ) -> Tuple[Optional[QuantumCircuit], Dict[str, List[int]]]:
        """
        Codifica estado de mercado com sensibilidade adaptativa.

        YAA REFINAMENTO: Implementa análise contextual e ajuste dinâmico
        de sensibilidades baseado no histórico de padrões.
        """
        if not self.has_encoders():
            encoder_logger.warning("Nenhum encoder disponível.")
            return None, {}

        # YAA REFINAMENTO: Análise contextual dos dados de entrada
        market_context = self.context_analyzer.analyze_market_context(perception_data)

        # Atualizar sensibilidades baseadas no contexto
        self._update_dynamic_sensitivities(market_context)

        encoders_to_use = []
        excluded: Dict[str, str] = {}
        for encoder in self.encoders.values():
            # YAA REFINAMENTO: Filtrar encoders baseado na sensibilidade e contexto
            use_encoder, reason = self._should_use_encoder(encoder, market_context)
            if use_encoder:
                encoders_to_use.append(encoder)
            else:
                excluded[encoder.name] = reason

        for name, reason in excluded.items():
            encoder_logger.info(
                "Encoder '%s' excluído da codificação: %s", name, reason
            )

        encoder_logger.info(
            f"Usando {len(encoders_to_use)} encoders de {len(self.encoders)} disponíveis "
            f"para contexto {market_context['regime']}"
        )

        if not encoders_to_use:
            if market_context.get("regime", "normal") == "normal" and self.encoders:
                default_encoder = max(
                    self.encoders.values(),
                    key=lambda e: self.encoder_sensitivities.get(e.name, 0.0),
                )
                encoders_to_use.append(default_encoder)
                encoder_logger.warning(
                    "Nenhum encoder atendia aos critérios; usando encoder padrão '%s'",
                    default_encoder.name,
                )
            else:
                encoder_logger.warning(
                    "Nenhum encoder selecionado após análise contextual."
                )
                return None, {}

        # YAA REFINAMENTO: Construir circuito com sensibilidade adaptativa
        return self._build_adaptive_circuit(
            encoders_to_use, perception_data, num_qubits_for_circuit, market_context
        )

    def _should_use_encoder(
        self, encoder: QuantumEncoder[Any], market_context: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Determinar se um encoder deve ser usado baseado no contexto de mercado."""
        encoder_name = encoder.name
        regime = market_context.get("regime", "normal")
        volatility = market_context.get("volatility", 0.5)

        current_sensitivity = self.encoder_sensitivities.get(encoder_name, 1.0)
        reason = ""

        if regime == "high_volatility":
            if "Volatility" in encoder_name or "OBI" in encoder_name:
                return current_sensitivity > 0.8, (
                    "sensibilidade < 0.8" if current_sensitivity <= 0.8 else ""
                )
            if "RSI" in encoder_name and volatility > 0.7:
                return False, "RSI instável em alta volatilidade"
        elif regime == "low_volatility":
            if "Momentum" in encoder_name or "Volume" in encoder_name:
                return current_sensitivity > 0.9, (
                    "sensibilidade < 0.9" if current_sensitivity <= 0.9 else ""
                )
        else:
            if current_sensitivity <= 0.3:
                reason = "sensibilidade muito baixa"
            return current_sensitivity > 0.3, reason

        return current_sensitivity > 0.7, (
            "sensibilidade < 0.7" if current_sensitivity <= 0.7 else ""
        )

    def _update_dynamic_sensitivities(self, market_context: Dict[str, Any]):
        """
        YAA REFINEMENT: Atualização ultra-sensível de sensibilidades dos encoders.

        Implementa:
        - Calibração harmônica adaptativa
        - Detecção de ressonâncias emergentes
        - Ajuste multi-modal baseado em contexto quântico
        - Sensibilidade espectral temporal
        """
        regime = market_context.get("regime", "normal")
        volatility = market_context.get("volatility", 0.5)
        trend_strength = market_context.get("trend_strength", 0.5)

        # YAA REFINEMENT: Métricas quânticas adicionais
        quantum_entropy = market_context.get("quantum_entropy", 0.5)
        quantum_coherence = market_context.get("quantum_coherence", 0.5)
        otoc_magnitude = market_context.get("otoc_magnitude", 0.5)
        harmonic_resonance = market_context.get("harmonic_resonance", 0.0)
        temporal_correlation = market_context.get("temporal_correlation", 0.5)

        # Calcular fator de sensibilidade composto
        sensitivity_environment_factor = self._calculate_environment_sensitivity_factor(
            quantum_entropy, quantum_coherence, otoc_magnitude, harmonic_resonance
        )

        encoder_logger.debug(
            f"Updating sensitivities - Regime: {regime}, "
            f"Volatility: {volatility:.3f}, Quantum Coherence: {quantum_coherence:.3f}, "
            f"Environment Factor: {sensitivity_environment_factor:.3f}"
        )

        for encoder_name in self.encoders.keys():
            base_sensitivity = self._calculate_base_sensitivity(
                self.encoders[encoder_name]
            )

            # === YAA REFINEMENT: Ajustes Multi-Modais ===

            # 1. Ajustes baseados no regime de mercado
            regime_adjustment = self._get_regime_adjustment(
                encoder_name, regime, volatility
            )

            # 2. Ajustes baseados em coerência quântica
            coherence_adjustment = self._get_coherence_adjustment(
                encoder_name, quantum_coherence, quantum_entropy
            )

            # 3. Ajustes baseados em OTOC (scrambling)
            otoc_adjustment = self._get_otoc_adjustment(encoder_name, otoc_magnitude)

            # 4. Ajustes baseados em ressonância harmônica
            harmonic_adjustment = self._get_harmonic_adjustment(
                encoder_name, harmonic_resonance, temporal_correlation
            )

            # 5. Ajuste adicional baseado na força da tendência
            trend_adjustment = self._get_trend_adjustment(encoder_name, trend_strength)

            # === Composição Final dos Ajustes ===
            composite_adjustment = (
                regime_adjustment * 0.30
                + coherence_adjustment * 0.25
                + otoc_adjustment * 0.20
                + harmonic_adjustment * 0.15
                + trend_adjustment * 0.10
            ) * sensitivity_environment_factor

            # Suavização temporal com memória adaptativa
            current_sensitivity = self.encoder_sensitivities.get(
                encoder_name, base_sensitivity
            )

            # Taxa de adaptação baseada na estabilidade do ambiente
            adaptation_rate = self._calculate_adaptation_rate(
                quantum_coherence, volatility, harmonic_resonance
            )

            new_sensitivity = (
                1 - adaptation_rate
            ) * current_sensitivity + adaptation_rate * (
                base_sensitivity * composite_adjustment
            )

            # Clipping adaptativo baseado no contexto
            min_sensitivity, max_sensitivity = self._get_adaptive_sensitivity_bounds(
                encoder_name, regime, quantum_coherence
            )

            final_sensitivity = np.clip(
                new_sensitivity, min_sensitivity, max_sensitivity
            )

            # Atualizar com logging detalhado
            if (
                abs(final_sensitivity - current_sensitivity) > 0.05
            ):  # Log apenas mudanças significativas
                encoder_logger.debug(
                    f"Encoder '{encoder_name}': sensitivity {current_sensitivity:.3f} -> {final_sensitivity:.3f} "
                    f"(regime: {regime_adjustment:.2f}, coherence: {coherence_adjustment:.2f}, "
                    f"otoc: {otoc_adjustment:.2f}, harmonic: {harmonic_adjustment:.2f})"
                )

            self.encoder_sensitivities[encoder_name] = final_sensitivity

    def _calculate_environment_sensitivity_factor(
        self,
        quantum_entropy: float,
        quantum_coherence: float,
        otoc_magnitude: float,
        harmonic_resonance: float,
    ) -> float:
        """Calcular fator de sensibilidade do ambiente quântico."""
        # Ambientes de alta coerência e baixa entropia são mais sensíveis
        coherence_factor = quantum_coherence
        entropy_factor = (
            1.0 - quantum_entropy
        )  # Inverter: baixa entropia = alta sensibilidade

        # OTOC e ressonância harmônica contribuem para sensibilidade
        otoc_factor = otoc_magnitude
        harmonic_factor = min(harmonic_resonance * 2.0, 1.0)  # Amplificar até o limite

        # Composição com pesos não-lineares
        environment_factor = (
            coherence_factor * 0.4
            + entropy_factor * 0.3
            + otoc_factor * 0.2
            + harmonic_factor * 0.1
        )

        # Aplicar função de ativação suave para suavizar extremos
        return 0.5 + 0.8 * np.tanh(2 * (environment_factor - 0.5))

    def _get_regime_adjustment(
        self, encoder_name: str, regime: str, volatility: float
    ) -> float:
        """Ajuste baseado no regime de mercado."""
        if regime == "high_volatility":
            if "Volatility" in encoder_name or "OBI" in encoder_name:
                return (
                    1.3 + volatility * 0.3
                )  # Extra boost para encoders de volatilidade
            elif "RSI" in encoder_name and volatility > 0.7:
                return 0.7  # RSI pode ser instável em alta volatilidade
            else:
                return 0.9
        elif regime == "low_volatility":
            if "Momentum" in encoder_name or "Volume" in encoder_name:
                return (
                    1.4 + (1.0 - volatility) * 0.4
                )  # Boost proporcional à baixa volatilidade
            else:
                return 1.1
        else:  # Regime normal
            return 1.0

    def _get_coherence_adjustment(
        self, encoder_name: str, coherence: float, entropy: float
    ) -> float:
        """Ajuste baseado na coerência quântica."""
        # Alta coerência permite sensibilidade aumentada
        coherence_boost = coherence * 0.5

        # Baixa entropia também permite maior sensibilidade
        entropy_boost = (1.0 - entropy) * 0.3

        # Encoders sensíveis se beneficiam mais da coerência
        if "Phase" in encoder_name or "Embedding" in encoder_name:
            return 1.0 + coherence_boost + entropy_boost + 0.2
        else:
            return 1.0 + coherence_boost + entropy_boost

    def _get_otoc_adjustment(self, encoder_name: str, otoc_magnitude: float) -> float:
        """Ajuste baseado no OTOC (Out-of-Time-Order Correlator)."""
        # OTOC alto indica baixo scrambling, permitindo sensibilidade aumentada
        if otoc_magnitude > 0.8:
            return 1.2  # Alto OTOC = baixo ruído = alta sensibilidade
        elif otoc_magnitude < 0.3:
            return 0.8  # Baixo OTOC = alto scrambling = reduzir sensibilidade
        else:
            return 1.0

    def _get_harmonic_adjustment(
        self, encoder_name: str, harmonic_resonance: float, temporal_correlation: float
    ) -> float:
        """Ajuste baseado em ressonância harmônica."""
        # Ressonância harmônica indica padrões emergentes
        if harmonic_resonance > 0.5:
            harmonic_boost = harmonic_resonance * 0.4
            correlation_boost = temporal_correlation * 0.2

            # Encoders de momento e fase se beneficiam mais de padrões harmônicos
            if "Momentum" in encoder_name or "Phase" in encoder_name:
                return 1.0 + harmonic_boost + correlation_boost + 0.15
            else:
                return 1.0 + harmonic_boost + correlation_boost
        else:
            return 1.0

    def _get_trend_adjustment(self, encoder_name: str, trend_strength: float) -> float:
        """Ajuste baseado na força da tendência."""
        if trend_strength > 0.7:
            if "Momentum" in encoder_name:
                return 1.2  # Momentum crítico em tendências fortes
            elif "Volume" in encoder_name:
                return 1.1  # Volume confirma tendências
        return 1.0

    def _calculate_adaptation_rate(
        self, coherence: float, volatility: float, harmonic_resonance: float
    ) -> float:
        """Calcular taxa de adaptação baseada na estabilidade do ambiente."""
        # Ambientes mais estáveis permitem adaptação mais lenta (mais suave)
        stability = (coherence + (1.0 - volatility) + harmonic_resonance) / 3.0

        # Taxa de adaptação inversa à estabilidade
        base_rate = 0.3
        stability_factor = 1.0 - stability * 0.5  # Reduz taxa em até 50%

        return base_rate * stability_factor

    def _get_adaptive_sensitivity_bounds(
        self, encoder_name: str, regime: str, coherence: float
    ) -> Tuple[float, float]:
        """Calcular limites adaptativos de sensibilidade."""
        # Limites base
        base_min = 0.3
        base_max = 2.0

        # Ajustar baseado no regime
        if regime == "high_volatility":
            min_bound = base_min + 0.1  # Evitar sensibilidade muito baixa
            max_bound = base_max + 0.5  # Permitir maior sensibilidade
        elif regime == "low_volatility":
            min_bound = base_min - 0.1
            max_bound = base_max + 0.3
        else:
            min_bound = base_min
            max_bound = base_max

        # Ajustar baseado na coerência
        if coherence > 0.8:  # Alta coerência permite maior range
            max_bound += 0.3
        elif coherence < 0.3:  # Baixa coerência limita sensibilidade
            max_bound -= 0.2
            min_bound += 0.1

        return max(0.1, min_bound), min(3.0, max_bound)

    def _build_adaptive_circuit(
        self,
        encoders: List[QuantumEncoder[Any]],
        perception_data: Dict[str, Any],
        num_qubits: int,
        market_context: Dict[str, Any],
    ) -> Tuple[Optional[QuantumCircuit], Dict[str, List[int]]]:
        """Construir circuito quântico com sensibilidade adaptativa."""
        try:
            from qiskit import QuantumCircuit
            from qiskit_aer import Aer

            qc = QuantumCircuit(num_qubits)
            encoder_qubit_mapping: Dict[str, List[int]] = {}
            current_qubit = 0

            # YAA REFINAMENTO: Ordenar encoders por sensibilidade para alocação prioritária
            sorted_encoders = sorted(
                encoders,
                key=lambda e: self.encoder_sensitivities.get(e.name, 1.0),
                reverse=True,
            )

            for encoder in sorted_encoders:
                if current_qubit >= num_qubits:
                    encoder_logger.warning(
                        f"Não há qubits suficientes para o encoder '{encoder.name}'. "
                        f"Necessário: {current_qubit + 1}, Disponível: {num_qubits}"
                    )
                    break

                try:
                    # YAA REFINAMENTO: Aplicar sensibilidade adaptativa na operação
                    sensitivity = self.encoder_sensitivities.get(encoder.name, 1.0)
                    operation = encoder.get_quantum_operation(perception_data)

                    if operation is None:
                        encoder_logger.debug(
                            f"Encoder '{encoder.name}' retornou None para get_quantum_operation."
                        )
                        continue

                    op_type, params, target_qubits = operation

                    # Aplicar sensibilidade aos parâmetros
                    if params and sensitivity != 1.0:
                        adjusted_params = [p * sensitivity for p in params]
                        encoder_logger.debug(
                            f"Encoder '{encoder.name}': parâmetros ajustados por sensibilidade "
                            f"{sensitivity:.3f}: {params} -> {adjusted_params}"
                        )
                        params = adjusted_params

                    # Mapear qubits relativos para qubits absolutos no circuito
                    absolute_qubits = []
                    for relative_qubit in target_qubits:
                        absolute_qubit = current_qubit + relative_qubit
                        if absolute_qubit < num_qubits:
                            absolute_qubits.append(absolute_qubit)
                        else:
                            encoder_logger.warning(
                                f"Encoder '{encoder.name}': qubit {absolute_qubit} fora do range. "
                                f"Circuito tem {num_qubits} qubits."
                            )
                            break

                    if not absolute_qubits:
                        continue

                    # Aplicar operação quântica
                    if op_type == "ry":
                        for i, qubit in enumerate(absolute_qubits):
                            if i < len(params):
                                qc.ry(params[i], qubit)
                    elif op_type == "rx":
                        for i, qubit in enumerate(absolute_qubits):
                            if i < len(params):
                                qc.rx(params[i], qubit)
                    elif op_type == "rz":
                        for i, qubit in enumerate(absolute_qubits):
                            if i < len(params):
                                qc.rz(params[i], qubit)
                    elif op_type == "p":  # Phase gate
                        for i, qubit in enumerate(absolute_qubits):
                            if i < len(params):
                                qc.p(params[i], qubit)
                    elif op_type == "initialize":
                        if len(absolute_qubits) == 1 and len(params) >= 2:
                            # YAA CORREÇÃO CRÍTICA: Normalizar amplitudes antes de initialize
                            amplitudes = np.array(
                                [params[0], params[1]], dtype=np.float64
                            )
                            norm_squared = np.sum(amplitudes**2)

                            if norm_squared > 1e-12:
                                amplitudes = amplitudes / np.sqrt(norm_squared)
                            else:
                                amplitudes = np.array([1.0, 0.0], dtype=np.float64)

                            # Verificação final antes de initialize
                            final_norm_squared = np.sum(amplitudes**2)
                            if not np.isclose(final_norm_squared, 1.0, atol=1e-10):
                                amplitudes = amplitudes / np.sqrt(final_norm_squared)

                            # Log de debug para monitoramento
                            encoder_logger.debug(
                                f"Encoder '{encoder.name}': normalizando amplitudes "
                                f"de {norm_squared:.12f} para {np.sum(amplitudes ** 2):.12f}"
                            )

                            qc.initialize(amplitudes.tolist(), absolute_qubits[0])
                    else:
                        encoder_logger.warning(
                            f"Encoder '{encoder.name}': operação '{op_type}' não suportada."
                        )
                        continue

                    encoder_qubit_mapping[encoder.name] = absolute_qubits
                    current_qubit += len(absolute_qubits)

                except Exception as e:
                    encoder_logger.error(
                        f"Erro ao processar encoder '{encoder.name}': {e}",
                        exc_info=True,
                    )
                    continue

            # YAA REFINAMENTO: Adicionar entrelaçamento adaptativo baseado no contexto
            self._add_adaptive_entanglement(qc, encoder_qubit_mapping, market_context)

            # Verificar limites de complexidade
            while qc.depth() > MAX_CIRCUIT_DEPTH or qc.size() > MAX_CIRCUIT_OPERATIONS:
                if num_qubits > 1:
                    encoder_logger.warning(
                        f"Circuito excede limites (depth={qc.depth()}, ops={qc.size()}). "
                        f"Reconstruindo com {num_qubits//2} qubits."
                    )
                    return self._build_adaptive_circuit(
                        encoders, perception_data, num_qubits // 2, market_context
                    )
                else:
                    break

            return qc, encoder_qubit_mapping

        except Exception as e:
            encoder_logger.error(
                f"Erro na construção do circuito adaptativo: {e}", exc_info=True
            )
            return None, {}

    def _add_adaptive_entanglement(
        self,
        qc: QuantumCircuit,
        encoder_mapping: Dict[str, List[int]],
        market_context: Dict[str, Any],
    ):
        """Adicionar entrelaçamento adaptativo baseado no contexto de mercado."""
        regime = market_context.get("regime", "normal")

        # Estratégia de entrelaçamento baseada no regime
        if regime == "high_volatility":
            # Alta volatilidade: entrelaçamento mais denso para capturar correlações
            entanglement_density = 0.8
        elif regime == "low_volatility":
            # Baixa volatilidade: entrelaçamento mais esparso para sensibilidade
            entanglement_density = 0.4
        else:
            # Regime normal: entrelaçamento moderado
            entanglement_density = 0.6

        # Aplicar entrelaçamento entre encoders correlacionados
        encoder_names = list(encoder_mapping.keys())
        for i, encoder1 in enumerate(encoder_names):
            for j, encoder2 in enumerate(encoder_names[i + 1 :], i + 1):
                if np.random.random() < entanglement_density:
                    # Usar qubits principais de cada encoder
                    if encoder_mapping[encoder1] and encoder_mapping[encoder2]:
                        q1 = encoder_mapping[encoder1][0]
                        q2 = encoder_mapping[encoder2][0]
                        if q1 != q2:
                            qc.cx(q1, q2)
                            encoder_logger.debug(
                                f"Entrelaçamento adaptativo: {encoder1}[{q1}] -> {encoder2}[{q2}]"
                            )


class QuantumContextAnalyzer:
    """
    YAA REFINAMENTO: Analisador de contexto quântico para detecção
    de padrões emergentes e regimes de mercado.
    """

    def __init__(self):
        self.history_window = 10
        self.context_cache = {}

    def analyze_market_context(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisar contexto de mercado para ajuste de sensibilidade."""
        try:
            context = {
                "regime": "normal",
                "volatility": 0.5,
                "trend_strength": 0.5,
                "liquidity_state": "normal",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            # Analisar volatilidade
            if "volatility_metric" in perception_data:
                vol = perception_data["volatility_metric"]
                if vol > 0.7:
                    context["regime"] = "high_volatility"
                    context["volatility"] = vol
                elif vol < 0.3:
                    context["regime"] = "low_volatility"
                    context["volatility"] = vol

            # Analisar momentum
            if "price_percentage_change" in perception_data:
                momentum = abs(perception_data["price_percentage_change"])
                context["trend_strength"] = min(momentum / 5.0, 1.0)  # Normalizar

            # Analisar liquidez
            if "lv_metric" in perception_data:
                lv = perception_data["lv_metric"]
                if lv > 0.8:
                    context["liquidity_state"] = "high"
                elif lv < 0.3:
                    context["liquidity_state"] = "low"

            # Cache do contexto para referência futura
            self.context_cache[context["timestamp"]] = context

            return context

        except Exception as e:
            encoder_logger.debug(f"Erro na análise de contexto: {e}")
            return {
                "regime": "normal",
                "volatility": 0.5,
                "trend_strength": 0.5,
                "liquidity_state": "normal",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }


# O if __name__ == '__main__' precisará de uma grande reformulação
# para testar a nova API dos encoders.
if __name__ == "__main__":
    initialize_logging(log_level="DEBUG")
    main_logger = get_logger(__name__)
    main_logger.info("Iniciando teste local da NOVA API de encoders...")

    # Exemplo de TypedDicts
    obi_snap1: OrderBookSnapshot = {
        "bids": [(100, 10), (99, 5)],
        "asks": [(101, 8), (102, 6)],
        "timestamp": 1234567890.000,
    }
    obi_snap2: OrderBookSnapshot = {
        "bids": [(100, 2), (99, 1)],
        "asks": [(101, 10), (102, 12)],
        "timestamp": 1234567891.000,
    }
    obi_snap_empty_book: OrderBookSnapshot = {
        "bids": [],
        "asks": [],
        "timestamp": 1234567892.000,
    }

    lv_snap1: LiquidityVelocityData = {"lv_metric": 0.75, "timestamp": 123}
    frd_snap1: FundingRateDeviationData = {"frd_metric": 0.00015, "timestamp": 456}

    # Encoders
    obi_encoder = OBIEncoder()
    lv_encoder = LiquidityVelocityEncoder()
    frd_encoder = FundingRateDeviationEncoder(
        scaling_factor=5000 * np.pi
    )  # Testar com outro scaling factor

    # Teste Escalar
    main_logger.info("--- Teste Escalar ---")
    obi_vector_single = obi_encoder.encode(obi_snap1)
    main_logger.info(
        f"OBIEncoder (single) para snap1: {obi_vector_single}, Norma: {np.linalg.norm(obi_vector_single)}"
    )

    lv_vector_single = lv_encoder.encode(lv_snap1)
    main_logger.info(
        f"LVEncoder (single) para snap1: {lv_vector_single}, Norma: {np.linalg.norm(lv_vector_single)}"
    )

    frd_vector_single = frd_encoder.encode(frd_snap1)
    main_logger.info(
        f"FRDEncoder (single) para snap1: {frd_vector_single}, Norma: {np.linalg.norm(frd_vector_single)}"
    )

    obi_vector_empty_book = obi_encoder.encode(obi_snap_empty_book)
    main_logger.info(
        f"OBIEncoder (empty_book) : {obi_vector_empty_book}, Norma: {np.linalg.norm(obi_vector_empty_book)}"
    )

    # Teste Batch
    main_logger.info("--- Teste Batch ---")
    obi_batch = [obi_snap1, obi_snap2, obi_snap_empty_book]
    obi_vectors_batch, obi_qubit_mapping = obi_encoder.encode(obi_batch)
    main_logger.info(
        f"OBIEncoder (batch) para {len(obi_batch)} snapshots: \n{obi_vectors_batch}"
    )
    if obi_vectors_batch.ndim == 2:
        for i, vec in enumerate(obi_vectors_batch):
            main_logger.info(f"  Batch item {i} Norma: {np.linalg.norm(vec)}")

    # Teste Batch Vazio
    main_logger.info("--- Teste Batch Vazio ---")
    empty_batch_result, empty_mapping = obi_encoder.encode([])
    main_logger.info(f"OBIEncoder (empty batch): {empty_batch_result}")

    # Teste Tipo Inválido
    main_logger.info("--- Teste Tipo Inválido ---")
    invalid_input_result, _ = obi_encoder.encode("nao_e_dict_nem_sequence")
    main_logger.info(
        f"OBIEncoder (invalid input): {invalid_input_result}, Norma: {np.linalg.norm(invalid_input_result)}"
    )

    # Adaptação do PriceMomentumEncoder (legado)
    pm_encoder = PriceMomentumEncoder()
    pm_snap1 = {"price_percentage_change": 0.005, "timestamp": 789}
    pm_snap2 = {"price_percentage_change": -0.002, "timestamp": 790}
    pm_snap_invalid = {"price_change_WRONG_KEY": 0.01, "timestamp": 791}

    pm_vector_single = pm_encoder.encode(pm_snap1)
    main_logger.info(
        f"PMEncoder (single) para snap1: {pm_vector_single}, Norma: {np.linalg.norm(pm_vector_single)}"
    )

    pm_vectors_batch, pm_qubit_mapping = pm_encoder.encode(
        [pm_snap1, pm_snap2, pm_snap_invalid]
    )
    main_logger.info(
        f"PMEncoder (batch) para 3 snapshots (1 inválido): \n{pm_vectors_batch}"
    )
    if pm_vectors_batch.ndim == 2:
        for i, vec in enumerate(pm_vectors_batch):
            main_logger.info(f"  Batch item {i} Norma: {np.linalg.norm(vec)}")

    main_logger.info("Teste local da NOVA API de encoders concluído.")
