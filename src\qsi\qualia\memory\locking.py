from __future__ import annotations

import asyncio
import threading
from functools import wraps
from typing import Any, Callable, TypeVar, Union

F = TypeVar("F", bound=Callable[..., Any])


class LockManager:
    """Utility that creates ``threading`` or ``asyncio`` locks.

    Parameters
    ----------
    async_safe : bool, optional
        When ``True`` :class:`asyncio.Lock` is used instead of
        :class:`threading.Lock`.
    """

    def __init__(self, *, async_safe: bool = False) -> None:
        self.async_safe = async_safe

    def create_lock(self) -> Union[threading.Lock, asyncio.Lock]:
        """Return a new lock instance respecting ``async_safe``."""

        return asyncio.Lock() if self.async_safe else threading.Lock()


def with_lock(func: F) -> F:
    """Serialize access to ``func`` when ``self._lock`` is defined.

    The decorator expects the first argument of ``func`` to be an instance that
    optionally provides a ``_lock`` attribute compatible with
    :class:`threading.Lock` or :class:`asyncio.Lock`. When present, the lock is
    acquired around the call ensuring thread-safe access to the decorated
    method. Asynchronous functions will honor ``asyncio.Lock`` by using
    ``async with``.
    """

    if asyncio.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(self, *args: Any, **kwargs: Any) -> Any:
            lock = getattr(self, "_lock", None)
            if lock:
                if isinstance(lock, asyncio.Lock):
                    async with lock:
                        return await func(self, *args, **kwargs)
                else:
                    with lock:
                        return await func(self, *args, **kwargs)
            return await func(self, *args, **kwargs)

        return async_wrapper  # type: ignore[misc]
    else:

        @wraps(func)
        def wrapper(self, *args: Any, **kwargs: Any):
            lock = getattr(self, "_lock", None)
            if isinstance(lock, asyncio.Lock):
                try:
                    in_async = asyncio.current_task() is not None
                except RuntimeError:
                    in_async = False
                if not in_async:
                    raise TypeError(
                        f"{func.__name__} must be awaited when 'async_safe' is True"
                    )

                async def _run() -> Any:
                    async with lock:
                        return func(self, *args, **kwargs)

                return _run()
            if lock:
                with lock:
                    return func(self, *args, **kwargs)
            return func(self, *args, **kwargs)

        return wrapper  # type: ignore[misc]
