#!/usr/bin/env python3
"""
Teste simples de import do calibrador
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

try:
    print("Testando imports...")
    
    from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
    print("✅ QualiaMetricsCalibrator importado com sucesso")
    
    from qualia.calibration.cache_manager import IntelligentCacheManager
    print("✅ IntelligentCacheManager importado com sucesso")
    
    from qualia.calibration.parallel_processor import ParallelCalibrationProcessor
    print("✅ ParallelCalibrationProcessor importado com sucesso")
    
    from qualia.calibration.validation_system import CalibrationValidationSystem
    print("✅ CalibrationValidationSystem importado com sucesso")
    
    print("\n🎉 TODOS OS IMPORTS FUNCIONANDO!")
    print("✅ Melhorias estruturais implementadas com sucesso:")
    print("   • Cache inteligente")
    print("   • Processamento paralelo") 
    print("   • Sistema de validação")
    print("   • Calibrador integrado")
    
except Exception as e:
    print(f"❌ Erro no import: {e}")
    import traceback
    traceback.print_exc()
