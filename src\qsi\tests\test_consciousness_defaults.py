import importlib

from qualia.config.consciousness_defaults import load_consciousness_defaults


def test_load_consciousness_defaults(monkeypatch, tmp_path):
    warnings = []

    def _warn(msg, *args, **kwargs):
        warnings.append(msg % args if args else msg)

    monkeypatch.setattr(
        'qualia.config.consciousness_defaults.logger.warning',
        _warn,
    )

    monkeypatch.setenv('QUALIA_CONSCIOUSNESS_DEFAULTS', str(tmp_path / 'cd.yaml'))
    # Copy default file to temp path for this test
    import shutil
    shutil.copyfile('config/consciousness_defaults.yaml', tmp_path / 'cd.yaml')

    cfg = load_consciousness_defaults()

    from qualia.config import settings as conf_settings
    importlib.reload(conf_settings)

    assert warnings == []
    assert cfg["n_qubits"] == conf_settings.DEFAULT_CONSCIOUSNESS_N_QUBITS
    assert cfg["n_steps"] == conf_settings.DEFAULT_CONSCIOUSNESS_N_STEPS
    assert cfg["thermal_coefficient"] == conf_settings.DEFAULT_CONSCIOUSNESS_THERMAL_COEFFICIENT
    assert cfg["retro_mode"] == conf_settings.DEFAULT_CONSCIOUSNESS_RETRO_MODE
    assert cfg["perception_depth"] == conf_settings.DEFAULT_CONSCIOUSNESS_PERCEPTION_DEPTH

