{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["strategy_config", "qast_config", "ace_config", "risk_profile_settings", "qpm_config", "qmc_config", "monitor_close_threshold"], "properties": {"strategy_config": {"type": "object"}, "qast_config": {"type": "object"}, "ace_config": {"type": "object"}, "risk_profile_settings": {"type": "object"}, "qpm_config": {"type": "object"}, "qmc_config": {"type": "object"}, "monitor_close_threshold": {"type": "number"}}, "additionalProperties": true}