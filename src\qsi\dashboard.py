"""Web dashboard para visualizar métricas de avaliação do QSI."""

from flask import Flask, request, render_template_string
from qsi.system import QSI

app = Flask(__name__)

HTML_FORM = """
<!doctype html>
<title>QSI Dashboard</title>
<h1>Avaliar Repositório</h1>
<form method=post>
  URL do repositório: <input type=text name=repo_url size=80><br><br>
  <input type=submit value="Avaliar">
</form>
{% if result %}
<h2>Resultado</h2>
<p><strong>Decisão:</strong> {{ result.decision }}</p>
<table border=1 cellpadding=5>
<tr><th>Métrica</th><th>Valor</th></tr>
{% for k, v in result.metrics.items() %}
<tr><td>{{ k }}</td><td>{{ '%.4f' % v }}</td></tr>
{% endfor %}
</table>
{% endif %}
"""

@app.route('/', methods=['GET', 'POST'])
def index():
    result = None
    if request.method == 'POST':
        repo_url = request.form.get('repo_url')
        if repo_url:
            system = QSI(dimensions=8)
            result = system.evaluate(repo_url)
    return render_template_string(HTML_FORM, result=result)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
