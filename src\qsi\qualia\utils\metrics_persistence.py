"""Persistence helpers for cache and circuit breaker metrics."""

from __future__ import annotations

from typing import Optional

from ..common_types import QuantumSignaturePacket
from ..config.settings import qpm_metrics_enabled
from .logger import get_logger

logger = get_logger(__name__)

_QPM_METRICS: "QuantumPatternMemory" | None = None


def _get_qpm() -> Optional["QuantumPatternMemory"]:
    """Return a singleton ``QuantumPatternMemory`` for metrics."""
    global _QPM_METRICS
    if not qpm_metrics_enabled or "pytest" in sys.modules:
        return None
    if _QPM_METRICS is None:
        from ..memory.qpm_loader import get_qpm_instance

        _QPM_METRICS = get_qpm_instance({"enable_warmstart": False})
    return _QPM_METRICS


def persist_metric_to_qpm(metric_name: str, success: bool) -> None:
    """Persist a metric result to ``QuantumPatternMemory``.

    Parameters
    ----------
    metric_name : str
        Identifier of the metric being recorded.
    success : bool
        Whether the operation succeeded.
    """
    qpm = _get_qpm()
    if qpm is None:
        return
    try:
        packet = QuantumSignaturePacket(
            vector=[1.0, 0.0] if success else [0.0, 1.0],
            metrics={"metric": metric_name, "success": success},
        )
    except TypeError:
        from types import SimpleNamespace

        packet = SimpleNamespace(
            vector=[1.0, 0.0] if success else [0.0, 1.0],
            metrics={"metric": metric_name, "success": success},
        )
    try:
        qpm.store_pattern(packet, market_snapshot={}, outcome={})
    except Exception as exc:  # pragma: no cover - defensive
        logger.debug("Failed to store metric %s in QPM: %s", metric_name, exc)
