import pandas as pd
from qualia.strategies.nova_estrategia_qualia.core import QualiaTSVFStrategy


def test_normalize_timezone_for_concat_mixed_indices():
    strategy = QualiaTSVFStrategy(symbol="BTC/USDT", timeframe="1h", params={})
    df_aware = pd.DataFrame({"close": [1]}, index=[pd.Timestamp("2023-01-01T00:00:00Z")])
    df_naive = pd.DataFrame({"close": [2]}, index=[pd.Timestamp("2023-01-01T01:00:00")])

    res1, res2 = strategy._normalize_timezone_for_concat(df_aware, df_naive)

    assert isinstance(res1.index, pd.DatetimeIndex)
    assert isinstance(res2.index, pd.DatetimeIndex)
    assert res1.index.tzinfo is not None
    assert res2.index.tzinfo is not None
    assert str(res1.index.tz) == "UTC"
    assert str(res2.index.tz) == "UTC"
