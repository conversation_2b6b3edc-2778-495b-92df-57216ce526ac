"""High level access to QUALIA configuration sections."""

from __future__ import annotations

from ..utils.logger import get_logger
from typing import Any, Dict, Optional

from .risk_profile_definitions import MANDATORY_RISK_PROFILE_KEYS

from .config_loader import ConfigLoader

logger = get_logger(__name__)


class ConfigManager:
    """Provide convenient accessors for the QUALIA strategy configuration."""

    def __init__(self, config_path: Optional[str] = None) -> None:
        self.loader = ConfigLoader(config_path)

    # ------------------------------------------------------------------
    def load(self, force_reload: bool = False) -> Dict[str, Any]:
        """Load configuration through the underlying :class:`ConfigLoader`."""
        return self.loader.load(force_reload=force_reload)

    @property
    def data(self) -> Dict[str, Any]:
        """Shortcut for ``loader.data``."""
        return self.loader.data

    # Access helpers ---------------------------------------------------
    def get_strategy_config(self) -> Dict[str, Any]:
        """Return the ``strategy_config`` section from the loaded configuration."""
        return self.loader.get_section("strategy_config", {})

    def get_qast_config(self) -> Dict[str, Any]:
        """Return the ``qast_config`` section with QAST engine parameters."""
        return self.loader.get_section("qast_config", {})

    def get_ace_config(self) -> Dict[str, Any]:
        """Return adaptive consciousness evolution (ACE) configuration."""
        return self.loader.get_section("ace_config", {})

    def get_qmc_config(self) -> Dict[str, Any]:
        """Return quantum metrics calculator (QMC) configuration."""
        return self.loader.get_section("qmc_config", {})

    def get_risk_profile_config(self, profile: str) -> Dict[str, Any]:
        """Return risk settings for the requested profile.

        Parameters
        ----------
        profile:
            Name of the risk profile to retrieve. If the profile does not
            exist, the ``default`` configuration is returned.
        """

        profiles = self.loader.get_section("risk_profile_settings")
        if not isinstance(profiles, dict) or not profiles:
            raise ValueError("risk_profile_settings missing or invalid")

        if profile in profiles:
            selected_name = profile
            selected = profiles[profile]
        elif "default" in profiles:
            selected_name = "default"
            selected = profiles["default"]
        else:
            raise ValueError(
                f"Risk profile '{profile}' not found and no default provided"
            )

        missing = [k for k in MANDATORY_RISK_PROFILE_KEYS if k not in selected]
        if missing:
            raise ValueError(
                f"risk profile '{selected_name}' missing keys: {', '.join(missing)}"
            )

        return selected

    def reload(self) -> Dict[str, Any]:
        """Force reloading the configuration from disk."""
        return self.loader.reload()


__all__ = ["ConfigManager", "MANDATORY_RISK_PROFILE_KEYS"]
