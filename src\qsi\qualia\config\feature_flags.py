from __future__ import annotations

"""Simple feature toggle helpers for QUALIA.

This module reads feature flag defaults from ``config/feature_flags.yaml`` and
allows overrides via environment variables of the form ``QUALIA_FT_<NAME>``.
"""

import os
from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

_DEFAULT_PATH = get_config_file_path("feature_flags.yaml")
_logger = get_logger(__name__)

def _load_feature_flags() -> Dict[str, Any]:
    """Load feature flag defaults from YAML configuration."""
    return load_yaml_config("QUALIA_FEATURE_FLAGS", _DEFAULT_PATH, logger=_logger)

_FEATURE_FLAGS = _load_feature_flags()


def feature_toggle(name: str, default: bool = False) -> bool:
    """Return whether a feature flag is enabled via environment variable.

    Parameters
    ----------
    name
        Identifier of the feature toggle.
    default
        Value returned when the toggle is not defined.

    Returns
    -------
    bool
        ``True`` when the toggle is enabled.
    """
    value = os.getenv(f"QUALIA_FT_{name.upper()}")
    if value is not None:
        return value.strip().lower() in {"1", "true", "yes", "on"}

    if name in _FEATURE_FLAGS:
        try:
            return bool(_FEATURE_FLAGS[name])
        except Exception:
            return default

    return default


MEM_CACHE_ENABLED = os.getenv("QUALIA_MEM_CACHE_ENABLED", "1").strip().lower() in {
    "1",
    "true",
    "yes",
    "on",
}
try:
    MEM_DECAY_FACTOR = float(os.getenv("QUALIA_MEM_DECAY_FACTOR", "1.0"))
except ValueError:
    MEM_DECAY_FACTOR = 1.0
INTENT_MEMORY_ENABLED = feature_toggle("intent_memory", default=False)

__all__ = [
    "feature_toggle",
    "MEM_CACHE_ENABLED",
    "MEM_DECAY_FACTOR",
    "INTENT_MEMORY_ENABLED",
]
