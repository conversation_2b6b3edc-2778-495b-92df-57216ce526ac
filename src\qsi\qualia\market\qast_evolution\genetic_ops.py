"""Genetic operators for QAST."""

from __future__ import annotations

import copy
import random
from typing import Any, Dict, Optional
from logging import DEBUG

from ...strategies.strategy_interface import TradingStrategy
from ...strategies.strategy_utils import copy_strategy
from ...utils.logger import get_logger

logger = get_logger(__name__)


def _mutate_strategy(
    engine: Any, strategy_representation: Any, mutation_strength: float
) -> None:
    """Mutate strategy parameters in place.

    Parameters
    ----------
    engine : Any
        Engine instance providing ``_get_adjustable_params`` and
        ``_set_param_value`` utilities.
    strategy_representation : Any
        Strategy object or configuration dictionary.
    mutation_strength : float
        Fraction representing the mutation intensity.

    Returns
    -------
    None
    """
    params_dict_to_mutate = None
    is_representation_dict = isinstance(strategy_representation, dict)

    if is_representation_dict:
        if "parameters" in strategy_representation and isinstance(
            strategy_representation["parameters"], dict
        ):
            params_dict_to_mutate = strategy_representation["parameters"]
        elif "params" in strategy_representation and isinstance(
            strategy_representation["params"], dict
        ):
            params_dict_to_mutate = strategy_representation["params"]
        else:
            # Fallback: se strategy_representation é um dict mas não tem ['parameters'] ou ['params'],
            # ou se esses valores não são dicionários, assumir que o próprio
            # strategy_representation é o dict de params. Isso é menos ideal
            # e pode indicar um problema na estrutura.
            logger.warning(
                "QAST Mutate: strategy_representation (dict) não tem uma chave 'parameters' ou 'params' válida. "
                f"Tentando mutar o strategy_representation diretamente. Conteúdo: {strategy_representation}"
            )
            if isinstance(strategy_representation, dict):
                params_dict_to_mutate = strategy_representation
            else:
                logger.error(
                    f"QAST Mutate: strategy_representation era um dict, mas agora não é? Tipo: {type(strategy_representation)}"
                )
                return

    elif isinstance(strategy_representation, TradingStrategy) and hasattr(
        strategy_representation, "get_params"
    ):
        params_dict_to_mutate = strategy_representation.get_params()
    elif hasattr(strategy_representation, "parameters") and isinstance(
        getattr(strategy_representation, "parameters"), dict
    ):
        params_dict_to_mutate = getattr(strategy_representation, "parameters")

    if params_dict_to_mutate is not None:
        # Caso 1: Encontramos um dicionário de parâmetros explícito (o preferido)
        adjustable_params_info = engine._get_adjustable_params(params_dict_to_mutate)
        target_for_set_param = (
            strategy_representation
            if not is_representation_dict
            else params_dict_to_mutate
        )
    elif not is_representation_dict:
        # Caso 2: strategy_representation é um objeto, mas não tem um atributo '.parameters' (dict).
        # Tentaremos mutar os atributos do objeto diretamente.
        if logger.isEnabledFor(DEBUG):
            logger.debug(
                f"Mutating object attributes directly for type {type(strategy_representation)} as no explicit 'parameters' dict was found."
            )
        adjustable_params_info = engine._get_adjustable_params(
            strategy_representation
        )  # Deve usar o fallback de dir()
        target_for_set_param = (
            strategy_representation  # _set_param_value operará no objeto
        )
    else:
        # Caso 3: strategy_representation é um dict, mas não conseguimos isolar/identificar um params_dict_to_mutate.
        logger.error(
            f"QAST Mutate: Não foi possível determinar o conjunto de parâmetros para mutação em: {strategy_representation}"
        )
        return

    # Loop de mutação
    for param_name, param_info in adjustable_params_info.items():
        original_value = param_info["value"]
        param_type = param_info["type"]
        new_value = original_value  # Default

        if param_type == "float":
            mutation = random.uniform(-mutation_strength, mutation_strength)
            new_value = original_value * (1 + mutation)
            if "min" in param_info and param_info["min"] is not None:
                new_value = max(param_info["min"], new_value)
            if "max" in param_info and param_info["max"] is not None:
                new_value = min(param_info["max"], new_value)
        elif param_type == "int":
            # Evitar que range_size seja 0 se original_value * mutation_strength for pequeno
            range_val = original_value * mutation_strength
            range_size = int(abs(range_val)) + (
                1 if range_val != 0 else 0
            )  # Adiciona 1 se não for zero, para garantir pelo menos +/-1
            if range_size == 0 and original_value != 0:
                range_size = 1  # Caso especial se original_value for pequeno

            mutation = random.randint(-range_size, range_size)
            new_value = original_value + mutation
            if "min" in param_info and param_info["min"] is not None:
                new_value = max(param_info["min"], new_value)
            if "max" in param_info and param_info["max"] is not None:
                new_value = min(param_info["max"], new_value)
        elif param_type == "bool":
            if random.random() < mutation_strength:
                new_value = not original_value
        else:  # unknown or unhandled type
            logger.debug(
                f"Parâmetro '{param_name}' do tipo '{param_type}' não será mutado."
            )

        engine._set_param_value(target_for_set_param, param_name, new_value)


def _get_adjustable_params(
    engine: Any, current_strategy_params: Any
) -> Dict[str, Dict[str, Any]]:
    """Return adjustable parameters for a strategy representation.

    Parameters
    ----------
    engine : Any
        Engine instance with ``qast_params_meta`` describing tunable parameters.
    current_strategy_params : Any
        Mapping of current parameter values.  If not a ``dict`` the helper
        ``_get_strategy_parameters`` is used to extract one.

    Returns
    -------
    Dict[str, Dict[str, Any]]
        Dictionary mapping parameter names to metadata describing value range
        and type information.
    """
    adjustable_params_info: Dict[str, Dict[str, Any]] = {}

    if not engine.qast_params_meta:
        logger.warning(
            f"QAST _get_adjustable_params (ID: {id(engine)}): engine.qast_params_meta está vazio. Nenhum parâmetro será considerado ajustável."
        )
        return adjustable_params_info

    if not isinstance(current_strategy_params, dict):
        current_strategy_params = engine._get_strategy_parameters(
            current_strategy_params
        )
        if not isinstance(current_strategy_params, dict):
            logger.error(
                f"QAST _get_adjustable_params (ID: {id(engine)}): current_strategy_params não é um dicionário (tipo: {type(current_strategy_params)}). Não é possível obter parâmetros."
            )
            return adjustable_params_info

    if logger.isEnabledFor(DEBUG):
        logger.debug(
            f"QAST _get_adjustable_params (ID: {id(engine)}): Buscando parâmetros ajustáveis. Meta definida para: {list(engine.qast_params_meta.keys())}. Valores atuais de: {list(current_strategy_params.keys())}"
        )

    for param_name, meta_info in engine.qast_params_meta.items():
        if param_name not in current_strategy_params:
            logger.warning(
                f"QAST _get_adjustable_params (ID: {id(engine)}): Parâmetro '{param_name}' definido em qast_params_meta mas não encontrado nos parâmetros atuais da estratégia. Ignorando."
            )
            continue

        current_value = current_strategy_params[param_name]
        param_type = meta_info.get("type", "unknown")

        # Verificar consistência do tipo se possível (opcional, mas bom para robustez)
        # Ex: if param_type == "int" and not isinstance(current_value, int): logger.warning(...)

        param_details = {
            "value": current_value,
            "type": param_type,
            "min": meta_info.get("min"),  # Pode ser None
            "max": meta_info.get("max"),  # Pode ser None
            "step": meta_info.get("step"),  # Pode ser None
            "options": meta_info.get("options"),  # Para tipo categórico
        }
        adjustable_params_info[param_name] = param_details
        logger.debug(
            f"QAST _get_adjustable_params (ID: {id(engine)}): Parâmetro '{param_name}' adicionado para ajuste com detalhes: {param_details}"
        )

    if not adjustable_params_info:
        logger.warning(
            f"QAST _get_adjustable_params (ID: {id(engine)}): Nenhum parâmetro ajustável foi identificado com base em qast_params_meta e current_strategy_params."
        )

    return adjustable_params_info


def _set_param_value(
    engine: Any, target_entity: Any, param_name: str, value: Any
) -> None:
    """Set a parameter value on a strategy entity.

    Parameters
    ----------
    engine : Any
        Engine instance used only for logging.
    target_entity : Any
        Strategy object or parameter dictionary.
    param_name : str
        Name of the parameter to update.
    value : Any
        Value to assign to the parameter.

    Returns
    -------
    None
    """
    # Caso 1: target_entity implementa a interface ``set_params``
    if hasattr(target_entity, "set_params") and callable(
        getattr(target_entity, "set_params")
    ):
        try:
            target_entity.set_params({param_name: value})
            return
        except Exception as e_set_params:  # pragma: no cover - erro inesperado
            logger.error(
                f"Erro ao chamar set_params em {type(target_entity)} para '{param_name}': {e_set_params}",
                exc_info=True,
            )
            # Prosseguir para outros métodos se este falhar

    # Caso 2: target_entity é um objeto que implementa set_parameter()
    if hasattr(target_entity, "set_parameter") and callable(
        getattr(target_entity, "set_parameter")
    ):
        try:
            target_entity.set_parameter(param_name, value)
            return
        except Exception as e_set_param:
            logger.error(
                f"Erro ao chamar set_parameter em {type(target_entity)} para '{param_name}': {e_set_param}",
                exc_info=True,
            )
            # Prosseguir para outros métodos se este falhar
    # Caso 3: target_entity É um dicionário de parâmetros
    if isinstance(target_entity, dict):
        if param_name in target_entity:
            target_entity[param_name] = value
        else:
            logger.warning(
                f"_set_param_value: Tentativa de definir parâmetro '{param_name}' que não existe no dicionário de parâmetros: {list(target_entity.keys())}"
            )
        return

    # Caso 4: target_entity é um objeto, mas não tem set_parameter()
    # Fallback: usar setattr diretamente.
    try:
        setattr(target_entity, param_name, value)
    except AttributeError as e_setattr:
        logger.error(
            f"_set_param_value: Falha ao usar setattr para '{param_name}' em {type(target_entity)}: {e_setattr}. O objeto pode não permitir atribuição."
        )
    except Exception as e_setattr_unhandled:
        logger.error(
            f"_set_param_value: Erro inesperado com setattr para '{param_name}' em {type(target_entity)}: {e_setattr_unhandled}",
            exc_info=True,
        )


def _get_strategy_parameters(
    engine: Any, strategy_representation: Any, ensure_copy: bool = False
) -> Dict[str, Any]:
    """Return the parameter dictionary for a strategy representation.

    Parameters
    ----------
    engine : Any
        Engine instance used to resolve parameters when ``strategy_representation``
        is not a mapping.
    strategy_representation : Any
        Strategy object or configuration dictionary.
    ensure_copy : bool, default=False
        If ``True``, the returned mapping is a deep copy.

    Returns
    -------
    Dict[str, Any]
        Mapping of strategy parameters.  Empty if none were found.
    """
    params: Optional[Dict[str, Any]] = None

    if isinstance(strategy_representation, dict):
        if "parameters" in strategy_representation and isinstance(
            strategy_representation["parameters"], dict
        ):
            params = strategy_representation["parameters"]
        elif "params" in strategy_representation and isinstance(
            strategy_representation["params"], dict
        ):
            params = strategy_representation["params"]
        else:
            params = strategy_representation
    else:
        if isinstance(strategy_representation, TradingStrategy) and hasattr(
            strategy_representation, "get_params"
        ):
            params = strategy_representation.get_params()
        elif hasattr(strategy_representation, "parameters") and isinstance(
            getattr(strategy_representation, "parameters"), dict
        ):
            params = getattr(strategy_representation, "parameters")
        elif hasattr(strategy_representation, "params") and isinstance(
            getattr(strategy_representation, "params"), dict
        ):
            params = getattr(strategy_representation, "params")
        else:
            params = {
                k: v
                for k, v in vars(strategy_representation).items()
                if not k.startswith("_") and k not in ["id", "generation", "fitness"]
            }

    if params is None:
        return {}
    return copy.deepcopy(params) if ensure_copy else params


def _update_strategy_representation_common_fields(
    engine,
    strategy_representation: Any,
    new_id: str,
    generation: int,
    params: Optional[Dict[str, Any]] = None,
) -> None:
    """Update identifier and parameter fields of a representation.

    Parameters
    ----------
    engine : Any
        Engine instance used only for logging.
    strategy_representation : Any
        Strategy object or configuration dictionary.
    new_id : str
        Identifier assigned to the representation.
    generation : int
        Generation number for bookkeeping.
    params : dict[str, Any], optional
        Parameter values to store alongside the representation.

    Returns
    -------
    None
    """
    if isinstance(strategy_representation, dict):
        strategy_representation["id"] = new_id
        strategy_representation["generation"] = generation
        strategy_representation["fitness"] = 0.0
        if params is not None:
            strategy_representation["parameters"] = copy.deepcopy(params)
    else:
        try:
            setattr(strategy_representation, "id", new_id)
            setattr(strategy_representation, "generation", generation)
            setattr(strategy_representation, "fitness", 0.0)
            if params is not None:
                if hasattr(strategy_representation, "set_params"):
                    strategy_representation.set_params(copy.deepcopy(params))
                elif hasattr(strategy_representation, "parameters"):
                    setattr(
                        strategy_representation,
                        "parameters",
                        copy.deepcopy(params),
                    )
                elif hasattr(strategy_representation, "params"):
                    setattr(
                        strategy_representation,
                        "params",
                        copy.deepcopy(params),
                    )
                else:
                    setattr(
                        strategy_representation,
                        "parameters",
                        copy.deepcopy(params),
                    )
        except Exception as e:
            logger.error(
                f"QAST _update_strategy_representation_common_fields: erro ao atualizar campos em {type(strategy_representation)}: {e}",
                exc_info=True,
            )


def _perform_crossover(
    engine: Any, parent1_repr: Any, parent2_repr: Any, generation: int
) -> Any:
    """Create a child strategy by crossing two parents.

    Parameters
    ----------
    engine : Any
        Engine instance used for helper operations.
    parent1_repr : Any
        First parent strategy representation.
    parent2_repr : Any
        Second parent strategy representation.
    generation : int
        Generation number assigned to the child.

    Returns
    -------
    Any
        Strategy representation produced from the crossover.
    """
    p1_id = (
        getattr(parent1_repr, "id", "unknown_p1")
        if not isinstance(parent1_repr, dict)
        else parent1_repr.get("id", "unknown_p1")
    )
    p2_id = (
        getattr(parent2_repr, "id", "unknown_p2")
        if not isinstance(parent2_repr, dict)
        else parent2_repr.get("id", "unknown_p2")
    )
    logger.info(
        f"QAST (ID: {id(engine)}): Realizando crossover entre Pai1 ({p1_id}) e Pai2 ({p2_id}) para Geração {generation}."
    )

    params1 = engine._get_strategy_parameters(parent1_repr, ensure_copy=True)
    params2 = engine._get_strategy_parameters(parent2_repr, ensure_copy=True)

    child_params = {}
    all_param_names = set(params1.keys()) | set(params2.keys())

    if not all_param_names:
        logger.warning(
            f"QAST (ID: {id(engine)}): Nenhum parâmetro encontrado nos pais {p1_id} e {p2_id} para crossover. Retornando cópia do Pai1."
        )
        # Criar uma representação filha baseada no pai1
        child_id = f"Gen{generation}_CxFailedCopy_{engine.crossover_child_count_this_generation}"
        engine.crossover_child_count_this_generation += 1
        child_repr_fail_cx = copy_strategy(parent1_repr)
        engine._update_strategy_representation_common_fields(
            child_repr_fail_cx, child_id, generation, params1
        )
        return child_repr_fail_cx

    for key in all_param_names:
        val1 = params1.get(key)
        val2 = params2.get(key)

        # Lógica de Crossover:
        if val1 is None and val2 is None:
            continue  # Ambos são None, não há o que cruzar para esta chave.
        elif val1 is None:
            child_params[key] = copy.deepcopy(val2)  # Pega de P2 se P1 não tem
        elif val2 is None:
            child_params[key] = copy.deepcopy(val1)  # Pega de P1 se P2 não tem
        else:  # Ambos os pais têm o parâmetro
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                # Crossover aritmético para numéricos (média ponderada ou simples)
                # child_params[key] = (val1 + val2) / 2
                # Blend crossover (alpha blending)
                alpha = random.random()  # alpha entre 0 e 1
                child_params[key] = alpha * val1 + (1 - alpha) * val2
                if isinstance(val1, int) and isinstance(
                    val2, int
                ):  # Se pais eram int, filho deve ser int
                    child_params[key] = round(child_params[key])

            elif isinstance(val1, bool) and isinstance(val2, bool):
                # Crossover para booleanos (escolha aleatória)
                child_params[key] = random.choice([val1, val2])
            # Adicionar mais lógicas de crossover para outros tipos se necessário (ex: listas, strings)
            # Para listas, poderia ser crossover de um ponto, dois pontos ou uniforme.
            # Por ora, se não for numérico ou booleano, escolhe aleatoriamente de um dos pais.
            else:  # Tipos diferentes ou não numéricos/booleanos
                child_params[key] = copy.deepcopy(random.choice([val1, val2]))

    if not child_params:
        logger.warning(
            f"QAST (ID: {id(engine)}): Parâmetros do filho resultaram vazios após crossover entre {p1_id} e {p2_id}. Retornando cópia do Pai1."
        )
        child_id_empty_params = f"Gen{generation}_CxEmptyCopy_{engine.crossover_child_count_this_generation}"
        engine.crossover_child_count_this_generation += 1
        child_repr_empty_params = copy_strategy(parent1_repr)
        engine._update_strategy_representation_common_fields(
            child_repr_empty_params,
            child_id_empty_params,
            generation,
            params1,
        )
        return child_repr_empty_params

    # Criar a representação da estratégia filha.
    # Se as representações dos pais eram objetos, o filho também deveria ser um objeto.
    # Se eram dicts, o filho será um dict.
    child_id = f"Gen{generation}_Cx_{engine.crossover_child_count_this_generation}"
    engine.crossover_child_count_this_generation += 1

    child_representation: Any
    if isinstance(
        parent1_repr, dict
    ):  # Assumir que se P1 é dict, P2 também é, e filho será dict
        child_representation = {
            "id": child_id,
            "generation": generation,
            "fitness": 0.0,  # Fitness será avaliado depois
            "parameters": child_params,
            "class_name": parent1_repr.get("class_name"),
            "symbol": parent1_repr.get("symbol"),
            "timeframe": parent1_repr.get("timeframe"),
        }
        logger.info(
            f"QAST (ID: {id(engine)}): Filho (dict) {child_id} gerado por crossover com {len(child_params)} params."
        )
    elif isinstance(
        parent1_repr, TradingStrategy
    ):  # Se P1 é objeto, criar novo objeto filho
        try:
            # Assumir que strategy_template é a classe da estratégia.
            # Isso pode precisar de ajuste se o template for uma instância ou uma fábrica mais complexa.
            if hasattr(engine.strategy_template, "__call__"):  # É uma classe
                child_object = engine.strategy_template(**child_params)
            else:  # Assumir que é uma instância, então clonar e reconfigurar
                child_object = copy_strategy(
                    engine.strategy_template
                )  # Copia o template
                if isinstance(child_object, dict):
                    engine._update_strategy_representation_common_fields(
                        child_object, child_id, generation, child_params
                    )
                    child_representation = child_object
                    logger.info(
                        f"QAST (ID: {id(engine)}): Filho (dict) {child_id} gerado por crossover."
                    )
                    return child_representation
                if hasattr(child_object, "reconfigure") and callable(
                    getattr(child_object, "reconfigure")
                ):
                    child_object.reconfigure(**child_params)
                else:  # Fallback para setar atributos individualmente
                    for p_name, p_val in child_params.items():
                        if hasattr(child_object, p_name):
                            setattr(child_object, p_name, p_val)
                        elif hasattr(child_object, "params") and isinstance(
                            getattr(child_object, "params"), dict
                        ):
                            getattr(child_object, "params")[p_name] = p_val

            engine._update_strategy_representation_common_fields(
                child_object, child_id, generation, child_params
            )

            child_representation = child_object
            logger.info(
                f"QAST (ID: {id(engine)}): Filho (objeto tipo {type(child_object)}) {child_id} gerado por crossover."
            )
        except Exception as e_obj_child:
            logger.error(
                f"QAST (ID: {id(engine)}): Erro ao criar objeto filho da estratégia via crossover (Base template: {type(engine.strategy_template)}): {e_obj_child}. Retornando cópia do Pai1.",
                exc_info=True,
            )
            child_id_obj_fail = f"Gen{generation}_CxObjFailCopy_{engine.crossover_child_count_this_generation}"
            engine.crossover_child_count_this_generation += 1
            child_repr_obj_fail = copy_strategy(parent1_repr)
            engine._update_strategy_representation_common_fields(
                child_repr_obj_fail, child_id_obj_fail, generation, params1
            )
            return child_repr_obj_fail
    else:
        logger.error(
            f"QAST (ID: {id(engine)}): Tipo de representação do pai1 ({type(parent1_repr)}) não suportado para determinar tipo do filho. Retornando cópia do Pai1."
        )
        child_id_type_fail = f"Gen{generation}_CxTypeFailCopy_{engine.crossover_child_count_this_generation}"
        engine.crossover_child_count_this_generation += 1
        child_repr_type_fail = copy_strategy(parent1_repr)
        engine._update_strategy_representation_common_fields(
            child_repr_type_fail, child_id_type_fail, generation, params1
        )
        return child_repr_type_fail

    return child_representation
