﻿# coding: utf-8
"""YAA - Adaptive History Manager for QUALIA System"""

from __future__ import annotations

import asyncio
import json
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from ..common.specs import MarketSpec
from ..constants import MIN_INITIAL_HISTORY
from ..market.base_integration import <PERSON>ptoDataFet<PERSON>
from ..utils.logger import get_logger
from ..utils.timeframe import safe_timeframe_to_minutes, timeframe_to_minutes

logger = get_logger(__name__)


@dataclass
class HistoryRequirement:
    symbol: str
    timeframe: str
    strategy_name: str
    required_candles: int
    min_acceptable: int
    priority: int = 1
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class ExchangeCapability:
    name: str
    max_candles_per_request: Dict[str, int]
    max_historical_candles: Dict[str, int]
    rate_limit_ms: int
    supports_batch: bool = False
    last_verified: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class CacheEntry:
    symbol: str
    timeframe: str
    data: pd.DataFrame
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0

    def is_expired(self, max_age_minutes: int = 60) -> bool:
        age = (datetime.now(timezone.utc) - self.created_at).total_seconds() / 60
        return age > max_age_minutes

    def update_access(self) -> None:
        self.last_accessed = datetime.now(timezone.utc)
        self.access_count += 1


class AdaptiveHistoryManager:
    """YAA - Gerenciador Adaptativo de Histórico com Cache Inteligente"""

    def __init__(
        self, cache_dir: Optional[str] = None, max_cache_size_mb: int = 500
    ) -> None:
        self.cache_dir = Path(cache_dir or "data/cache/history")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_cache_size_mb = max_cache_size_mb
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.history_requirements: List[HistoryRequirement] = []
        self.exchange_capabilities: Dict[str, ExchangeCapability] = {}
        self.adaptive_batch_sizes: Dict[str, int] = {}
        self.failure_counts: Dict[str, int] = {}
        self.success_rates: Dict[str, float] = {}
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_requests = 0
        self._last_request_time: Dict[str, float] = {}
        logger.info("YAA AdaptiveHistoryManager inicializado")

    def register_strategy_requirement(self, requirement: HistoryRequirement) -> None:
        """Registra requisito de histórico de uma estratégia."""
        self.history_requirements = [
            req
            for req in self.history_requirements
            if not (
                req.symbol == requirement.symbol
                and req.strategy_name == requirement.strategy_name
            )
        ]
        self.history_requirements.append(requirement)
        logger.info(
            f"Requisito registrado: {requirement.strategy_name} - {requirement.required_candles} candles"
        )

    def register_exchange_capability(self, capability: ExchangeCapability) -> None:
        """Registra capacidades de uma exchange."""
        self.exchange_capabilities[capability.name] = capability
        logger.info(f"Capacidades da exchange {capability.name} registradas")

    def _generate_cache_key(self, symbol: str, timeframe: str, suffix: str = "") -> str:
        """Gera chave única para cache."""
        base_key = f"{symbol}_{timeframe}".replace("/", "_")
        return f"{base_key}_{suffix}" if suffix else base_key

    def _estimate_exchange_limit(
        self, symbol: str, timeframe: str, exchange_name: str
    ) -> int:
        """Estima limite de candles históricos da exchange."""
        if exchange_name in self.exchange_capabilities:
            capability = self.exchange_capabilities[exchange_name]
            return capability.max_historical_candles.get(timeframe, 720)

        # Estimativas baseadas em exchanges conhecidas
        exchange_lower = exchange_name.lower()
        if "kraken" in exchange_lower:
            return 720  # Kraken tem limite de ~720 candles
        elif "binance" in exchange_lower:
            return 1000
        elif "coinbase" in exchange_lower:
            return 300
        elif "kucoin" in exchange_lower:
            return 1500  # KuCoin tem limite de ~1500 candles
        else:
            return 500  # Default conservador

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        required_candles: int,
        exchange_fetcher: Any,
        exchange_name: str,
        force_refresh: bool = False,
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """YAA - Método principal para obter dados históricos com cache adaptativo."""

        self.total_requests += 1
        cache_key = self._generate_cache_key(symbol, timeframe)

        # Metadata de retorno
        metadata = {
            "cache_hit": False,
            "exchange_limit_reached": False,
            "adaptive_adjustment": False,
            "actual_candles": 0,
            "requested_candles": required_candles,
            "source": "unknown",
        }

        # Verificar cache primeiro (se não forçar refresh)
        if not force_refresh and cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            if not entry.is_expired():
                entry.update_access()
                self.cache_hits += 1
                metadata.update(
                    {
                        "cache_hit": True,
                        "actual_candles": len(entry.data),
                        "source": "memory_cache",
                    }
                )
                logger.debug(
                    f"Cache hit para {symbol}@{timeframe}: {len(entry.data)} candles"
                )
                return entry.data.copy(), metadata

        self.cache_misses += 1

        # Implementar rate limiting adaptativo
        await self._apply_rate_limiting(exchange_name)

        # Estimar limite da exchange
        exchange_limit = self._estimate_exchange_limit(symbol, timeframe, exchange_name)
        effective_request = min(required_candles, exchange_limit)

        if effective_request < required_candles:
            metadata["exchange_limit_reached"] = True
            logger.warning(
                f"Limite da exchange {exchange_name}: {exchange_limit} < {required_candles} solicitados"
            )

        # Buscar dados da exchange
        try:
            data = await self._fetch_from_exchange(
                exchange_fetcher, symbol, timeframe, effective_request
            )

            if not data.empty:
                # Armazenar no cache
                self._store_in_cache(cache_key, symbol, timeframe, data)

                # Atualizar estatísticas de sucesso
                self._update_success_stats(exchange_name, True)

                metadata.update(
                    {"actual_candles": len(data), "source": "exchange", "success": True}
                )

                logger.info(
                    f"Dados obtidos da exchange {exchange_name}: {len(data)} candles para {symbol}@{timeframe}"
                )
                return data, metadata
            else:
                logger.warning(
                    f"Exchange {exchange_name} retornou dados vazios para {symbol}@{timeframe}"
                )

        except Exception as e:
            logger.error(f"Erro ao buscar dados da exchange {exchange_name}: {e}")
            self._update_success_stats(exchange_name, False)

        # Fallback: tentar cache em disco
        disk_data = await self._load_from_disk_cache(symbol, timeframe)
        if not disk_data.empty:
            self._store_in_cache(cache_key, symbol, timeframe, disk_data)
            metadata.update(
                {
                    "actual_candles": len(disk_data),
                    "source": "disk_cache",
                    "fallback": True,
                }
            )
            logger.info(
                f"Dados recuperados do cache em disco: {len(disk_data)} candles"
            )
            return disk_data, metadata

        # Último recurso: dados sintéticos mínimos
        synthetic_data = self._generate_synthetic_data(
            symbol, timeframe, max(MIN_INITIAL_HISTORY, 50)
        )
        metadata.update(
            {
                "actual_candles": len(synthetic_data),
                "source": "synthetic",
                "fallback": True,
                "warning": "Dados sintéticos gerados - não usar em produção",
            }
        )
        logger.warning(
            f"Gerando dados sintéticos para {symbol}@{timeframe}: {len(synthetic_data)} candles"
        )
        return synthetic_data, metadata

    async def _apply_rate_limiting(self, exchange_name: str) -> None:
        """Aplica rate limiting adaptativo baseado na exchange."""
        rate_limit_key = f"rate_limit_{exchange_name}"
        current_time = time.time()

        if rate_limit_key in self._last_request_time:
            time_since_last = current_time - self._last_request_time[rate_limit_key]

            # Rate limits baseados na exchange
            if "kraken" in exchange_name.lower():
                min_interval = 1.0  # 1 segundo entre requests
            elif "binance" in exchange_name.lower():
                min_interval = 0.1  # 100ms entre requests
            elif "kucoin" in exchange_name.lower():
                min_interval = 0.2  # 200ms entre requests para KuCoin
            else:
                min_interval = 0.5  # Default 500ms

            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                logger.debug(
                    f"Rate limiting: aguardando {sleep_time:.2f}s para {exchange_name}"
                )
                await asyncio.sleep(sleep_time)

        self._last_request_time[rate_limit_key] = time.time()

    async def _fetch_from_exchange(
        self, exchange_fetcher: Any, symbol: str, timeframe: str, candles_needed: int
    ) -> pd.DataFrame:
        """Busca dados da exchange usando o fetcher fornecido."""

        if not exchange_fetcher:
            raise ValueError("Exchange fetcher não fornecido")

        if hasattr(exchange_fetcher, "fetch_ohlcv"):
            return await self._fetch_via_ohlcv(
                exchange_fetcher, symbol, timeframe, candles_needed
            )

        if hasattr(exchange_fetcher, "fetch_historical_data"):
            return await self._fetch_via_custom(
                exchange_fetcher, symbol, timeframe, candles_needed
            )

        return pd.DataFrame()

    async def _fetch_via_ohlcv(
        self,
        exchange_fetcher: Any,
        symbol: str,
        timeframe: str,
        candles_needed: int,
    ) -> pd.DataFrame:
        """Fetch OHLCV data from a CCXT-style exchange."""

        try:
            now = datetime.now(timezone.utc)
            timeframe_minutes = safe_timeframe_to_minutes(timeframe)
            start_time = now - timedelta(minutes=candles_needed * timeframe_minutes)
            since = int(start_time.timestamp() * 1000)

            # Busca os dados mais recentes para preencher lacunas
            ohlcv_data = await exchange_fetcher.fetch_ohlcv(
                spec=MarketSpec(symbol=symbol, timeframe=timeframe),
                since=since,
                limit=self.max_chunk_size,
            )

            if ohlcv_data is None:
                return pd.DataFrame()

            if isinstance(ohlcv_data, pd.DataFrame) and ohlcv_data.empty:
                return pd.DataFrame()

            df = pd.DataFrame(
                ohlcv_data,
                columns=["timestamp", "open", "high", "low", "close", "volume"],
            )
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms", utc=True)
            return df.sort_values("timestamp").reset_index(drop=True)

        except Exception as exc:  # pragma: no cover - network errors
            logger.error("Erro ao usar fetch_ohlcv: %s", exc)
            return pd.DataFrame()

    async def _fetch_via_custom(
        self,
        exchange_fetcher: Any,
        symbol: str,
        timeframe: str,
        candles_needed: int,
    ) -> pd.DataFrame:
        """Fetch historical data using a custom fetcher."""

        try:
            end_date = datetime.now(timezone.utc)
            timeframe_minutes = safe_timeframe_to_minutes(timeframe)
            start_date = end_date - timedelta(
                minutes=candles_needed * timeframe_minutes
            )

            return await exchange_fetcher.fetch_historical_data(
                MarketSpec(symbol=symbol, timeframe=timeframe),
                start_date=start_date,
                end_date=end_date,
                use_cache=False,
            )

        except Exception as exc:  # pragma: no cover - network errors
            logger.error("Erro ao usar fetch_historical_data: %s", exc)
            return pd.DataFrame()

    def _timeframe_to_minutes(self, timeframe: str) -> int:
        """Wrapper for :func:`safe_timeframe_to_minutes`."""

        return safe_timeframe_to_minutes(timeframe)

    def _store_in_cache(
        self, cache_key: str, symbol: str, timeframe: str, data: pd.DataFrame
    ) -> None:
        """Armazena dados no cache em memória."""
        if data.empty:
            return

        # Verificar limite de cache
        self._enforce_cache_limits()

        # Criar entrada de cache
        entry = CacheEntry(
            symbol=symbol,
            timeframe=timeframe,
            data=data.copy(),
            created_at=datetime.now(timezone.utc),
            last_accessed=datetime.now(timezone.utc),
        )

        self.memory_cache[cache_key] = entry

        # Salvar em disco de forma assíncrona
        asyncio.create_task(self._save_to_disk_cache(symbol, timeframe, data))

    def _enforce_cache_limits(self) -> None:
        """Aplica limites de cache removendo entradas antigas."""
        if len(self.memory_cache) > 100:  # Limite de entradas
            # Remover as 20 entradas menos acessadas
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: (x[1].access_count, x[1].last_accessed),
            )

            for key, _ in sorted_entries[:20]:
                del self.memory_cache[key]

            logger.debug("Cache limpo: removidas 20 entradas menos utilizadas")

    async def _save_to_disk_cache(
        self, symbol: str, timeframe: str, data: pd.DataFrame
    ) -> None:
        """Salva dados no cache em disco usando CSV como fallback."""
        try:
            # Tentar parquet primeiro, fallback para CSV
            cache_file = self.cache_dir / f"{symbol.replace('/', '_')}_{timeframe}.csv"

            # Preparar dados para salvar
            data_to_save = data.copy()
            if "timestamp" in data_to_save.columns and hasattr(
                data_to_save["timestamp"].iloc[0], "isoformat"
            ):
                data_to_save["timestamp"] = data_to_save["timestamp"].dt.strftime(
                    "%Y-%m-%d %H:%M:%S.%f%z"
                )

            data_to_save.to_csv(cache_file, index=False)
            logger.debug(f"Dados salvos no cache em disco (CSV): {cache_file}")
        except Exception as e:
            logger.warning(f"Erro ao salvar cache em disco: {e}")

    async def _load_from_disk_cache(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """Carrega dados do cache em disco."""
        try:
            # Tentar CSV primeiro
            cache_file = self.cache_dir / f"{symbol.replace('/', '_')}_{timeframe}.csv"
            if cache_file.exists():
                data = pd.read_csv(cache_file)

                # Converter timestamp de volta para datetime
                if "timestamp" in data.columns:
                    data["timestamp"] = pd.to_datetime(data["timestamp"], utc=True)

                logger.debug(
                    f"Dados carregados do cache em disco (CSV): {len(data)} candles"
                )
                return data
        except Exception as e:
            logger.warning(f"Erro ao carregar cache em disco: {e}")

        return pd.DataFrame()

    def _generate_synthetic_data(
        self, symbol: str, timeframe: str, candles: int
    ) -> pd.DataFrame:
        """Gera dados sintéticos como último recurso."""
        logger.warning(
            f"Gerando {candles} candles sintéticos para {symbol}@{timeframe}"
        )

        # Timestamps
        now = datetime.now(timezone.utc)
        timeframe_minutes = safe_timeframe_to_minutes(timeframe)
        timestamps = pd.date_range(
            end=now, periods=candles, freq=f"{timeframe_minutes}min", tz=timezone.utc
        )

        # Preços sintéticos (random walk baseado em BTC se for crypto)
        base_price = 50000.0 if "BTC" in symbol else 1.0
        price_changes = np.random.normal(0, 0.02, size=candles)
        prices = base_price * np.cumprod(1 + price_changes)

        # OHLCV vetorizado
        high_noise = np.abs(np.random.normal(0, 0.01, size=candles))
        low_noise = np.abs(np.random.normal(0, 0.01, size=candles))
        high = prices * (1 + high_noise)
        low = prices * (1 - low_noise)
        open_prices = np.concatenate(([prices[0]], prices[:-1]))
        volume = np.random.uniform(100, 1000, size=candles)

        df = pd.DataFrame(
            {
                "timestamp": timestamps,
                "open": open_prices,
                "high": np.maximum.reduce([open_prices, high, prices]),
                "low": np.minimum.reduce([open_prices, low, prices]),
                "close": prices,
                "volume": volume,
            }
        )
        return df

    def _update_success_stats(self, exchange_name: str, success: bool) -> None:
        """Atualiza estatísticas de sucesso da exchange."""
        if exchange_name not in self.success_rates:
            self.success_rates[exchange_name] = 0.0
        if exchange_name not in self.failure_counts:
            self.failure_counts[exchange_name] = 0

        if success:
            # Média móvel exponencial para taxa de sucesso
            self.success_rates[exchange_name] = (
                0.9 * self.success_rates[exchange_name] + 0.1 * 1.0
            )
        else:
            self.failure_counts[exchange_name] += 1
            self.success_rates[exchange_name] = (
                0.9 * self.success_rates[exchange_name] + 0.1 * 0.0
            )

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas detalhadas do cache."""
        cache_size_mb = (
            sum(
                len(entry.data) * entry.data.shape[1] * 8 / (1024 * 1024)
                for entry in self.memory_cache.values()
            )
            if self.memory_cache
            else 0
        )

        hit_rate = self.cache_hits / max(self.total_requests, 1) * 100

        return {
            "total_requests": self.total_requests,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate_percent": hit_rate,
            "cache_entries": len(self.memory_cache),
            "cache_size_mb": cache_size_mb,
            "max_cache_size_mb": self.max_cache_size_mb,
            "adaptive_batch_sizes": dict(self.adaptive_batch_sizes),
            "success_rates": dict(self.success_rates),
            "failure_counts": dict(self.failure_counts),
            "requirements_registered": len(self.history_requirements),
            "exchanges_registered": len(self.exchange_capabilities),
        }

    def optimize_requirements(self) -> Dict[str, Any]:
        """YAA - Otimiza requisitos baseado em capacidades das exchanges."""
        optimizations = {
            "adjustments_made": 0,
            "strategies_affected": [],
            "total_candles_reduced": 0,
        }

        for requirement in self.history_requirements:
            # Verificar se temos capacidades registradas para otimizar
            for exchange_name, capability in self.exchange_capabilities.items():
                max_available = capability.max_historical_candles.get(
                    requirement.timeframe, 720
                )

                if requirement.required_candles > max_available:
                    original_requirement = requirement.required_candles
                    requirement.required_candles = max_available
                    requirement.last_updated = datetime.now(timezone.utc)

                    optimizations["adjustments_made"] += 1
                    optimizations["strategies_affected"].append(
                        {
                            "strategy": requirement.strategy_name,
                            "symbol": requirement.symbol,
                            "original": original_requirement,
                            "adjusted": max_available,
                            "exchange": exchange_name,
                        }
                    )
                    optimizations["total_candles_reduced"] += (
                        original_requirement - max_available
                    )

                    logger.info(
                        f"Otimização: {requirement.strategy_name} ajustado de "
                        f"{original_requirement} para {max_available} candles"
                    )

        return optimizations

    async def preload_critical_data(self) -> Dict[str, Any]:
        """YAA - Pré-carrega dados críticos baseado nos requisitos registrados."""
        preload_results = {
            "success_count": 0,
            "failure_count": 0,
            "total_candles_loaded": 0,
            "strategies_preloaded": [],
        }

        # Ordenar por prioridade
        sorted_requirements = sorted(
            self.history_requirements, key=lambda x: x.priority, reverse=True
        )

        for requirement in sorted_requirements:
            try:
                cache_key = self._generate_cache_key(
                    requirement.symbol, requirement.timeframe
                )

                # Verificar se já está no cache
                if cache_key in self.memory_cache:
                    entry = self.memory_cache[cache_key]
                    if len(entry.data) >= requirement.min_acceptable:
                        logger.debug(
                            f"Dados já em cache para {requirement.strategy_name}"
                        )
                        continue

                # Tentar carregar do cache em disco
                disk_data = await self._load_from_disk_cache(
                    requirement.symbol, requirement.timeframe
                )

                if not disk_data.empty and len(disk_data) >= requirement.min_acceptable:
                    self._store_in_cache(
                        cache_key, requirement.symbol, requirement.timeframe, disk_data
                    )
                    preload_results["success_count"] += 1
                    preload_results["total_candles_loaded"] += len(disk_data)
                    preload_results["strategies_preloaded"].append(
                        requirement.strategy_name
                    )

                    logger.info(
                        f"Pré-carregado do disco: {requirement.strategy_name} - "
                        f"{len(disk_data)} candles"
                    )

            except Exception as e:
                logger.error(
                    f"Erro no pré-carregamento para {requirement.strategy_name}: {e}"
                )
                preload_results["failure_count"] += 1

        return preload_results

    def calculate_optimal_batch_size(self, exchange_name: str, timeframe: str) -> int:
        """YAA - Calcula tamanho de lote ótimo baseado em performance histórica."""

        # Tamanhos base por exchange
        base_sizes = {
            "kraken": {"1m": 720, "5m": 720, "1h": 720, "4h": 720, "1d": 365},
            "binance": {"1m": 1000, "5m": 1000, "1h": 1000, "4h": 1000, "1d": 500},
            "coinbase": {"1m": 300, "5m": 300, "1h": 300, "4h": 300, "1d": 200},
            "kucoin": {"1m": 1500, "5m": 1500, "1h": 1500, "4h": 1500, "1d": 730},
        }

        exchange_lower = exchange_name.lower()
        for known_exchange, sizes in base_sizes.items():
            if known_exchange in exchange_lower:
                return sizes.get(timeframe, 500)

        # Default baseado no timeframe
        timeframe_defaults = {
            "1m": 500,
            "5m": 500,
            "15m": 400,
            "30m": 300,
            "1h": 720,
            "4h": 180,
            "1d": 365,
            "1w": 52,
        }

        return timeframe_defaults.get(timeframe, 300)

    async def intelligent_data_merge(
        self,
        symbol: str,
        timeframe: str,
        new_data: pd.DataFrame,
        existing_data: Optional[pd.DataFrame] = None,
    ) -> pd.DataFrame:
        """YAA - Merge inteligente de dados históricos evitando duplicatas."""

        if new_data.empty:
            return existing_data if existing_data is not None else pd.DataFrame()

        if existing_data is None or existing_data.empty:
            return new_data.sort_values("timestamp").reset_index(drop=True)

        # Combinar dados
        combined = pd.concat([existing_data, new_data], ignore_index=True)

        # Remover duplicatas baseado em timestamp
        combined = combined.drop_duplicates(subset=["timestamp"], keep="last")

        # Ordenar por timestamp
        combined = combined.sort_values("timestamp").reset_index(drop=True)

        # Verificar gaps e logs informativos
        if len(combined) > 1:
            time_diffs = combined["timestamp"].diff().dt.total_seconds() / 60
            expected_interval = safe_timeframe_to_minutes(timeframe)

            # Detectar gaps significativos
            gaps = time_diffs[time_diffs > expected_interval * 2]
            if not gaps.empty:
                logger.warning(
                    f"Detectados {len(gaps)} gaps nos dados de {symbol}@{timeframe}. "
                    f"Maior gap: {gaps.max():.1f} minutos"
                )

        logger.debug(
            f"Merge de dados para {symbol}@{timeframe}: "
            f"{len(existing_data)} + {len(new_data)} = {len(combined)} candles"
        )

        return combined

    def estimate_memory_usage(self) -> Dict[str, Any]:
        """YAA - Estima uso de memória do cache."""

        total_entries = len(self.memory_cache)
        total_rows = sum(len(entry.data) for entry in self.memory_cache.values())

        # Estimar bytes por linha (6 colunas OHLCV + timestamp)
        estimated_bytes_per_row = 8 * 7  # 7 colunas x 8 bytes (float64)
        estimated_total_bytes = total_rows * estimated_bytes_per_row
        estimated_mb = estimated_total_bytes / (1024 * 1024)

        return {
            "total_entries": total_entries,
            "total_rows": total_rows,
            "estimated_mb": estimated_mb,
            "max_allowed_mb": self.max_cache_size_mb,
            "usage_percent": (estimated_mb / self.max_cache_size_mb) * 100,
            "entries_by_symbol": {
                entry.symbol: len(entry.data) for entry in self.memory_cache.values()
            },
        }

    async def cleanup_old_cache_files(self, max_age_days: int = 7) -> Dict[str, Any]:
        """YAA - Limpa arquivos de cache antigos."""

        cleanup_results = {"files_removed": 0, "bytes_freed": 0, "errors": 0}

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=max_age_days)

            for cache_file in self.cache_dir.glob("*.csv"):
                try:
                    file_time = datetime.fromtimestamp(
                        cache_file.stat().st_mtime, tz=timezone.utc
                    )

                    if file_time < cutoff_time:
                        file_size = cache_file.stat().st_size
                        cache_file.unlink()
                        cleanup_results["files_removed"] += 1
                        cleanup_results["bytes_freed"] += file_size
                        logger.debug(f"Arquivo de cache removido: {cache_file}")

                except Exception as e:
                    logger.warning(f"Erro ao remover {cache_file}: {e}")
                    cleanup_results["errors"] += 1

            if cleanup_results["files_removed"] > 0:
                mb_freed = cleanup_results["bytes_freed"] / (1024 * 1024)
                logger.info(
                    f"Limpeza de cache: {cleanup_results['files_removed']} arquivos "
                    f"removidos, {mb_freed:.2f} MB liberados"
                )

        except Exception as e:
            logger.error(f"Erro na limpeza de cache: {e}")
            cleanup_results["errors"] += 1

        return cleanup_results

    def get_performance_metrics(self) -> Dict[str, Any]:
        """YAA - Métricas de performance do sistema de cache."""

        total_requests = max(self.total_requests, 1)

        # Calcular eficiência por exchange
        exchange_efficiency = {}
        for exchange_name, success_rate in self.success_rates.items():
            failure_count = self.failure_counts.get(exchange_name, 0)
            exchange_efficiency[exchange_name] = {
                "success_rate": success_rate,
                "failure_count": failure_count,
                "efficiency_score": success_rate * 100
                - (failure_count * 5),  # Penalizar falhas
            }

        # Calcular estatísticas de acesso do cache
        if self.memory_cache:
            access_counts = [entry.access_count for entry in self.memory_cache.values()]
            avg_access = np.mean(access_counts)
            max_access = max(access_counts)
            min_access = min(access_counts)
        else:
            avg_access = max_access = min_access = 0

        return {
            "cache_hit_rate": (self.cache_hits / total_requests) * 100,
            "total_requests": self.total_requests,
            "cache_efficiency": {
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_rate_percent": (self.cache_hits / total_requests) * 100,
            },
            "exchange_performance": exchange_efficiency,
            "cache_access_stats": {
                "avg_access_count": avg_access,
                "max_access_count": max_access,
                "min_access_count": min_access,
                "total_entries": len(self.memory_cache),
            },
            "requirements_stats": {
                "total_registered": len(self.history_requirements),
                "high_priority": len(
                    [r for r in self.history_requirements if r.priority > 1]
                ),
                "avg_required_candles": (
                    np.mean([r.required_candles for r in self.history_requirements])
                    if self.history_requirements
                    else 0
                ),
            },
        }

    async def smart_preload_strategy(
        self, exchange_fetcher: Any, exchange_name: str
    ) -> Dict[str, Any]:
        """YAA - Estratégia inteligente de pré-carregamento baseada em prioridades."""

        preload_results = {
            "strategies_processed": 0,
            "data_loaded": 0,
            "cache_hits": 0,
            "exchange_calls": 0,
            "total_time_seconds": 0,
        }

        start_time = time.time()

        # Ordenar requisitos por prioridade e necessidade
        sorted_requirements = sorted(
            self.history_requirements,
            key=lambda x: (x.priority, -x.required_candles),
            reverse=True,
        )

        for requirement in sorted_requirements:
            try:
                cache_key = self._generate_cache_key(
                    requirement.symbol, requirement.timeframe
                )

                # Verificar se já temos dados suficientes
                if cache_key in self.memory_cache:
                    entry = self.memory_cache[cache_key]
                    if len(entry.data) >= requirement.min_acceptable:
                        preload_results["cache_hits"] += 1
                        continue

                # Buscar dados
                data, metadata = await self.get_historical_data(
                    symbol=requirement.symbol,
                    timeframe=requirement.timeframe,
                    required_candles=requirement.min_acceptable,
                    exchange_fetcher=exchange_fetcher,
                    exchange_name=exchange_name,
                    force_refresh=False,
                )

                if not data.empty:
                    preload_results["data_loaded"] += len(data)
                    if metadata.get("source") == "exchange":
                        preload_results["exchange_calls"] += 1

                preload_results["strategies_processed"] += 1

                # Rate limiting para não sobrecarregar a exchange
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(
                    f"Erro no pré-carregamento inteligente para {requirement.strategy_name}: {e}"
                )

        preload_results["total_time_seconds"] = time.time() - start_time

        logger.info(
            f"Pré-carregamento inteligente concluído: {preload_results['strategies_processed']} estratégias, "
            f"{preload_results['data_loaded']} candles carregados em {preload_results['total_time_seconds']:.1f}s"
        )

        return preload_results
