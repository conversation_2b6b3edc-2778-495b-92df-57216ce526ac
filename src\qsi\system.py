import numpy as np
from typing import Dict, Any

from qsi.analyzer import RepoAnalyzer
from qsi.validator import QuantumCompatibilityValidator
from qsi.resonator import GeometricQSIResonator
from qsi.metrics import calculate_geometric_metrics
# Use explicit package path so the bundled ``qualia`` package is resolved when
# the repository root is executed directly. This avoids ``ModuleNotFoundError``
# during test collection without requiring external installation.
# The full QUALIA memory implementation requires heavy optional dependencies
# (scikit-learn, cuPy, etc.) which are not needed for the simplified behaviour
# exercised in the unit tests. To keep the tests lightweight and avoid extra
# requirements, use the minimal ``QuantumMemoryBank`` provided in this package.
from qualia.quantum_memory_bank import QuantumMemoryBank
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

class QSI:
    """
    A classe mestra do Quantum Synapse Integrator.

    Esta classe unifica todos os subsistemas (análise, validação, ressonância)
    em uma única entidade coesa, orquestrando o fluxo de trabalho completo
    de avaliação de um conceito ou artefato externo.
    """
    def __init__(
        self,
        dimensions: int = 8,
        *,
        phi1: float = 1.618,
        phi2: float = 2.618,
        U1: np.ndarray | None = None,
        U2: np.ndarray | None = None,
    ) -> None:
        """
        Inicializa o sistema QSI.

        Args:
            dimensions: A dimensionalidade do espaço de Hilbert no qual o
                        sistema irá operar sua cognição.
            phi1: Valor de φ utilizado para gerar a matriz U1 caso não seja
                  fornecida explicitamente.
            phi2: Valor de φ utilizado para gerar a matriz U2 caso não seja
                  fornecida explicitamente.
            U1: Matriz unitária inicial opcional.
            U2: Segunda matriz unitária opcional.
        """
        self.dimensions = dimensions
        self.resonator = GeometricQSIResonator(
            dimensions=self.dimensions,
            phi1=phi1,
            phi2=phi2,
            U1=U1,
            U2=U2,
        )
        # Inicializa o banco de memória para registrar métricas de avaliações
        self.memory_bank = QuantumMemoryBank()

        self.alignment_threshold = 0.6
        self.symmetry_threshold = 0.7
        logger.info("-" * 50)
        logger.info("Quantum Synapse Integrator (QSI) Inicializado")
        logger.info("   Espaço de Pensamento: %s dimensões", self.dimensions)
        logger.info("-" * 50)

    def _encode_data_to_state(self, repo_data: Dict[str, Any]) -> np.ndarray:
        """Codifica dados clássicos em um estado quântico inicial."""
        q_relevance = repo_data.get('quantum_relevance', 0.5)
        q_quality = repo_data.get('code_quality', 0.5)
        
        vector = np.zeros(self.dimensions, dtype=np.complex128)
        vector[0] = q_relevance
        vector[1] = 1 - q_relevance
        if self.dimensions > 2:
            vector[2] = q_quality
        if self.dimensions > 3:
            vector[3] = 1 - q_quality
        
        norm = np.linalg.norm(vector)
        return vector / norm if norm > 0 else np.ones(self.dimensions, dtype=np.complex128) / np.sqrt(self.dimensions)

    def _make_decision_from_metrics(self, metrics: Dict[str, float]) -> str:
        """Toma uma decisão clássica baseada nas métricas do estado final."""
        alignment = metrics.get('spectral_phi_alignment', 0)
        symmetry = metrics.get('golden_symmetry', 0)

        if alignment > self.alignment_threshold and symmetry > self.symmetry_threshold:
            return "APROVAR"
        elif alignment > self.alignment_threshold * 0.67 or symmetry > self.symmetry_threshold * 0.71:
            return "CONSULTAR"
        else:
            return "REJEITAR"

    def _update_internal_state(self, metrics: Dict[str, float]):
        """
        (Hook para Futuro Aprendizado)
        Usa as métricas de uma avaliação para refinar o estado interno do QSI.
        """
        logger.info(
            "\n    [Aprendizado] Hook de aprendizado ativado. Registrando métricas..."
        )

        # 1. Armazena as métricas no banco de memória
        self.memory_bank.add_metrics(metrics)

        # 2. Obtém a média das métricas registradas
        aggregated = self.memory_bank.aggregate_metrics()
        if aggregated:
            new_align = aggregated.get('spectral_phi_alignment', 0)
            new_sym = aggregated.get('golden_symmetry', 0)

            # 3. Ajusta gradualmente os limiares de decisão
            self.alignment_threshold = 0.9 * self.alignment_threshold + 0.1 * new_align
            self.symmetry_threshold = 0.9 * self.symmetry_threshold + 0.1 * new_sym

            logger.info(
                "    [Aprendizado] Limiar atualizado: alinhamento=%.2f, simetria=%.2f",
                self.alignment_threshold,
                self.symmetry_threshold,
            )

    def evaluate(self, repo_url: str) -> Dict[str, Any]:
        """Avalia um repositório e retorna métricas detalhadas.

        Esta rotina coordena todas as etapas do QSI: análise do código,
        validação de compatibilidade e aplicação do ressonador geométrico.

        Args:
            repo_url: URL do repositório Git a ser avaliado.

        Returns:
            Um dicionário contendo o veredito final e as métricas de ressonância.

        Exemplo:
            ```python
            system = QSI(dimensions=8)
            resultado = system.evaluate("https://github.com/exemplo/repo")
            ```
        """
        logger.info("\n[QSI] Iniciando avaliação completa de: %s", repo_url)
        
        # 1. Análise (Sentidos)
        analyzer = RepoAnalyzer(repo_url)
        analysis_results = analyzer.analyze()
        
        # 2. Validação (Discernimento Técnico)
        validator = QuantumCompatibilityValidator(analysis_results)
        if not validator.validate():
            return {"decision": "REJEITAR", "reason": "Falha na validação técnica"}
        
        # 3. Codificação para Estado Quântico
        initial_state = self._encode_data_to_state(analysis_results)
        
        # 4. Ressonância Geométrica (Cognição)
        final_state = self.resonator.apply_resonance(initial_state)
        
        # 5. Medição de Métricas
        metrics = calculate_geometric_metrics(final_state, self.resonator.U2)
        
        # 6. Decisão Baseada em Ressonância
        decision = self._make_decision_from_metrics(metrics)
        
        # 7. Gancho para Aprendizado Futuro
        self._update_internal_state(metrics) # Re-enabled
        
        logger.info("[QSI] Avaliação concluída. Veredito: %s", decision)
        return {"decision": decision, "metrics": metrics}
