from __future__ import annotations

"""Symbolic Intention Operator.

This module defines :class:`SymbolicIntentionOperator` responsible for
interpreting symbolic tokens produced by the :class:`IntentioWaveletEngine`.
The operator modulates parameters of :class:`~qualia.core.universe.QUALIAQuantumUniverse`
as a lightweight bridge between symbolic intentions and quantum dynamics.
"""

from typing import Sequence

from ..utils.logger import get_logger
from .universe import QUALIAQuantumUniverse


class SymbolicIntentionOperator:
    """Adjust ``QUALIAQuantumUniverse`` parameters based on symbolic tokens."""

    def __init__(
        self,
        *,
        amp_factor: float = 1.1,
        attenuation_factor: float = 0.9,
        depth_delta: int = 1,
    ) -> None:
        self.amp_factor = float(amp_factor)
        self.attenuation_factor = float(attenuation_factor)
        self.depth_delta = int(depth_delta)
        self.logger = get_logger(__name__)

    def apply_tokens(
        self, tokens: Sequence[str], universe: QUALIAQuantumUniverse
    ) -> None:
        """Apply symbolic tokens to influence the quantum universe.

        Parameters
        ----------
        tokens
            Sequence of tokens from ``IntentioWaveletEngine``.
        universe
            Instance of :class:`QUALIAQuantumUniverse` to be adjusted.
        """

        normalized = [token.lower() for token in tokens]

        if any(t in {"amplify", "increase"} for t in normalized):
            prev = universe.lambda_factor_multiplier
            universe.lambda_factor_multiplier *= self.amp_factor
            self.logger.info(
                "Symbolic amplify: lambda_factor_multiplier %.3f -> %.3f",
                prev,
                universe.lambda_factor_multiplier,
            )

        if any(t in {"attenuate", "reduce"} for t in normalized):
            prev = universe.lambda_factor_multiplier
            universe.lambda_factor_multiplier *= self.attenuation_factor
            self.logger.info(
                "Symbolic attenuate: lambda_factor_multiplier %.3f -> %.3f",
                prev,
                universe.lambda_factor_multiplier,
            )

        if "deepen" in normalized:
            prev = universe.scr_depth
            universe.scr_depth += self.depth_delta
            self.logger.info(
                "Symbolic deepen: scr_depth %d -> %d",
                prev,
                universe.scr_depth,
            )

        if "shallow" in normalized:
            prev = universe.scr_depth
            universe.scr_depth = max(1, universe.scr_depth - self.depth_delta)
            self.logger.info(
                "Symbolic shallow: scr_depth %d -> %d",
                prev,
                universe.scr_depth,
            )
