from __future__ import annotations

from dataclasses import dataclass, field
from threading import Lock
from typing import Any, Dict


@dataclass
class TradingState:
    """Thread-safe container for trading state."""

    data: Dict[str, Any] = field(
        default_factory=lambda: {
            "status": "stopped",
            "last_update": None,
            "error_message": None,
            "api_configured": False,
            "live_mode": False,
            "api_key": None,
            "api_secret": None,
            "exchange": None,
            "symbols": [],
            "risk_profile": "balanced",
            "initial_capital": 10000,
            "market_data": {},
            "positions": [],
            "trade_history": [],
            "performance_metrics": {
                "pnl": 0,
                "pnl_percentage": 0,
                "win_rate": 0,
                "drawdown": 0,
                "sharpe": 0,
                "trades": 0,
            },
            "quantum_metrics": {
                "entropy": 0,
                "coherence": 0,
                "active_states": 0,
                "page_entropy": 0,
                "otoc": 0,
                "quantum_coherence": 0,
                "loschmidt_echo": 0,
                "pattern_recognition_rate": 0,
                "self_reflection_depth": 0,
                "thermal_coefficient": 0.1,
                "retrocausal_coefficient": 0.0,
                "regime_change": 0,
            },
            "metric_history": [],
        }
    )
    _lock: Lock = field(default_factory=Lock, init=False, repr=False)

    def __getitem__(self, key: str) -> Any:
        with self._lock:
            return self.data[key]

    def __setitem__(self, key: str, value: Any) -> None:
        with self._lock:
            self.data[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        with self._lock:
            return self.data.get(key, default)

    def update(self, *args: Any, **kwargs: Any) -> None:
        with self._lock:
            self.data.update(*args, **kwargs)

    def update_quantum_metrics(self, metrics: Dict[str, Any]) -> None:
        """Thread-safe update of quantum_metrics."""
        with self._lock:
            self.data.setdefault("quantum_metrics", {}).update(metrics)

    def set_quantum_metric(self, key: str, value: Any) -> None:
        """Set a single quantum metric in a thread-safe manner."""
        with self._lock:
            self.data.setdefault("quantum_metrics", {})[key] = value

    def get_quantum_metric(self, key: str, default: Any = None) -> Any:
        """Retrieve a single quantum metric in a thread-safe manner."""
        with self._lock:
            return self.data.setdefault("quantum_metrics", {}).get(key, default)

    def get_quantum_metrics(self) -> Dict[str, Any]:
        """Return a copy of all quantum metrics in a thread-safe manner."""
        with self._lock:
            return dict(self.data.setdefault("quantum_metrics", {}))


def create_trading_state() -> TradingState:
    """Return a new ``TradingState`` instance."""

    return TradingState()


# Default instance kept for backward compatibility
TRADING_STATE = create_trading_state()
