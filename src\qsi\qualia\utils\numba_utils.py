"""Optional Numba utilities for QUALIA."""

from __future__ import annotations

import os
from typing import Any, Callable

try:  # pragma: no cover - optional dependency
    from numba import njit as _njit  # type: ignore

    _numba_available = True
except Exception:  # pragma: no cover - fallback when numba missing
    _numba_available = False
    _njit = None


def _use_numba() -> bool:
    """Return ``True`` when Numba should be enabled."""
    if not _numba_available:
        return False
    return os.getenv("QUALIA_USE_GPU", "false").lower() in {"1", "true", "yes"}


def optional_njit(
    *args: Any, **kwargs: Any
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Return decorator that applies ``numba.njit`` when available."""

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        if _use_numba():
            return _njit(*args, **kwargs)(func)  # type: ignore[misc]
        return func

    return decorator


__all__ = ["optional_njit"]
