from __future__ import annotations

from datetime import datetime, timedelta, timezone
from typing import List, Any, Optional

import os

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

try:
    import arxiv  # type: ignore
except ModuleNotFoundError:  # pragma: no cover
    arxiv = None  # type: ignore


class PaperFetcher:
    """Fetch recent machine learning papers from arXiv.

    Parameters
    ----------
    days_back : int, optional
        Number of days in the past to search from. Defaults to ``7``.
    max_results : int, optional
        Maximum number of papers to fetch. Defaults to ``40``.
    categories : str, optional
        Space-separated arXiv categories to query. Defaults to ``"cs.AI OR cs.LG"``
        or the value of environment variable ``FAR_CATEGORIES`` if set.
    """

    def __init__(
        self,
        days_back: int = 7,
        max_results: int = 40,
        *,
        categories: Optional[str] = None,
    ) -> None:
        """Create a new fetcher instance."""
        self.days_back = days_back
        self.max_results = max_results
        self.categories = categories or os.getenv("FAR_CATEGORIES", "cs.AI OR cs.LG")
        self.results: List[Any] = []

    def run(self) -> List[Any]:
        """Retrieve papers from arXiv.

        Returns
        -------
        list[Any]
            List of :class:`arxiv.Result` objects. Empty if fetching fails or the
            ``arxiv`` package is missing.
        """
        if arxiv is None:
            logger.warning("arxiv package missing; Fetcher returns empty list")
            return []
        start_time = datetime.now(timezone.utc) - timedelta(days=self.days_back)
        query = f"cat:{self.categories}"
        try:
            search = arxiv.Search(
                query=query,
                max_results=self.max_results,
                sort_by=arxiv.SortCriterion.SubmittedDate,
            )
            papers: List[arxiv.Result] = []
            for result in search.results():
                pub_date = result.published
                if pub_date.tzinfo is None:
                    pub_date = pub_date.replace(tzinfo=timezone.utc)
                else:
                    pub_date = pub_date.astimezone(timezone.utc)
                if pub_date > start_time:
                    papers.append(result)
                else:
                    break
            self.results = papers
            logger.info("PaperFetcher: got %d papers", len(papers))
            return papers
        except Exception as exc:  # pragma: no cover - best-effort logging
            logger.error("PaperFetcher: failed to fetch papers: %s", exc)
            self.results = []
            return []


class GitHubFetcher:
    """Fetch trending repositories from GitHub."""

    def __init__(
        self,
        query: str = "machine learning",
        max_results: int = 40,
        *,
        token: str | None = None,
    ) -> None:
        self.query = query
        self.max_results = max_results
        self.token = token or os.getenv("GITHUB_TOKEN")
        self.results: List[Any] = []

    def run(self) -> List[Any]:
        """Retrieve repositories from GitHub search API."""
        import requests

        headers = {"Accept": "application/vnd.github+json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        params = {
            "q": self.query,
            "sort": "stars",
            "order": "desc",
            "per_page": str(self.max_results),
        }
        try:
            resp = requests.get(
                "https://api.github.com/search/repositories",
                headers=headers,
                params=params,
                timeout=10,
            )
            resp.raise_for_status()
            data = resp.json()
            items = data.get("items", [])
            self.results = items
            logger.info("GitHubFetcher: got %d repos", len(items))
            return items
        except Exception as exc:  # pragma: no cover - best-effort logging
            logger.error("GitHubFetcher: failed to fetch repos: %s", exc)
            self.results = []
            return []


class TwitterFetcher:
    """Fetch recent tweets using the X API."""

    def __init__(
        self,
        query: str = "ai",
        max_results: int = 40,
        *,
        bearer_token: str | None = None,
    ) -> None:
        self.query = query
        self.max_results = max_results
        self.bearer_token = bearer_token or os.getenv("TWITTER_BEARER_TOKEN")
        self.results: List[Any] = []

    def run(self) -> List[Any]:
        """Retrieve tweets from Twitter API v2."""
        if not self.bearer_token:
            logger.warning("TwitterFetcher: bearer token not provided")
            self.results = []
            return []
        import requests

        headers = {"Authorization": f"Bearer {self.bearer_token}"}
        params = {"query": self.query, "max_results": str(self.max_results)}
        try:
            resp = requests.get(
                "https://api.twitter.com/2/tweets/search/recent",
                headers=headers,
                params=params,
                timeout=10,
            )
            resp.raise_for_status()
            data = resp.json().get("data", [])
            self.results = data
            logger.info("TwitterFetcher: got %d tweets", len(data))
            return data
        except Exception as exc:  # pragma: no cover - best-effort logging
            logger.error("TwitterFetcher: failed to fetch tweets: %s", exc)
            self.results = []
            return []


__all__ = ["PaperFetcher", "GitHubFetcher", "TwitterFetcher"]
