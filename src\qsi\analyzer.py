import os
import shutil
import tempfile
import zipfile
from typing import Dict, Any, List, Tuple
import subprocess
from concurrent.futures import ThreadPoolExecutor

from qsi.utils import safe_extract_zip, run_command

class RepoAnalyzer:
    """
    Analisador autônomo de repositórios GitHub.
    Este módulo serve como os "sentidos" externos do QSI, sondando o ambiente
    informacional em busca de padrões e relevância quântica.
    """
    def __init__(self, repo_url: str):
        """
        Inicializa o analisador com a URL do repositório alvo.

        Args:
            repo_url: A URL do repositório GitHub a ser analisado.
        """
        self.repo_url = repo_url
        self.quantum_signatures = [
            'quantum', 'qubit', 'qiskit', 'pennylane', 
            'cirq', 'braket', 'pyquil',
            'superposition', 'entanglement', 'decoherence',
            'grover', 'shor', 'vqe'
        ]
        print(f"RepoAnalyzer inicializado para: {self.repo_url}")

    def _download_and_extract_repo(self) -> str:
        """
        Baixa o repositório como um arquivo zip e o extrai para um diretório temporário.
        """
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, 'repo.zip')
        extracted_path = os.path.join(temp_dir, 'repo')

        try:
            # Constrói a URL do zip a partir da URL do repositório
            zip_url = self.repo_url + '/archive/master.zip'
            print(f"    ...baixando repositório de {zip_url}...")
            
            # Usa curl para baixar o arquivo
            run_command(['curl', '-L', '-o', zip_path, zip_url], check=True)

            print(f"    ...extraindo repositório em {extracted_path}...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                safe_extract_zip(zip_ref, extracted_path)
            
            # O conteúdo é extraído para um subdiretório, então encontramos ele
            unzipped_folder = os.path.join(extracted_path, os.listdir(extracted_path)[0])
            return unzipped_folder

        except (subprocess.CalledProcessError, zipfile.BadZipFile, IndexError) as e:
            print(f"    Erro ao baixar ou extrair o repositório: {e}")
            shutil.rmtree(temp_dir)
            return None

    def _analyze_file(self, file_path: str) -> Tuple[bool, int]:
        """Analisa um único arquivo e retorna se possui termos quânticos e quantos."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().lower()
                matches = sum(1 for sig in self.quantum_signatures if sig in content)
                return (matches > 0, matches)
        except Exception as e:
            print(f"    Erro ao ler o arquivo {file_path}: {e}")
            return (False, 0)

    def _calculate_quantum_relevance(self, repo_path: str) -> float:
        """Calcula a pontuação de relevância quântica do repositório."""
        print("    ...calculando relevância quântica...")

        file_paths: List[str] = []
        for root, _, files in os.walk(repo_path):
            for file in files:
                if file.endswith(('.py', '.md', '.txt', '.ipynb')):
                    file_paths.append(os.path.join(root, file))

        total_files = len(file_paths)
        quantum_files = 0
        total_matches = 0

        max_workers = min(32, (os.cpu_count() or 1) * 2)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            for is_quantum, matches in executor.map(self._analyze_file, file_paths):
                if is_quantum:
                    quantum_files += 1
                    total_matches += matches

        if total_files == 0:
            return 0.0

        relevance = (quantum_files / total_files) * (1 + total_matches / (total_files * 10))
        return min(relevance, 1.0)

    def _assess_code_quality(self, repo_path: str) -> float:
        """Avalia a qualidade geral do código utilizando ``flake8`` e ``radon``.

        O score final é a média entre a nota de estilo (flake8) e a nota de
        complexidade ciclomática (radon). Caso alguma ferramenta falhe,
        assume-se nota 0 para ela.
        """
        print("    ...avaliando qualidade do código...")

        # Executa flake8 para contar problemas de estilo
        flake8_proc = run_command(
            ["flake8", repo_path], capture_output=True, text=True
        )
        flake8_issues = len(flake8_proc.stdout.splitlines()) + len(
            flake8_proc.stderr.splitlines()
        )
        flake8_score = max(0.0, 1 - min(flake8_issues, 50) / 50)

        # Executa radon para obter a média de complexidade ciclomática
        radon_proc = run_command(
            ["radon", "cc", "-s", "-a", repo_path], capture_output=True, text=True
        )
        complexity_grade = None
        for line in radon_proc.stdout.splitlines():
            if line.startswith("Average complexity:"):
                # Saída típica: "Average complexity: A (2.3)"
                parts = line.split(":", 1)[1].strip().split()
                if parts:
                    complexity_grade = parts[0]
                break

        grade_map = {"A": 1.0, "B": 0.9, "C": 0.8, "D": 0.7, "E": 0.6, "F": 0.5}
        complexity_score = grade_map.get(complexity_grade, 0.0)

        final_score = round((flake8_score + complexity_score) / 2, 2)
        return final_score

    def _check_dependencies(self, repo_path: str) -> Dict[str, str]:
        """Lê o arquivo ``requirements.txt`` e lista as dependências."""
        print("    ...verificando dependências...")

        dependencies: Dict[str, str] = {}
        requirements_file = os.path.join(repo_path, "requirements.txt")

        if not os.path.isfile(requirements_file):
            print("    Nenhum requirements.txt encontrado.")
            return dependencies

        try:
            with open(requirements_file, "r", encoding="utf-8") as req:
                for line in req:
                    line = line.strip()
                    if not line or line.startswith("#"):
                        continue
                    if "==" in line:
                        name, version = line.split("==", 1)
                        dependencies[name.strip()] = version.strip()
                    else:
                        # Caso não haja versão especificada
                        dependencies[line] = ""
        except Exception as e:
            print(f"    Erro ao ler dependências: {e}")

        return dependencies

    def _evaluate_conceptual_fit(self) -> str:
        """
        Avalia o alinhamento conceitual do repositório com a arquitetura YAA.
        (Placeholder)
        """
        print("    ...avaliando alinhamento conceitual...")
        # Lógica futura: usar LLMs para analisar READMEs e documentação
        return "Alto alinhamento com finanças quânticas" # Retornando um valor mockado

    def analyze(self) -> Dict[str, Any]:
        """
        Executa a análise multidimensional completa do repositório.

        Returns:
            Um dicionário contendo os resultados da análise.
        """
        print(f"Executando análise multidimensional em {self.repo_url}...")
        
        repo_path = self._download_and_extract_repo()
        if not repo_path:
            return {"error": "Falha ao baixar e extrair o repositório."}

        try:
            # ``_assess_code_quality`` pode ser monkeypatched em testes sem
            # aceitar argumentos. Tentamos passar ``repo_path`` e, em caso de
            # ``TypeError``, chamamos novamente sem parâmetros.
            try:
                code_quality = self._assess_code_quality(repo_path)
            except TypeError:
                code_quality = self._assess_code_quality()

            analysis_results = {
                'quantum_relevance': self._calculate_quantum_relevance(repo_path),
                'code_quality': code_quality,
                'dependency_compatibility': self._check_dependencies(repo_path),
                'conceptual_alignment': self._evaluate_conceptual_fit()
            }
        finally:
            # O diretório pai do repo_path é o temp_dir original
            shutil.rmtree(os.path.dirname(os.path.dirname(repo_path)))
            
        print("Análise concluída.")
        return analysis_results
