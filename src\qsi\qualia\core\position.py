# coding: utf-8
"""
<PERSON>ste módulo define a estrutura para representar uma posição de trading aberta.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any
from ..common_types import (
    QuantumSignaturePacket,
)


@dataclass
class OpenPosition:
    """Container para informações de uma posição em aberto.

    Parameters
    ----------
    symbol:
        Código do ativo negociado.
    order_id:
        Identificador único da ordem relacionada.
    entry_price:
        Preço de entrada da operação.
    size:
        Quantidade negociada do ativo.
    side:
        Direção da operação, ``"buy"`` ou ``"sell"``.
    timestamp:
        Momento de criação da posição.
    stop_loss:
        Preço de stop loss definido, se existir.
    take_profit:
        Preço de take profit definido, se existir.
    status:
        Estado atual da posição. Valores possíveis: ``"open"``,
        ``"closed_sl"``, ``"closed_tp"`` ou ``"closed_manual"``.
    pnl:
        <PERSON><PERSON> ou prejuízo acumulado da posição.
    pnl_pct:
        Percentual de lucro ou prejuízo acumulado.
    captured_quantum_signature_packet:
        Pacote de assinatura quântica capturado no momento da decisão.
    market_snapshot_at_decision:
        Informações de mercado disponíveis no momento da abertura.
    decision_context_details:
        Metadados complementares sobre o contexto da decisão.
    total_fees:
        Soma das taxas pagas até o momento.
    """

    symbol: str
    order_id: str
    entry_price: float
    size: float
    side: str  # "buy" ou "sell"
    timestamp: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    status: str = "open"  # "open", "closed_sl", "closed_tp", "closed_manual"
    pnl: float = 0.0
    pnl_pct: float = 0.0
    captured_quantum_signature_packet: Optional[QuantumSignaturePacket] = field(
        default=None, repr=False
    )
    market_snapshot_at_decision: Optional[Dict[str, Any]] = field(
        default=None, repr=False
    )
    decision_context_details: Optional[Dict[str, Any]] = field(default=None, repr=False)
    total_fees: float = 0.0

    def __post_init__(self) -> None:
        """Validate the instantiated data."""
        if self.side not in {"buy", "sell"}:
            raise ValueError("side must be 'buy' or 'sell'")
