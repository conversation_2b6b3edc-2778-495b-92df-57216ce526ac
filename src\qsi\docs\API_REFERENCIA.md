# Referência de API do Quantum Synapse Integrator

Este documento apresenta uma visão geral dos principais módulos, classes e funções públicas do projeto **QSI**. Cada seção contém descrições breves, parâmetros mais importantes e exemplos de uso em Português brasileiro.

## qsi.system

### Classe `QSI`

Responsável por orquestrar a análise, validação e ressonância quântica.

```python
from qsi.system import QSI

sistema = QSI(dimensions=8)
resultado = sistema.evaluate("https://github.com/exemplo/repo")
print(resultado)
```

- **dimensions**: tamanho do espaço de Hilbert usado pelo ressonador.
- **phi1**, **phi2**: valores de φ para gerar as matrizes unitárias padrão.
- **evaluate(repo_url)**: executa todo o fluxo de avaliação e retorna um dicionário com `decision` e `metrics`.

## qsi.analyzer

### Classe `RepoAnalyzer`

Realiza download e inspeção de um repositório Git. Calcula relevância quântica, qualidade de código e verifica dependências.

```python
from qsi.analyzer import RepoAnalyzer

analyzer = RepoAnalyzer("https://github.com/exemplo/repo")
info = analyzer.analyze()
```

Principais métodos privados:
- `_calculate_quantum_relevance(repo_path)` – busca termos quânticos no código.
- `_assess_code_quality(repo_path)` – executa `flake8` e `radon`.

## qsi.validator

### Classe `QuantumCompatibilityValidator`

Valida código quanto à sintaxe, princípios quânticos e execução segura em sandbox.

```python
from qsi.validator import QuantumCompatibilityValidator

validator = QuantumCompatibilityValidator(info)
validator.validate()
```

## qsi.resonator

### Classe `GeometricQSIResonator`

Aplica duas transformações unitárias baseadas na proporção áurea.

```python
from qsi.resonator import GeometricQSIResonator

res = GeometricQSIResonator(dimensions=4)
novo_estado = res.apply_resonance(estado_inicial)
```

## qsi.geometry

- `build_phi_unitary(dim, phi)` – gera matriz unitária inspirada em φ.
- `qsi.validation.validate_quantum_state(func)` – decorador que garante que o vetor de estado de entrada esteja normalizado.

## qsi.metrics

- `calculate_geometric_metrics(state, resonance_matrix)` – produz métricas como `spectral_phi_alignment` e `golden_symmetry` a partir do estado final.

## qsi.consciousness

### Classe `QuantumConsciousnessModule`

Módulo de decisão que usa ressonância geométrica para classificar repositórios.

```python
from qsi.consciousness import QuantumConsciousnessModule

qc = QuantumConsciousnessModule(dimensions=8)
veredito = qc.evaluate_integration(info)
```

## qsi.memory

### Classe `QuantumMemoryBank`

Armazena métricas históricas e calcula médias agregadas.

```python
from qsi.memory import QuantumMemoryBank

mem = QuantumMemoryBank()
mem.add_metrics({"golden_symmetry": 0.8})
media = mem.aggregate_metrics()
```

## Scripts Úteis

### `main.py`

Demonstra o fluxo completo. Basta executar:

```bash
python main.py
```

### `dashboard.py`

Inicia uma interface web em Flask para avaliar repositórios via navegador.

```bash
python dashboard.py
# acessar http://localhost:5000
```

### `batch_cli.py`

Processa vários repositórios a partir de um arquivo texto.

```bash
python batch_cli.py repos.txt --dimensions 8 --output resultados.json
```


