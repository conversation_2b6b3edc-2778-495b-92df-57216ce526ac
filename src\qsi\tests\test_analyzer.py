import shutil
from qsi.analyzer import <PERSON>oAnaly<PERSON>


def create_mock_repo(tmp_path):
    repo = tmp_path / "repo"
    repo.mkdir()
    (repo / "script.py").write_text("quantum magic")
    (repo / "README.md").write_text("qiskit qubit quantum")
    return repo


def test_calculate_quantum_relevance(tmp_path):
    repo = create_mock_repo(tmp_path)
    analyzer = RepoAnalyzer("http://example.com/repo")
    relevance = analyzer._calculate_quantum_relevance(str(repo))
    assert relevance == 1.0


def test_analyze_aggregates_metrics(tmp_path, monkeypatch):
    repo = create_mock_repo(tmp_path)
    analyzer = RepoAnalyzer("http://example.com/repo")

    monkeypatch.setattr(analyzer, "_download_and_extract_repo", lambda: str(repo))
    # ``_assess_code_quality`` espera o caminho do repositório; o lambda de
    # teste precisa aceitar esse argumento para evitar ``TypeError`` durante a
    # execução.
    monkeypatch.setattr(analyzer, "_assess_code_quality", lambda path: 0.9)
    monkeypatch.setattr(analyzer, "_check_dependencies", lambda path: {"dep": "1.0"})
    monkeypatch.setattr(analyzer, "_evaluate_conceptual_fit", lambda: "High")
    monkeypatch.setattr(shutil, "rmtree", lambda path: None)

    results = analyzer.analyze()
    assert results == {
        "quantum_relevance": 1.0,
        "code_quality": 0.9,
        "dependency_compatibility": {"dep": "1.0"},
        "conceptual_alignment": "High",
    }
