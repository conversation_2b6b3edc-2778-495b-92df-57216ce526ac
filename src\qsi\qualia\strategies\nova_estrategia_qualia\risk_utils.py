"""Risk calculation utilities for :mod:`nova_estrategia_qualia`."""

from __future__ import annotations

from typing import Optional, Tuple

import numpy as np
import time
from datadog import DogStatsd

from ...signals import SignalType
from ...config.nova_risk_defaults import load_nova_risk_defaults

_DEFAULTS = load_nova_risk_defaults()


def calculate_sl_tp_multipliers(confidence: float) -> Tuple[float, float]:
    """Return stop-loss and take-profit multipliers.

    Parameters
    ----------
    confidence
        Strength of the trading signal in ``[0, 1]``.

    Returns
    -------
    tuple
        ``(sl_multiplier, tp_multiplier)`` used with ATR based stops.

    Notes
    -----
    Multipliers expand or contract according to ``confidence``. Higher
    confidence increases the take-profit target while reducing the stop-loss
    distance, reflecting the strategy's willingness to risk more when it is
    more certain about market direction.
    """
    sl_base = float(_DEFAULTS.get("sl_multiplier_base", 3.0))
    tp_base = float(_DEFAULTS.get("tp_multiplier_base", 2.0))
    sl_multiplier = sl_base * (1.0 - min(confidence, 0.5))
    tp_multiplier = tp_base * (1.0 + confidence)
    return sl_multiplier, tp_multiplier


def atr_stop_take_profit(
    close_values: np.ndarray,
    decision_signal: SignalType,
    confidence: float,
    transaction_cost: float,
    *,
    atr_period: int | None = None,
    statsd_client: DogStatsd | None = None,
) -> Tuple[Optional[float], Optional[float]]:
    """Calculate stop loss and take profit prices using ATR.

    Parameters
    ----------
    close_values
        Array of closing prices with the most recent value at the end.
    decision_signal
        Either ``"buy"`` or ``"sell"``; ``"hold"`` returns ``(None, None)``.
    confidence
        Strength of the decision in ``[0, 1]`` passed to
        :func:`calculate_sl_tp_multipliers`.
    transaction_cost
        Estimated round-trip transaction cost fraction.
    atr_period
        Window size for the ATR calculation.

    Returns
    -------
    tuple
        ``(stop_loss_price, take_profit_price)`` or ``(None, None)`` if there is
        insufficient data.

    Notes
    -----
    The ATR adjustment scales with multipliers derived from ``confidence`` so
    that higher confidence leads to tighter stops and more ambitious profit
    targets.
    """
    if decision_signal is SignalType.HOLD or len(close_values) == 0:
        return None, None
    start_time = time.perf_counter()

    period = atr_period or int(_DEFAULTS.get("atr_period", 14))

    current_price = close_values[-1]
    if len(close_values) < period + 1:
        if statsd_client:
            statsd_client.timing(
                "nova_estrategia.atr_stop_tp_ms",
                (time.perf_counter() - start_time) * 1000,
            )
        return None, None

    close_changes = np.abs(np.diff(close_values[-period - 1 :]))
    atr_value = float(np.mean(close_changes))

    sl_multiplier, tp_multiplier = calculate_sl_tp_multipliers(confidence)

    if decision_signal is SignalType.BUY:
        stop_loss = current_price * (1.0 - (sl_multiplier * atr_value / current_price))
        take_profit = current_price * (
            1.0 + (tp_multiplier * atr_value / current_price)
        )
    elif decision_signal is SignalType.SELL:
        stop_loss = current_price * (1.0 + (sl_multiplier * atr_value / current_price))
        take_profit = current_price * (
            1.0 - (tp_multiplier * atr_value / current_price)
        )
    else:
        return None, None

    min_profit = current_price * (2 * transaction_cost)
    if decision_signal is SignalType.BUY:
        take_profit = max(take_profit, current_price + min_profit)
    else:
        take_profit = min(take_profit, current_price - min_profit)
    if statsd_client:
        statsd_client.timing(
            "nova_estrategia.atr_stop_tp_ms",
            (time.perf_counter() - start_time) * 1000,
        )
    return float(stop_loss), float(take_profit)
