"""Meta controller for quantum metrics adjustments."""

from __future__ import annotations

from typing import Any, Dict

from pydantic import BaseModel

from ..utils.logger import get_logger
from .strategy_interface import OrderDecision

logger = get_logger(__name__)


class MetaQuantumController(BaseModel):
    """Adjust trading decisions based on quantum metrics.

    The controller inspects metrics such as entropy and coherence and
    modifies the generated :class:`OrderDecision` accordingly. Before a
    strategy finalizes an order, it can call
    :meth:`process_decision` to incorporate the latest metacognitive
    insights.

    The metacognition engine may update ``entropy_threshold`` and
    ``coherence_threshold`` through external feedback loops. Strategies
    should provide the controller with a reference to the active
    metacognition component if they wish thresholds to self‑adjust.

    Parameters
    ----------
    entropy_threshold
        Threshold above which exposure is reduced.
    coherence_threshold
        Threshold below which directional signals are downgraded.
    metacognition
        Optional metacognition interface used to refine thresholds.
    """

    entropy_threshold: float = 0.7
    coherence_threshold: float = 0.5
    metacognition: Any | None = None

    def process_decision(
        self, decision: OrderDecision, metrics: Dict[str, float]
    ) -> OrderDecision:
        """Return a new decision adjusted by quantum metrics."""
        ent = float(metrics.get("entropy", 0.0))
        coh = float(metrics.get("coherence", 1.0))

        # Optional metacognitive feedback
        if self.metacognition and hasattr(self.metacognition, "observe_metrics"):
            try:
                self.metacognition.observe_metrics(metrics)
                feedback = getattr(self.metacognition, "get_feedback", lambda: None)()
                if isinstance(feedback, dict):
                    self.entropy_threshold = float(
                        feedback.get("entropy_threshold", self.entropy_threshold)
                    )
                    self.coherence_threshold = float(
                        feedback.get("coherence_threshold", self.coherence_threshold)
                    )
            except Exception as exc:  # pragma: no cover - defensive
                logger.warning("Metacognition feedback failed: %s", exc)

        new_data = decision.dict()

        # Downgrade signal if coherence is too low
        if coh < self.coherence_threshold and new_data["signal"] in {"BUY", "SELL"}:
            new_data["signal"] = "HOLD"
            new_data["confidence"] *= 0.5
            new_data.setdefault("reasons", []).append(
                "MetaQuantumController: low coherence"
            )

        # Scale position if entropy is high
        if ent > self.entropy_threshold:
            size = new_data.get("size_factor", 1.0)
            new_data["size_factor"] = size * 0.5
            new_data.setdefault("reasons", []).append(
                "MetaQuantumController: high entropy"
            )

        logger.debug("Decision adjusted by MetaQuantumController: %s", new_data)
        return OrderDecision(**new_data)
