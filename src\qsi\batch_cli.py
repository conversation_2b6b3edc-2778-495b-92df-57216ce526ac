import argparse
import json
from typing import List
from qsi.system import QSI


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Executa o QSI em lote a partir de uma lista de repositórios"
    )
    parser.add_argument(
        "repos_file",
        help="Caminho para o arquivo contendo URLs de repositórios, um por linha",
    )
    parser.add_argument(
        "--dimensions",
        type=int,
        default=8,
        help="Dimensionalidade do espaço de Hilbert utilizado pelo QSI",
    )
    parser.add_argument(
        "--phi1",
        type=float,
        default=1.618,
        help="Valor de φ para a unitária U1",
    )
    parser.add_argument(
        "--phi2",
        type=float,
        default=2.618,
        help="Valor de φ para a unitária U2",
    )
    parser.add_argument(
        "--output",
        help="Arquivo de saída para salvar os resultados em formato JSON",
    )
    return parser.parse_args()


def load_repos(path: str) -> List[str]:
    with open(path, "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip()]


def main() -> None:
    args = parse_args()
    repos = load_repos(args.repos_file)

    results = []
    for repo_url in repos:
        system = QSI(dimensions=args.dimensions)
        result = system.evaluate(repo_url=repo_url)
        results.append({"repo": repo_url, **result})

    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

    # Impressão simples dos resultados
    for entry in results:
        print("\n=== Resultado para", entry["repo"], "===")
        print(json.dumps(entry, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
