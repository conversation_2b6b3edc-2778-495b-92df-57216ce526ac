"""Métricas fractais e entropia mórfica."""

from __future__ import annotations

from typing import Any, List, Tuple

import numpy as np

try:  # pragma: no cover - optional dependency
    import pywt
except Exception:  # pragma: no cover - fallback when pywt is missing
    pywt = None


def compute_fractal_dimension(field_tensor: np.ndarray) -> float:
    """Estima a dimensão fractal de ``field_tensor`` usando o método box-counting.

    Parameters
    ----------
    field_tensor
        Arranjo numérico a ser analisado. Será achatado antes do cálculo.

    Returns
    -------
    float
        Dimensão fractal estimada no intervalo ``[1.0, 2.0]``.
    """
    vector = np.asarray(field_tensor).astype(float).ravel()
    n = vector.size
    if n < 4:
        return 1.0

    norm_vector = (vector - np.min(vector)) / (np.max(vector) - np.min(vector) + 1e-10)
    scales = np.logspace(0.1, np.log10(n / 4.0), 10)
    counts: List[int] = []
    for scale in scales:
        box_size = int(max(1, scale))
        boxes = int(np.ceil(n / box_size))
        count = 0
        for i in range(boxes):
            start = i * box_size
            end = min((i + 1) * box_size, n)
            box = norm_vector[start:end]
            if len(box) > 0 and (np.max(box) - np.min(box)) > 1e-6:
                count += 1
        counts.append(count)

    if len(counts) > 2:
        log_scales = np.log(scales)
        log_counts = np.log(np.array(counts) + 1e-10)
        slope = np.polyfit(log_scales, log_counts, 1)[0]
        dimension = 1.0 + abs(slope)
        return float(max(1.0, min(2.0, dimension)))
    return 1.0


def compute_morphic_entropy(coeffs: List[Any] | Tuple[Any, ...]) -> float:
    """Calcula a entropia mórfica a partir de coeficientes wavelet.

    Parameters
    ----------
    coeffs
        Saída de ``pywt.wavedec2`` ou funções equivalentes.

    Returns
    -------
    float
        Valor de entropia de Shannon sobre a magnitude dos coeficientes.
    """
    flattened: List[np.ndarray] = []
    for c in coeffs:
        if isinstance(c, tuple):
            for sub in c:
                flattened.append(np.asarray(sub).ravel())
        else:
            flattened.append(np.asarray(c).ravel())

    if not flattened:
        return 0.0

    data = np.concatenate(flattened)
    probs = np.abs(data)
    total = probs.sum()
    if total == 0:
        return 0.0

    probs /= total
    entropy = -np.sum(probs * np.log2(probs + 1e-12))
    return float(entropy)


def compute_fractal_metrics(field_tensor: np.ndarray) -> dict[str, Any]:
    """Computa dimensão fractal e entropia mórfica de ``field_tensor``.

    Parameters
    ----------
    field_tensor
        Matriz ou vetor numérico a ser decomposto.

    Returns
    -------
    dict
        Dicionário contendo ``fractal_dimension``, ``wavelet_coefficients`` e
        ``morphic_entropy``.
    """
    try:
        coeffs = pywt.wavedec2(field_tensor, "haar")
    except AttributeError:  # pragma: no cover - fallback for minimal installs
        coeffs = pywt.wavedec(field_tensor, "haar")
    fractal_dim = compute_fractal_dimension(field_tensor)
    entropy = compute_morphic_entropy(coeffs)
    return {
        "fractal_dimension": fractal_dim,
        "wavelet_coefficients": coeffs,
        "morphic_entropy": entropy,
    }


__all__ = [
    "compute_fractal_metrics",
    "compute_fractal_dimension",
    "compute_morphic_entropy",
]
