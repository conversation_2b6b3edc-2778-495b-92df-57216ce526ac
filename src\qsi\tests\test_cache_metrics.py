import os
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import pandas as pd
from qualia.utils.cache import cached, cached_dataframe, clear_cache, _memory_cache


def test_cached_metrics_increment(monkeypatch):
    calls = []
    monkeypatch.setattr('qualia.utils.cache.persist_metric_to_qpm', lambda name, success: calls.append((name, success)))

    @cached(expiry_seconds=60)
    def add(a, b):
        return a + b

    func_id = f"{add.__module__}.{add.__name__}"
    clear_cache()
    _memory_cache['metadata'].clear()

    assert add(1, 2) == 3
    assert _memory_cache['metadata'][func_id]['misses'] == 1
    assert _memory_cache['metadata'][func_id]['hits'] == 0
    assert calls[-1] == ('cache_hit', False)

    assert add(1, 2) == 3
    assert _memory_cache['metadata'][func_id]['hits'] == 1
    assert calls[-1] == ('cache_hit', True)


def test_cached_dataframe_metrics_increment(monkeypatch, tmp_path):
    calls = []
    monkeypatch.setattr('qualia.utils.cache.persist_metric_to_qpm', lambda name, success: calls.append((name, success)))

    @cached_dataframe(expiry_seconds=60, cache_file_path=tmp_path)
    def make_df(x):
        return pd.DataFrame({'x': [x]})

    func_id = f"{make_df.__module__}.{make_df.__name__}"
    clear_cache()
    _memory_cache['metadata'].clear()

    df1 = make_df(1)
    assert df1.equals(pd.DataFrame({'x': [1]}))
    assert _memory_cache['metadata'][func_id]['misses'] == 1
    assert calls[-1] == ('cache_hit', False)

    df2 = make_df(1)
    assert df2.equals(pd.DataFrame({'x': [1]}))
    assert _memory_cache['metadata'][func_id]['hits'] == 1
    assert calls[-1] == ('cache_hit', True)

    clear_cache(func_id)
    df3 = make_df(1)
    assert df3.equals(pd.DataFrame({'x': [1]}))
    assert _memory_cache['metadata'][func_id]['hits'] == 2
    assert calls[-1] == ('cache_hit', True)
