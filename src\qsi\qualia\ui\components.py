"""
UI components for the QUALIA Quantum Dashboard

Este módulo contém componentes de interface de usuário reutilizáveis para o
Dashboard Quântico QUALIA, incluindo seletores, controles de parâmetros e
elementos visuais para exibição de resultados.

Utiliza Flask para renderização de templates e processamento de formulários.
"""

import pandas as pd
from typing import Any, Dict, List, Optional, Union
from flask import request


def algorithm_selector() -> str:
    """
    Create a selector for quantum algorithms

    Returns:
        Selected algorithm name
    """
    algorithms = ["QFT", "Grover", "VQE", "QUALIA Universe"]

    # No Flask, essa função retornará os algoritmos para serem usados em um template
    # ou simplesmente usará o valor de um parâmetro HTTP
    selected = request.args.get("algorithm", "QFT")

    # Verificar se o algoritmo selecionado é válido
    if selected not in algorithms:
        selected = algorithms[0]

    return selected


def parameter_controls(algorithm: str) -> Dict[str, Any]:
    """
    Parse algorithm parameters from request

    Args:
        algorithm: The selected algorithm name

    Returns:
        Dictionary of parameter values
    """
    params = {}

    # Common parameters with robust parsing
    n_qubits = request.args.get("n_qubits", default=3, type=int)
    if n_qubits is None or n_qubits < 1:
        n_qubits = 3
    params["n_qubits"] = n_qubits

    shots = request.args.get("shots", default=1024, type=int)
    params["shots"] = shots if shots is not None else 1024

    # Algorithm-specific parameters
    if algorithm == "Grover":
        max_state = 2 ** params["n_qubits"] - 1
        default_target = min(1, max_state)
        target_decimal = request.args.get(
            "target_decimal", default=default_target, type=int
        )
        if target_decimal is None:
            target_decimal = default_target
        target_decimal = max(0, min(target_decimal, max_state))
        params["target_decimal"] = target_decimal
        params["target_binary"] = format(target_decimal, f"0{params['n_qubits']}b")

    elif algorithm == "VQE":
        depth = request.args.get("depth", default=2, type=int)
        params["depth"] = depth if depth is not None else 2

    elif algorithm == "QUALIA Universe":
        steps = request.args.get("steps", default=10, type=int)
        params["steps"] = steps if steps is not None else 10
        params["thermal"] = request.args.get("thermal", "false").lower() == "true"

        # Opções para retro_mode (incluindo Deutsch CTC)
        retro_options = ["none", "standard", "future_phase_echo", "deutsch_ctc"]
        retro_mode = request.args.get("retro_mode", "none")
        params["retro_mode"] = retro_mode if retro_mode in retro_options else "none"

        # Parâmetros adicionais para Deutsch CTC
        if params["retro_mode"] == "deutsch_ctc":
            n_ctc = request.args.get("n_qubits_ctc", default=1, type=int)
            n_ctc = n_ctc if n_ctc is not None else 1
            params["n_qubits_ctc"] = min(n_ctc, min(3, params["n_qubits"] - 1))

            ctc_options = ["cnot", "swap", "toffoli", "custom"]
            ctc_interaction = request.args.get("ctc_interaction", "cnot")
            params["ctc_interaction"] = (
                ctc_interaction if ctc_interaction in ctc_options else "cnot"
            )

        # QAST Feedback Loop Option
        params["use_qast_feedback"] = (
            request.args.get("use_qast_feedback", "false").lower() == "true"
        )

        if params["use_qast_feedback"]:
            max_steps = request.args.get("qast_max_steps", default=50, type=int)
            entropy_th = request.args.get(
                "qast_entropy_threshold", default=0.005, type=float
            )
            quantum_th = request.args.get(
                "qast_quantum_threshold", default=0.0005, type=float
            )
            stable_cycles = request.args.get("qast_stable_cycles", default=3, type=int)
            params["qast_max_steps"] = max_steps if max_steps is not None else 50
            params["qast_entropy_threshold"] = (
                entropy_th if entropy_th is not None else 0.005
            )
            params["qast_quantum_threshold"] = (
                quantum_th if quantum_th is not None else 0.0005
            )
            params["qast_stable_cycles"] = (
                stable_cycles if stable_cycles is not None else 3
            )

    return params


def backend_selector() -> str:
    """
    Create a selector for quantum backends

    Returns:
        Selected backend name
    """
    backends = ["statevector_simulator", "qasm_simulator", "aer_simulator"]

    selected = request.args.get("backend", "statevector_simulator")

    # Verificar se o backend selecionado é válido
    if selected not in backends:
        selected = backends[0]

    return selected


def error_message_container() -> Optional[str]:
    """
    Display error message if exists in session state
    """
    # No Flask, erros são tipicamente armazenados em session (flask.session)
    # ou passados para o template via render_template
    from flask import session, flash

    if "error" in session and session["error"]:
        error_message = session["error"]
        # Limpar o erro após uso
        session.pop("error", None)
        # Flash pode ser usado para mensagens de erro em Flask
        flash(f"Error: {error_message}", "error")
        return error_message
    return None


def create_metrics_expander(
    metric_name: str, metric_values: List[Any]
) -> Dict[str, Any]:
    """
    Create metrics data for display

    Args:
        metric_name: Name of the metric
        metric_values: List of metric values

    Returns:
        Dictionary with formatted metric data for templates
    """
    # Format metric name for display
    display_name = metric_name.replace("_", " ").title()

    # Clean up the values
    clean_values = [v if v is not None else float("nan") for v in metric_values]

    # Preparar dados para o template
    metrics_data = {
        "name": display_name,
        "values": clean_values,
        "steps": list(range(len(clean_values))),
        "data_json": pd.DataFrame(
            {"Step": list(range(len(clean_values))), display_name: clean_values}
        ).to_json(orient="records"),
    }

    return metrics_data


def hypothesis_testing_controls() -> Dict[str, Any]:
    """
    Create controls for hypothesis testing section

    Returns:
        Dictionary of hypothesis testing parameters
    """
    params = {}

    hypotheses = [
        "H1: Entropia Quântica Adaptativa",
        "H2: Coerência sob Modulação",
        "H3: Emergência de Padrões OTOC",
        "H4: Meta-Estabilidade Temporal",
    ]

    # Obter parâmetros da requisição HTTP
    hypothesis_index = request.args.get("hypothesis_index", "0")
    try:
        index = int(hypothesis_index)
        if index < 0 or index >= len(hypotheses):
            index = 0
    except ValueError:
        index = 0

    params["hypothesis"] = hypotheses[index]
    params["n_qubits"] = int(request.args.get("n_qubits", 4))
    params["runtime"] = int(request.args.get("runtime", 50))
    params["noise_level"] = float(request.args.get("noise_level", 2.0))

    # Disponibilizar lista de opções para o template
    params["hypothesis_options"] = hypotheses

    return params


def market_analysis_controls() -> Dict[str, Any]:
    """
    Create controls for crypto market analysis section

    Returns:
        Dictionary of market analysis parameters
    """
    params = {}

    exchange_options = ["binance", "coinbase", "kraken", "ftx"]
    symbol_options = ["BTC/USDT", "ETH/USDT", "SOL/USDT", "BNB/USDT", "DOGE/USDT"]
    timeframe_options = ["1m", "5m", "15m", "1h", "4h", "1d"]

    # Obter parâmetros da requisição HTTP
    exchange = request.args.get("exchange", "kraken")
    if exchange not in exchange_options:
        exchange = "kraken"

    symbol = request.args.get("symbol", "BTC/USDT")
    if symbol not in symbol_options:
        symbol = "BTC/USDT"

    # Ajuste automático: se for Kraken e símbolo for BTC/USDT, converte para XBT/USD
    if exchange == "kraken" and symbol == "BTC/USDT":
        symbol = "XBT/USD"

    timeframe = request.args.get("timeframe", "1h")
    if timeframe not in timeframe_options:
        timeframe = "1h"

    # Parâmetros numéricos
    params["lookback"] = int(request.args.get("lookback", 14))
    params["quantum_qubits"] = int(request.args.get("quantum_qubits", 5))
    params["entropy_drop"] = float(request.args.get("entropy_drop", 8.0))

    # Parâmetros de seleção
    params["exchange"] = exchange
    params["symbol"] = symbol
    params["timeframe"] = timeframe

    # Disponibilizar listas de opções para o template
    params["exchange_options"] = exchange_options
    params["symbol_options"] = symbol_options
    params["timeframe_options"] = timeframe_options

    return params
