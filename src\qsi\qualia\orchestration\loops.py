"""Loops assíncronos do QUALIA Trading System.

Este módulo contém as rotinas utilizadas pelo
:class:`scripts.start_real_trading.QUALIATradingSystem` durante a
operação em tempo real. As funções recebem a instância do sistema como
primeiro argumento e preservam a assinatura dos métodos originais.
"""

from __future__ import annotations

import asyncio
import time
from datetime import datetime, timezone
from typing import TYPE_CHECKING

import numpy as np

# Importações de tipos comuns utilizados na persistência das decisões
from ..common_types import QuantumSignaturePacket

from ..utils.logger import get_logger
from ..event_bus import MarketDataUpdated
from ..event_bus import TradingSignalGenerated

logger = get_logger(__name__)

if TYPE_CHECKING:  # pragma: no cover - hints only
    from scripts.start_real_trading import QUALIATradingSystem
    from ..consciousness.enhanced_data_collector import EnhancedMarketData

__all__ = [
    "_data_collection_loop",
    "_holographic_evolution_loop",
    "_decision_cycle_loop",
    "_consciousness_loop",
    "_execution_cycle_loop",
    "_monitoring_loop",
    "_safety_monitoring_loop",
    "_metacognition_loop",
]


async def _interruptible_sleep(system: "QUALIATradingSystem", duration: float) -> bool:
    """Sleep que pode ser interrompido verificando shutdown a cada 0.5s.

    YAA: Solução para o problema de shutdown travado.

    Args:
        system: Instância do sistema de trading
        duration: Duração total do sleep em segundos

    Returns:
        True se completou o sleep, False se foi interrompido por shutdown
    """
    elapsed = 0.0
    check_interval = 0.5  # Verifica shutdown a cada 0.5 segundos

    while elapsed < duration:
        if system.shutdown_requested or not system.running:
            logger.debug(f"Sleep interrompido após {elapsed:.1f}s devido a shutdown")
            return False

        sleep_time = min(check_interval, duration - elapsed)
        await asyncio.sleep(sleep_time)
        elapsed += sleep_time

    return True


async def _data_collection_loop(system: "QUALIATradingSystem") -> None:
    """Executa a coleta de dados e dispara eventos individuais."""
    logger.info("📡 Data Collection Loop (Streaming Events) iniciado")

    while system.running and not system.shutdown_requested:
        try:
            # 1. Coleta de Notícias (mantém a lógica original)
            if system.real_data_collector and system.holographic_universe:
                news_events = []
                if system.enhanced_data_collector and hasattr(
                    system.enhanced_data_collector, "collect_news_events"
                ):
                    try:
                        news_events = await system.enhanced_data_collector.collect_news_events()
                    except Exception as e:
                        logger.error(f"❌ Erro coletando notícias: {e}")

                if news_events:
                    converter = system.enhanced_data_collector or system.real_data_collector
                    if hasattr(converter, "convert_to_holographic_events"):
                        news_holographic_events = converter.convert_to_holographic_events(
                            [], news_events, system.holographic_universe.field_size
                        )
                        if news_holographic_events:
                            logger.info(f"💉 Injetando {len(news_holographic_events)} eventos de NOTÍCIAS...")
                            for event in news_holographic_events:
                                await system.holographic_universe.inject_holographic_event(event)

            # 2. Coleta de Dados de Mercado (Lógica Refatorada)
            # A coleta agora é apenas acionada. Os eventos são publicados
            # de dentro do EnhancedDataCollector.
            if system.enhanced_data_collector:
                logger.debug("🔧 Acionando collect_enhanced_market_data() para streaming de eventos...")
                await system.enhanced_data_collector.collect_enhanced_market_data()
            else:
                logger.warning("🔧 EnhancedDataCollector não encontrado, pulando coleta de mercado.")

            # 3. Gerenciar intervalo de sleep
            # O loop agora pode ter um intervalo fixo, pois não depende mais
            # do retorno da função de coleta.
            collection_interval = system.config.get("timing", {}).get("data_collection_interval", 60.0)
            logger.debug(f"Aguardando {collection_interval}s para o próximo ciclo de coleta.")
            if not await _interruptible_sleep(system, collection_interval):
                break  # Shutdown solicitado

        except Exception as exc:
            logger.error(f"Erro no loop de coleta de dados: {exc}", exc_info=True)
            if not await _interruptible_sleep(system, 30.0):
                break


async def _holographic_evolution_loop(
    system: "QUALIATradingSystem", _payload: MarketDataUpdated
) -> None:
    """Executa um passo de evolução ao receber ``MarketDataUpdated``."""
    logger.info("🌀 Holographic Evolution triggered")

    try:
        current_time = time.time()
        await system.holographic_universe.step_evolution(current_time)

        patterns = system.holographic_universe.analyze_holographic_patterns()
        field_summary = system.holographic_universe.get_field_summary()
        system.holographic_metrics.append(
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "field_energy": field_summary.get("total_energy", 0),
                "field_entropy": field_summary.get("entropy", 0),
                "patterns_detected": len(patterns),
            }
        )
    except Exception as exc:  # pragma: no cover - unforeseen errors
        logger.error(f"Erro na evolução holográfica: {exc}", exc_info=True)


async def _decision_cycle_loop(
    system: "QUALIATradingSystem", _payload: MarketDataUpdated
) -> None:
    """Executa o ciclo de decisão ao receber ``MarketDataUpdated``."""
    cycle_count = getattr(system, "_decision_cycle_counter", 0) + 1
    system._decision_cycle_counter = cycle_count

    logger.info(
        f"🔄 DECISION CYCLE #{cycle_count} - {datetime.now().strftime('%H:%M:%S')}"
    )

    try:
        if not system.oracle_engine:
            logger.error("❌ Oracle engine não inicializado!")
            return

        oracle_decisions = await system.oracle_engine.consult_oracle()
        logger.info(f"🧠 Oracle gerou {len(oracle_decisions)} decisões")

        for i, decision in enumerate(oracle_decisions[:3]):
            logger.info(
                f"   Oracle {i+1}: {decision.symbol} {decision.action} "
                f"conf={decision.confidence:.3f} source={getattr(decision, 'source', 'unknown')}"
            )

        logger.info("🌀 Analisando padrões holográficos...")
        patterns = system.holographic_universe.analyze_holographic_patterns()
        logger.info(f"🌀 Detectados {len(patterns)} padrões holográficos")

        patterns_above_threshold = 0
        for i, pattern in enumerate(patterns[:5]):
            above_threshold = pattern.strength > 0.3 and pattern.confidence > 0.6
            if above_threshold:
                patterns_above_threshold += 1
            logger.info(
                f"   Padrão {i+1}: pos={pattern.position}, força={pattern.strength:.3f}, "
                f"conf={pattern.confidence:.3f}, tipo={pattern.pattern_type} "
                f"{'✅' if above_threshold else '❌'}"
            )

        logger.info(
            f"🎯 Padrões acima do threshold: {patterns_above_threshold}/{len(patterns)}"
        )

        logger.info("🎯 Gerando sinais holográficos...")
        holographic_signals = system.holographic_universe.generate_trading_signals(
            patterns
        )
        logger.info(f"🎯 Gerados {len(holographic_signals)} sinais holográficos")

        for i, signal in enumerate(holographic_signals):
            logger.info(
                f"   Sinal {i+1}: {signal.symbol} {signal.action} "
                f"força={signal.strength:.3f} conf={signal.confidence:.3f} "
                f"fonte={signal.source}"
            )

        logger.info(
            f"🔗 Combinando {len(oracle_decisions)} oracle + {len(holographic_signals)} holográficos..."
        )
        combined_decisions = await system._combine_oracle_and_holographic_signals(
            oracle_decisions, holographic_signals
        )
        logger.info(f"🔗 Combinadas em {len(combined_decisions)} decisões")

        for i, decision in enumerate(combined_decisions[:3]):
            metadata = getattr(decision, "metadata", {})
            quantum_enhanced = metadata.get("quantum_enhanced", False)
            logger.info(
                f"   Combinada {i+1}: {decision.symbol} {decision.action} "
                f"conf={decision.confidence:.3f} quantum={'✅' if quantum_enhanced else '❌'}"
            )

        # --- SIM-PIPELINE: detect → tokenize → modulate ---------------------------
        try:
            if getattr(system, "enable_sim", False) and all(
                getattr(system, attr, None) is not None
                for attr in ("hotspot_detector", "quantum_tokenizer", "operator_sim")
            ):
                # Exemplo: série de volatilidade recente (fallback vazia se não disponível)
                vol_series = getattr(system, "vol_series", [])
                hotspots = await system.hotspot_detector.detect(vol_series)
                tokens = system.quantum_tokenizer.tokenize(hotspots)
                for tok in tokens:
                    system.operator_sim.listen(tok)
                system.operator_sim.modulate_field(system.holographic_universe)
        except Exception as exc:  # pragma: no cover - defensivo
            logger.error(f"Erro no pipeline SIM: {exc}", exc_info=True)

        if system.mode == "aggressive_test" and combined_decisions:
            logger.warning(
                "🔥 MODO AGRESSIVO: Analisando outliers para promoção de sinal..."
            )
            scores = [
                d.metadata.get("combined_position_raw", 0) for d in combined_decisions
            ]
            if scores:
                mean_score = np.mean(scores)
                std_score = np.std(scores)
                logger.info(
                    f"   - Distribuição de Convicção: Média={mean_score:.4f}, Desvio Padrão={std_score:.4f}"
                )
                if std_score > 0.01:
                    best_outlier = max(
                        combined_decisions,
                        key=lambda d: abs(
                            d.metadata.get("combined_position_raw", 0) - mean_score
                        ),
                    )
                    outlier_score = best_outlier.metadata.get(
                        "combined_position_raw", 0
                    )
                    if abs(outlier_score - mean_score) > 1.5 * std_score:
                        logger.warning(
                            f"  🔥 PROMOVENDO SINAL OUTLIER para {best_outlier.symbol} (Score: {outlier_score:.4f})"
                        )
                        best_outlier.action = "BUY" if outlier_score > 0 else "SELL"
                        best_outlier.confidence = 0.65
                        best_outlier.source += "+AggressivePromotion"
                        best_outlier.reasoning.append(
                            "!! SINAL PROMOVIDO POR ANÁLISE DE OUTLIER !!"
                        )

        logger.info("🛡️ Aplicando enhanced risk assessment...")
        validated_decisions = []
        rejected_count = 0
        execution_results = []

        for i, decision in enumerate(combined_decisions):
            logger.info(
                f"🔍 Validando decisão {i+1}/{len(combined_decisions)}: {decision.symbol} {decision.action}"
            )
            risk_assessment = system._enhanced_risk_assessment(decision)
            logger.info(f"   Risk Assessment: {risk_assessment}")

            if risk_assessment["approved"]:
                symbol_key = decision.symbol.replace("USDT", "").replace("/", "")
                quantum_data = system.quantum_states.get(symbol_key, {})

                rsi_val = quantum_data.get("rsi")
                rsi_str = f"{rsi_val:.1f}" if rsi_val is not None else "N/A"
                vol_ratio_val = quantum_data.get("volume_ratio")
                vol_ratio_str = (
                    f"{vol_ratio_val:.2f}" if vol_ratio_val is not None else "N/A"
                )
                volatility_val = quantum_data.get("volatility")
                volatility_str = (
                    f"{volatility_val:.2f}%" if volatility_val is not None else "N/A"
                )
                quantum_encoded_str = (
                    "✅" if quantum_data.get("quantum_encoded") else "❌"
                )

                logger.info(
                    f"✅ SINAL APROVADO: {decision.symbol} {decision.action} "
                    f"confidence={decision.confidence:.2f} "
                    f"RSI={rsi_str} Vol_Ratio={vol_ratio_str} Volatility={volatility_str} "
                    f"quantum_encoded={quantum_encoded_str}"
                )

                decision.metadata = decision.metadata or {}
                decision.metadata.update(
                    {
                        "enhanced_risk_assessment": risk_assessment,
                        "quantum_data": quantum_data,
                    }
                )

                validated_decisions.append(decision)
                execution_results.append(True)
                if system.memory_service:
                    try:
                        unified_state = system.oracle_engine.last_unified_state
                        metrics = {"decision_confidence": decision.confidence}
                        if unified_state:
                            metrics.update(
                                {
                                    "overall_risk": unified_state.risk_metrics.get(
                                        "overall_risk", 0.0
                                    ),
                                    "consciousness_level": unified_state.consciousness_level,
                                }
                            )
                        if decision.quantum_signature:
                            metrics.update(decision.quantum_signature.metrics)
                            vector = list(decision.quantum_signature.vector)
                        else:
                            vector = []
                        packet = QuantumSignaturePacket(vector=vector, metrics=metrics)
                        market_snapshot = {}
                        if (
                            unified_state
                            and decision.symbol in unified_state.market_data
                        ):
                            df = unified_state.market_data[decision.symbol]
                            if hasattr(df, "iloc") and not df.empty:
                                market_snapshot = df.iloc[-1].to_dict()
                        success = await system.memory_service.store(
                            packet,
                            market_snapshot=market_snapshot,
                            decision_context=decision.metadata,
                        )
                        if success:
                            logger.debug(
                                f"📝 QuantumSignaturePacket {packet.id} armazenado"
                            )
                        else:
                            logger.warning(
                                f"⚠️ Falha ao armazenar QuantumSignaturePacket {packet.id}"
                            )
                    except Exception as mem_exc:
                        logger.error(
                            f"Erro ao gravar na memória: {mem_exc}", exc_info=True
                        )
            else:
                rejected_count += 1
                execution_results.append(False)
                logger.warning(
                    f"🚫 SINAL REJEITADO {rejected_count}: {decision.symbol} {decision.action} - "
                    f"Razão: {risk_assessment['reason']}"
                )

        if system.enhanced_data_collector and hasattr(
            system.enhanced_data_collector, "record_pattern_feedback"
        ):
            try:
                system.enhanced_data_collector.record_pattern_feedback(
                    patterns_detected=len(patterns),
                    signals_generated=len(holographic_signals),
                    signals_executed=len(validated_decisions),
                    execution_results=execution_results,
                )
                calibration_report = (
                    system.enhanced_data_collector.get_calibration_report()
                )
                if calibration_report.get("last_calibration", 0) > time.time() - 10:
                    logger.info(
                        f"📊 Calibração atualizada: price_amp={calibration_report['price_amplification']:.2f}, "
                        f"news_amp={calibration_report['news_amplification']:.2f}, "
                        f"threshold={calibration_report['pattern_threshold']:.2f}"
                    )
            except Exception as calib_error:
                logger.debug(f"Erro no feedback do calibrador: {calib_error}")

        if validated_decisions:
            logger.info(
                f"📨 Publicadas {len(validated_decisions)} decisões para execução via evento"
            )
            for i, decision in enumerate(validated_decisions):
                logger.info(
                    f"   📨 Publicada {i+1}: {decision.symbol} {decision.action} conf={decision.confidence:.3f}"
                )
        else:
            logger.info("⏸️ Nenhuma decisão para executar neste ciclo")

        rejected_count = len(combined_decisions) - len(validated_decisions)
        rejection_rate = rejected_count / max(len(combined_decisions), 1)

        logger.info("📊 ESTATÍSTICAS DO CICLO:")
        logger.info(f"   Oracle decisions: {len(oracle_decisions)}")
        logger.info(f"   Holographic signals: {len(holographic_signals)}")
        logger.info(f"   Combined decisions: {len(combined_decisions)}")
        logger.info(f"   Validated decisions: {len(validated_decisions)}")
        logger.info(f"   Rejected decisions: {rejected_count}")
        logger.info(f"   Rejection rate: {rejection_rate:.1%}")

        system.decision_metrics.append(
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "oracle_decisions": len(oracle_decisions),
                "holographic_signals": len(holographic_signals),
                "combined_decisions": len(combined_decisions),
                "validated_decisions": len(validated_decisions),
                "rejection_rate": rejection_rate,
            }
        )

        if getattr(system, "event_bus", None):
            await system.event_bus.publish_async(
                "trading.signal.generated",
                TradingSignalGenerated(decisions=validated_decisions),
            )
    except Exception as exc:  # pragma: no cover - unforeseen errors
        logger.error(
            f"❌ ERRO CRÍTICO no ciclo de decisão #{cycle_count}: {exc}",
            exc_info=True,
        )


async def _consciousness_loop(system: "QUALIATradingSystem") -> None:
    """Loop contínuo de consciência do sistema."""
    logger.debug("🌌 DEBUG: ENTRANDO NO _consciousness_loop()")
    logger.debug("🌌 Consciousness Loop iniciado")

    while system.running and not system.shutdown_requested:
        try:
            await system.consciousness._update_consciousness_state()
            performance_score = await system.consciousness._evaluate_performance()
            if await system.consciousness._should_adapt():
                # Passa contexto de metacognição para adaptação do sistema
                await system.consciousness._adapt_system(
                    metacog_ctx=system.metacognition
                )
            system.consciousness._update_system_metrics(performance_score)
            system.consciousness._log_consciousness_status()
            if not await _interruptible_sleep(system, 30.0):
                break  # Shutdown solicitado
        except Exception as exc:  # pragma: no cover
            logger.error(f"Erro na consciência: {exc}", exc_info=True)
            if not await _interruptible_sleep(system, 30.0):
                break  # Shutdown solicitado


async def _execution_cycle_loop(system: "QUALIATradingSystem") -> None:
    """Monitora posições abertas e atualiza métricas de PnL.

    Este loop apenas acompanha as posições em aberto e atualiza o
    resultado financeiro acumulado do sistema (PnL). Nenhuma nova ordem
    é enviada durante essa rotina: ela serve unicamente para monitorar a
    execução existente e registrar métricas de performance.

    Parameters
    ----------
    system : QUALIATradingSystem
        Instância do sistema de trading responsável pelo ciclo de
        execução.

    Returns
    -------
    None
    """
    logger.debug("🎯 DEBUG: ENTRANDO NO _execution_cycle_loop()")
    logger.debug("📈 Execution Cycle Loop (PnL monitor) iniciado")

    interval = system.timing.get("execution_cycle_interval", 5.0)

    while system.running and not system.shutdown_requested:
        try:
            if system.execution_interface:
                await system.execution_interface._monitor_positions()
                system.execution_interface._update_metrics()
            if not await _interruptible_sleep(system, interval):
                break  # Shutdown solicitado
        except Exception as exc:  # pragma: no cover
            logger.error(f"Erro na monitoração de PnL: {exc}", exc_info=True)
            if not await _interruptible_sleep(system, 10.0):
                break  # Shutdown solicitado


async def _monitoring_loop(system: "QUALIATradingSystem") -> None:
    """Loop de monitoramento de performance."""
    logger.debug("📊 DEBUG: ENTRANDO NO _monitoring_loop()")
    logger.debug("📊 Monitoring Loop iniciado")

    while system.running and not system.shutdown_requested:
        try:
            performance_entry = await system._collect_performance_metrics()
            system.performance_data.append(performance_entry)

            if len(system.performance_data) % 30 == 0:
                system._log_comprehensive_status(performance_entry)

            if not await _interruptible_sleep(
                system, system.timing["monitoring_interval"]
            ):
                break  # Shutdown solicitado
        except Exception as exc:  # pragma: no cover
            logger.error(f"Erro no monitoramento: {exc}", exc_info=True)
            if not await _interruptible_sleep(system, 10.0):
                break  # Shutdown solicitado


async def _safety_monitoring_loop(system: "QUALIATradingSystem") -> None:
    """Loop de monitoramento de segurança."""
    logger.debug("🛡️ DEBUG: ENTRANDO NO _safety_monitoring_loop()")
    logger.debug("🛡️ Safety Monitoring Loop iniciado")

    while system.running and not system.shutdown_requested:
        try:
            safety_check = await system._perform_safety_checks()
            if not safety_check["safe"]:
                logger.error(f"🚨 VIOLAÇÃO DE SEGURANÇA: {safety_check['reason']}")
                await system._emergency_stop(safety_check["reason"])
                break
            if not await _interruptible_sleep(
                system, system.timing["safety_check_interval"]
            ):
                break  # Shutdown solicitado
        except Exception as exc:  # pragma: no cover
            logger.error(f"Erro no monitoramento de segurança: {exc}", exc_info=True)
            if not await _interruptible_sleep(system, 5.0):
                break  # Shutdown solicitado


# Mensagem usada quando não há novas entradas para metacognição
NO_NEW_DATA_MSG = "🤔 Sem novos dados/metas; aguardando próximo ciclo."


async def _metacognition_loop(system: "QUALIATradingSystem") -> None:
    """Executa o ciclo de metacognição para autoanálise do sistema.

    O parâmetro ``info_interval`` controla a frequência em que uma mensagem
    informativa é registrada quando nenhum dado novo é detectado. O valor é
    lido de ``system.timing['metacognition_idle_info_interval']`` e o padrão
    é ``10``. Caso esse valor seja ``0`` ou negativo, ``10`` é utilizado e um
    aviso é registrado.
    """
    logger.debug("🤔 Metacognition Loop iniciado")

    last_entry_ts = None
    idle_cycle_count = 0
    info_interval = system.timing.get("metacognition_idle_info_interval", 10)
    if info_interval <= 0:
        logger.warning(
            "metacognition_idle_info_interval deve ser maior que zero; utilizando 10"
        )
        info_interval = 10
    if getattr(system, "metacognition", None):
        try:
            last_entry = system.metacognition.decision_log[-1]
            if isinstance(last_entry, dict):
                last_entry_ts = last_entry.get("timestamp")
        except (IndexError, AttributeError):  # pragma: no cover - defensivo
            last_entry_ts = None

    while system.running and not system.shutdown_requested:
        try:
            # YAA FIX-ASYNC-SHUTDOWN: Verificar shutdown antes de cada operação crítica
            if system.shutdown_requested or not system.running:
                logger.debug("🤔 Shutdown detectado no metacognition loop")
                break

            if system.metacognition:
                latest_ts = None
                try:
                    latest_entry = system.metacognition.decision_log[-1]
                    if isinstance(latest_entry, dict):
                        latest_ts = latest_entry.get("timestamp")
                except (IndexError, AttributeError):  # pragma: no cover - defensivo
                    latest_ts = None

                if latest_ts is None or latest_ts == last_entry_ts:
                    idle_cycle_count += 1
                    if idle_cycle_count % info_interval == 0:
                        logger.info(NO_NEW_DATA_MSG)
                    else:
                        logger.debug(NO_NEW_DATA_MSG)
                    if not await _interruptible_sleep(
                        system,
                        system.timing.get("metacognition_interval", 30.0),
                    ):
                        break
                    continue

                idle_cycle_count = 0

                last_entry_ts = latest_ts
                logger.debug("🤔 Executando ciclo de metacognição...")

                # YAA FIX-ASYNC-SHUTDOWN: Proteger operação de metacognição com try/except específico
                try:
                    await asyncio.to_thread(system.metacognition.run_cycle)
                except asyncio.CancelledError:
                    logger.debug(
                        "🤔 FIX-ASYNC-SHUTDOWN: Metacognition cancelada graciosamente"
                    )
                    break
                except Exception as meta_exc:
                    logger.warning(f"🤔 Erro na metacognição: {meta_exc}")
            else:
                logger.debug(
                    "🤔 Metacognition Loop: componente não disponível, pulando ciclo."
                )
            if not await _interruptible_sleep(
                system, system.timing.get("metacognition_interval", 30.0)
            ):
                break  # Shutdown solicitado
        except asyncio.CancelledError:
            # YAA FIX-ASYNC-SHUTDOWN: Tratamento específico para CancelledError
            logger.debug(
                "🤔 FIX-ASYNC-SHUTDOWN: Metacognition loop cancelado graciosamente"
            )
            break
        except Exception as exc:  # pragma: no cover - unforeseen errors
            logger.error(
                f"❌ ERRO CRÍTICO no ciclo de metacognição: {exc}", exc_info=True
            )
            if not await _interruptible_sleep(system, 60.0):
                break  # Shutdown solicitado
