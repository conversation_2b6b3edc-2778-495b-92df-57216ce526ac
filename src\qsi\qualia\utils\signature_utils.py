"""Utility helpers for metadata-driven signature handling."""

from typing import Any, Dict, Optional
import json
from functools import lru_cache

from ..config import VectorType
from qualia.config.settings import allowed_vector_types

try:
    from datadog import DogStatsd
except ModuleNotFoundError:
    class DogStatsd:
        def increment(self, *args, **kwargs):
            pass

        def timing(self, *args, **kwargs):
            pass
from .logger import get_logger
from .tracing import instrument_logger, is_tracing_enabled
from .metrics_persistence import persist_metric_to_qpm
from .backend import is_gpu_available, safe_get_backend
from .hardware_acceleration import qpu_available
import numpy as np

try:  # pragma: no cover - optional dependency
    import cupy as cp
except Exception:  # pragma: no cover - fallback
    cp = None
from ..config.utils_defaults import load_utils_defaults
from ..config.feature_flags import feature_toggle

logger = get_logger(__name__)
statsd = DogStatsd() if 'DogStatsd' in locals() else None

_DEFAULTS = load_utils_defaults() if feature_toggle("utils_v2") else {}
_TRACE_LOGGING = bool(_DEFAULTS.get("trace_instrumentation_enabled", False))

if is_tracing_enabled():  # pragma: no cover - configuration
    instrument_logger(logger.logger)


def expected_signature_dimension(source_details: Dict[str, Any]) -> Optional[int]:
    """Infer the length of a signature vector from metadata.

    Parameters
    ----------
    source_details : Dict[str, Any]
        Metadata from ``QuantumSignaturePacket`` describing the vector's
        provenance.

    Returns
    -------
    Optional[int]
        The inferred dimension of ``vector``. Returns ``None`` when
        ``source_details`` is missing required keys or contains invalid values.

    Examples
    --------
    >>> metadata = {"vector_type": "probability_distribution", "n_qubits": 2}
    >>> expected_signature_dimension(metadata)
    4
    """

    if not source_details:
        return None

    key = json.dumps(source_details, sort_keys=True)
    return _expected_signature_dimension_cached(key)


@lru_cache(maxsize=128)
def _expected_signature_dimension_cached(details_json: str) -> Optional[int]:
    source_details = json.loads(details_json)

    vector_type = source_details.get("vector_type")
    n_qubits = source_details.get("n_qubits")

    if vector_type is None:
        return None

    vector_name = getattr(vector_type, "value", str(vector_type))
    if vector_name not in allowed_vector_types:
        return None

    try:
        if vector_type == VectorType.SELECTED_METRICS_VECTOR:
            keys = source_details.get("use_metrics_keys")
            return len(keys) if isinstance(keys, list) else None
        if n_qubits is None:
            return None
        if vector_type == VectorType.AMPLITUDE_VECTOR_INTERLEAVED_FLOAT:
            return 2 * (2 ** int(n_qubits))
        if vector_type == VectorType.PROBABILITY_DISTRIBUTION:
            return 2 ** int(n_qubits)
        if vector_type == VectorType.FIRST_N_PROBABILITIES:
            return int(source_details.get("N_first_probs", 2 ** int(n_qubits)))
        if vector_type == VectorType.PHASE_VECTOR:
            return 2 ** int(n_qubits)
        if vector_type == VectorType.CUMULATIVE_PROBABILITIES:
            return 2 ** int(n_qubits)
        if vector_type == VectorType.DENSITY_MATRIX_VECTOR:
            return 2 * (4 ** int(n_qubits))
        if vector_type == VectorType.SECOND_ORDER_MOMENTS_VECTOR:
            return 4 ** int(n_qubits)
    except (ValueError, TypeError) as exc:
        logger.warning(
            "Invalid metadata for signature dimension: %s (%s)",
            source_details,
            exc,
        )
        statsd.increment("signature_utils.invalid_metadata")
        return None

    return None


def validate_signature_vector(
    vector: Any,
    *,
    use_gpu: bool | None = None,
    use_qpu: bool | None = None,
) -> bool:
    """Validate a signature vector using optional hardware acceleration.

    Parameters
    ----------
    vector : array-like
        Numerical signature vector to validate.
    use_gpu : bool, optional
        Force GPU validation when ``True``. When ``None`` the function
        uses :func:`is_gpu_available` to decide automatically.
    use_qpu : bool, optional
        Off-load validation to a QPU when ``True`` and available.

    Returns
    -------
    bool
        ``True`` when the vector is valid (contains only finite numbers).
    """
    if use_qpu:
        valid = _validate_with_qpu(vector)
    else:
        if use_gpu is None:
            use_gpu = is_gpu_available()

        if use_gpu and cp is not None:
            arr = cp.asarray(vector, dtype=float)
            valid = not bool(cp.isnan(arr).any())
        else:
            arr = np.asarray(vector, dtype=float)
            valid = not bool(np.isnan(arr).any())

    persist_metric_to_qpm("signature_validation", valid)
    return valid


def _validate_with_qpu(vector: Any) -> bool:
    """Attempt validation using a QPU backend."""

    if not qpu_available():
        arr = np.asarray(vector, dtype=float)
        return not bool(np.isnan(arr).any())

    try:
        from qiskit import QuantumCircuit, transpile
        from qiskit.quantum_info import Statevector

        arr = np.asarray(vector, dtype=float)
        if np.isnan(arr).any():
            return False

        dim = len(arr)
        num_qubits = int(np.log2(dim)) if dim and dim & (dim - 1) == 0 else 1
        norm = np.linalg.norm(arr)
        if norm == 0:
            return False
        state = Statevector(arr / norm)
        qc = QuantumCircuit(num_qubits)
        qc.initialize(state.data, range(num_qubits))
        backend = safe_get_backend("ibmq_qasm_simulator") or safe_get_backend(
            "statevector_simulator"
        )
        if backend is not None:
            backend.run(transpile(qc, backend))
        return True
    except Exception as exc:  # pragma: no cover - optional path
        logger.warning("QPU validation failed: %s", exc)
        arr = np.asarray(vector, dtype=float)
        return not bool(np.isnan(arr).any())
