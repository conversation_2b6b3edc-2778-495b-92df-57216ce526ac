"""Teste para detectar ciclos de importacao em 'qualia'.

O teste tenta instalar ``pipdeptree`` para auxiliar na analise de dependencias e
utiliza ``importlib``/``ast`` para detectar ciclos basicos entre modulos de
``qualia``. Seu objetivo eh prevenir regressoes de importacao circular.
"""

import ast
import os
import subprocess
import sys
from pathlib import Path


def _install_pipdeptree():
    """Instala ``pipdeptree`` se o ambiente permitir."""
    subprocess.run(
        [sys.executable, "-m", "pip", "install", "pipdeptree"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        check=False,
    )


def _build_dependency_graph(base_dir: Path, pkg_name: str = "qualia"):
    """Constroi grafo de dependencias internas baseado em imports."""
    modules = {}
    for root, _, files in os.walk(base_dir):
        for fname in files:
            if fname.endswith(".py"):
                path = Path(root) / fname
                mod = path.relative_to(base_dir).with_suffix("").as_posix().replace("/", ".")
                modules[mod] = path

    deps = {m: [] for m in modules}

    def resolve_relative(module: str | None, level: int, current: str) -> str | None:
        parts = current.split(".")
        if level > len(parts):
            return None
        prefix = parts[:-level]
        if module:
            prefix += module.split(".")
        return ".".join(prefix)

    for mod, path in modules.items():
        with open(path, "r", encoding="utf-8") as f:
            try:
                tree = ast.parse(f.read(), filename=str(path))
            except SyntaxError:
                continue
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name.startswith(f"{pkg_name}."):
                        target = alias.name[len(pkg_name) + 1 :]
                        if target in deps:
                            deps[mod].append(target)
            elif isinstance(node, ast.ImportFrom):
                if node.level:
                    resolved = resolve_relative(node.module, node.level, mod)
                    if resolved and resolved.startswith(f"{pkg_name}."):
                        target = resolved[len(pkg_name) + 1 :]
                        if target in deps:
                            deps[mod].append(target)
                elif node.module and node.module.startswith(f"{pkg_name}."):
                    target = node.module[len(pkg_name) + 1 :]
                    if target in deps:
                        deps[mod].append(target)
    return deps


def _has_cycle(graph: dict[str, list[str]]):
    visited = set()
    stack = set()

    def visit(node: str) -> bool:
        if node in stack:
            return True
        if node in visited:
            return False
        visited.add(node)
        stack.add(node)
        for neigh in graph.get(node, []):
            if visit(neigh):
                return True
        stack.remove(node)
        return False

    return any(visit(n) for n in graph)


def test_no_circular_imports_in_qualia():
    _install_pipdeptree()
    base_dir = Path(__file__).resolve().parent.parent / "qualia"
    graph = _build_dependency_graph(base_dir)
    assert not _has_cycle(graph), "Encontrado ciclo de importacao em 'qualia'"

