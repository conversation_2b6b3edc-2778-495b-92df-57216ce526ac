from __future__ import annotations

import hashlib
from functools import lru_cache
import numpy as np

from .embedder import Embedder
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class Analyzer:
    """Generate text embeddings with caching.

    Parameters
    ----------
    embedder : Embedder | None, optional
        Instance used to compute embeddings. Defaults to :class:`Embedder`.
    cache_size : int, optional
        Maximum number of cached embeddings. Defaults to ``128``.
    """

    def __init__(self, embedder: Embedder | None = None, cache_size: int = 128) -> None:
        self.embedder = embedder or Embedder()
        self.cache_size = cache_size

        @lru_cache(maxsize=cache_size)
        def _cached(text: str) -> np.ndarray:
            return self.embedder.encode([text])[0]

        self._cached = _cached

    def _hash(self, text: str) -> str:
        """Return SHA1 hash of text."""
        return hashlib.sha1(text.encode("utf-8")).hexdigest()

    def embed_text(self, text: str) -> np.ndarray:
        """Return embedding for a single text using cache.

        Parameters
        ----------
        text : str
            Input text.

        Returns
        -------
        numpy.ndarray
            Vector embedding for ``text``.
        """
        key = self._hash(text)
        info_before = self._cached.cache_info()
        vector = self._cached(text)
        info_after = self._cached.cache_info()
        if info_after.hits > info_before.hits:
            logger.debug("Embedding recuperado do cache para chave %s", key)
        else:
            logger.debug("Embedding calculado e armazenado para chave %s", key)
        return vector
