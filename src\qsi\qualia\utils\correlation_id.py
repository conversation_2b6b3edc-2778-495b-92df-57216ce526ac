"""Utilities for correlation IDs in logging."""

from __future__ import annotations

import uuid
from contextvars import Context<PERSON><PERSON>
from contextlib import contextmanager
from typing import Iterator, Optional


_CORRELATION_ID: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)


def get_correlation_id() -> Optional[str]:
    """Return the current correlation ID.

    Returns
    -------
    Optional[str]
        The current correlation ID if set, otherwise ``None``.
    """
    return _CORRELATION_ID.get()


def set_correlation_id(correlation_id: str) -> None:
    """Set the correlation ID for the current context.

    Parameters
    ----------
    correlation_id : str
        Correlation ID to be assigned.
    """
    _CORRELATION_ID.set(correlation_id)


@contextmanager
def correlation_context(correlation_id: Optional[str] = None) -> Iterator[str]:
    """Context manager that assigns a correlation ID.

    Parameters
    ----------
    correlation_id : Optional[str], optional
        ID to use for the context. If ``None``, a new ``uuid4`` hex is generated.

    Yields
    ------
    str
        The correlation ID active within the context.
    """
    cid = correlation_id or uuid.uuid4().hex
    token = _CORRELATION_ID.set(cid)
    try:
        yield cid
    finally:
        _CORRELATION_ID.reset(token)
