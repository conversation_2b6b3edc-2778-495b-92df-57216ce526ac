"""Application initialization helpers for QUALIA."""

from __future__ import annotations

from datetime import datetime
import numpy as np

from ..core.universe import QUALIAQuantumUniverse
from ..core.consciousness import QUALIAConsciousness
from ..core.symbolic_processor import QualiaSymbolicProcessor
from ..utils.logger import get_logger
from ..utils.state import QualiaState

logger = get_logger(__name__)


def get_qualia_state() -> QualiaState:
    """Retrieve the current :class:`QualiaState` instance."""
    from flask import current_app, g, has_request_context

    if has_request_context() and hasattr(g, "qualia_state"):
        return g.qualia_state
    return current_app.extensions["qualia_state"]


def initialize_qualia_system(state: QualiaState) -> QualiaState:
    """Initialize QUALIA core components.

    Parameters
    ----------
    state:
        Qualia state container where components will be stored.

    Returns
    -------
    QualiaState
        The updated state instance with initialized components.
    """

    with state.locked():
        state.consciousness = QUALIAConsciousness(
            n_qubits=8,
            n_steps=10,
            thermal_coefficient=0.1,
            retro_mode="none",
            perception_depth=3,
            self_reflection_enabled=True,
            entropy_sensitivity=0.02,
        )

        state.symbolic_processor = QualiaSymbolicProcessor(
            dimension=8, semantic_depth=3, associative_threshold=0.7
        )

        try:
            state.universe = QUALIAQuantumUniverse(
                n_qubits=8,
                scr_depth=10,
                base_lambda=np.pi / 6,
                alpha=0.2,
                retro_strength=0.0,
                num_ctc_qubits=1,
            )
        except Exception as exc:  # pragma: no cover - universe optional
            logger.warning(
                "Aviso: Não foi possível inicializar o universo quântico: %s", exc
            )

        state.update_timestamp()

    return state
