"""Disk persistence utilities for :class:`QuantumPatternMemory`."""

from __future__ import annotations

import json
import os
import tempfile
from pathlib import Path
from typing import Any, Deque, Dict, List, Optional, TYPE_CHECKING, Set
from collections import deque

import numpy as np

from ..utils.file_lock import lock_file, unlock_file

from pydantic import ValidationError
from sklearn.decomposition import PCA

from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger
from ..utils.persistence import convert_to_serializable
from .event_bus import SimpleEventBus, MemoryPersisted, MemoryLoaded

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from .quantum_pattern_memory import QuantumPatternMemory

logger = get_logger(__name__)


class PatternPersistence:
    """Handle disk persistence for :class:`QuantumPatternMemory`.

    This helper writes and loads the in-memory structure using an atomic
    strategy (temporary file + :func:`os.replace`) combined with a file lock via
    ``fcntl.flock``. It relies on a ``QuantumPatternMemory`` instance passed at
    initialization time.
    """

    def __init__(
        self, qpm: "QuantumPatternMemory", event_bus: SimpleEventBus | None = None
    ) -> None:
        self.qpm = qpm
        self.event_bus = event_bus
        self._warned: Set[str] = set()

    def _warn_once(self, key: str, message: str) -> None:
        if key not in self._warned:
            logger.warning(message)
            self._warned.add(key)

    def save_to_file(self, file_path: Optional[str] = None) -> bool:
        """Persist the memory to disk using an atomic replace strategy.

        Parameters
        ----------
        file_path : str, optional
            Custom path to persist the memory. When ``None`` the configured
            ``persistence_path`` is used.

        Returns
        -------
        bool
            ``True`` when the memory was successfully saved.
        """
        path_str = file_path or self.qpm.persistence_path
        if not path_str:
            self._warn_once(
                "missing_path_save",
                "QPM.save_to_file: Nenhum caminho de arquivo fornecido ou configurado.",
            )
            return False
        path_to_save = Path(path_str)
        try:
            os.makedirs(path_to_save.parent, exist_ok=True)
            memory_to_save = {}
            for dim, patterns in self.qpm.memory.items():
                cleaned = []
                for p in patterns:
                    p_copy = {k: v for k, v in p.items() if k != "qsp_obj"}
                    cleaned.append(p_copy)
                memory_to_save[str(dim)] = cleaned
            memory_to_save = convert_to_serializable(memory_to_save)
            lock_fd = open(path_to_save, "a+")
            try:
                if not lock_file(lock_fd):
                    self._warn_once(
                        "lock_fail_save",
                        "Não foi possível obter bloqueio exclusivo no arquivo",
                    )
                fd, tmp_path = tempfile.mkstemp(
                    dir=path_to_save.parent, prefix=".qpm_tmp"
                )
                with os.fdopen(fd, "w", encoding="utf-8") as f:
                    json.dump(
                        {
                            "memory": memory_to_save,
                            "max_memory_size_per_dimension": self.qpm.max_memory_size_per_dimension,
                            "similarity_threshold": self.qpm.similarity_threshold,
                            "low_similarity_threshold": self.qpm.low_similarity_threshold,
                            "low_threshold_max_patterns": self.qpm.low_threshold_max_patterns,
                            "pca_target_dim": getattr(self.qpm, "pca_target_dim", 128),
                        },
                        f,
                        indent=4,
                    )
                    f.flush()
                    os.fsync(f.fileno())
                unlock_file(lock_fd)
                lock_fd.close()
                os.replace(tmp_path, path_to_save)
                logger.debug(
                    "QuantumPatternMemory salva em %s",
                    path_to_save.as_posix(),
                )
                if self.event_bus:
                    num_patterns = sum(len(p) for p in self.qpm.memory.values())
                    self.event_bus.publish(
                        "memory.persisted",
                        MemoryPersisted(
                            path=path_to_save.as_posix(), patterns=num_patterns
                        ),
                    )
                return True
            finally:
                if not lock_fd.closed:
                    unlock_file(lock_fd)
                    lock_fd.close()
        except (OSError, OverflowError, ValidationError, TypeError, ValueError) as e:
            logger.error(
                "QPM: Erro ao salvar QuantumPatternMemory: %s",
                e,
                exc_info=True,
            )
            return False
        except RuntimeError:  # pragma: no cover
            logger.exception(
                "QPM: Erro inesperado ao salvar QuantumPatternMemory",
                exc_info=True,
            )
            raise

    def load_from_file(self, file_path: Optional[str] = None) -> bool:
        """Load memory contents from disk rebuilding internal state.

        Parameters
        ----------
        file_path : str, optional
            Path to the JSON file to load. Defaults to ``persistence_path``.

        Returns
        -------
        bool
            ``True`` when the file was read successfully and the memory was
            populated.
        """
        path_str = file_path or self.qpm.persistence_path
        if not path_str:
            self._warn_once(
                "missing_path_load",
                "QPM.load_from_file: Nenhum caminho de arquivo fornecido.",
            )
            return False
        path_to_load = Path(path_str)
        if not path_to_load.exists():
            self._warn_once(
                f"missing_file_load:{path_to_load.as_posix()}",
                f"QPM.load_from_file: arquivo {path_to_load.as_posix()} não encontrado.",
            )
            return False
        try:
            lock_fd = open(path_to_load, "r", encoding="utf-8")
            try:
                if not lock_file(lock_fd):
                    self._warn_once(
                        "lock_fail_load",
                        "Não foi possível obter bloqueio compartilhado no arquivo",
                    )
                data = json.load(lock_fd)
            finally:
                unlock_file(lock_fd)
                lock_fd.close()
            memory_data = data.get("memory", {})
            self.qpm.memory = {
                int(dim): deque(patterns, maxlen=self.qpm.max_memory_size_per_dimension)
                for dim, patterns in memory_data.items()
            }
            self.qpm.max_memory_size_per_dimension = int(
                data.get("max_memory_size_per_dimension", 1000)
            )
            self.qpm.similarity_threshold = data.get("similarity_threshold")
            self.qpm.low_similarity_threshold = float(
                data.get("low_similarity_threshold", 0.6)
            )
            self.qpm.low_threshold_max_patterns = int(
                data.get("low_threshold_max_patterns", 1000)
            )
            self.qpm.pca_target_dim = int(
                data.get("pca_target_dim", getattr(self.qpm, "pca_target_dim", 128))
            )

            pca_vectors: List[np.ndarray] = []
            pca_refs: List[Any] = []
            new_memory: Dict[int, Deque[Dict[str, Any]]] = {}
            for dim_str, patterns in self.qpm.memory.items():
                for pattern in patterns:
                    qsp_dict = pattern.get("quantum_signature_packet")
                    if not isinstance(qsp_dict, dict):
                        continue
                    vector = qsp_dict.get("vector", [])
                    source_details = qsp_dict.get("source_details", {})
                    if isinstance(vector, list) and len(vector) == 4096:
                        pca_vectors.append(np.array(vector, dtype=float))
                        pca_refs.append((pattern, qsp_dict))
                        continue

                    norm_vec, norm_dim = self.qpm._normalize_vector(
                        vector, source_details
                    )
                    qsp_dict["vector"] = norm_vec
                    pattern["quantum_signature_packet"] = qsp_dict
                    pattern["vector_dim"] = norm_dim
                    new_memory.setdefault(
                        norm_dim,
                        deque(maxlen=self.qpm.max_memory_size_per_dimension),
                    ).append(pattern)

            if pca_vectors:
                vectors_np = np.vstack(pca_vectors)
                invalid_mask = np.isnan(vectors_np) | ~np.isfinite(vectors_np)
                if invalid_mask.any():
                    logger.warning(
                        "QPM.load_from_file: Vetores contendo NaN ou infinito "
                        "detectados. Substituindo valores inválidos por 0."
                    )
                    vectors_np[invalid_mask] = 0.0

                if vectors_np.shape[0] < 2:
                    logger.debug(
                        "QPM.load_from_file: Redução PCA ignorada por falta de amostras."
                    )
                    reduced = vectors_np
                    if reduced.shape[1] < self.qpm.pca_target_dim:
                        pad_width = self.qpm.pca_target_dim - reduced.shape[1]
                        reduced = np.hstack(
                            [reduced, np.zeros((reduced.shape[0], pad_width))]
                        )
                    elif reduced.shape[1] > self.qpm.pca_target_dim:
                        reduced = reduced[:, : self.qpm.pca_target_dim]
                else:
                    n_components = min(
                        self.qpm.pca_target_dim,
                        vectors_np.shape[0],
                        vectors_np.shape[1],
                    )
                    invalid_mask = np.isnan(vectors_np) | ~np.isfinite(vectors_np)
                    if invalid_mask.any():
                        logger.warning(
                            "QPM.load_from_file: Vetores contendo NaN ou infinito "
                            "detectados antes da redução PCA. Substituindo valores "
                            "inválidos por 0."
                        )
                        vectors_np[invalid_mask] = 0.0
                    pca = PCA(n_components=n_components)
                    reduced = pca.fit_transform(vectors_np)
                    if reduced.shape[1] < self.qpm.pca_target_dim:
                        pad_width = self.qpm.pca_target_dim - reduced.shape[1]
                        reduced = np.hstack(
                            [reduced, np.zeros((reduced.shape[0], pad_width))]
                        )

                for i, (pattern, qsp_dict) in enumerate(pca_refs):
                    qsp_dict["vector"] = reduced[i].tolist()
                    pattern["quantum_signature_packet"] = qsp_dict
                    pattern["vector_dim"] = self.qpm.pca_target_dim
                    new_memory.setdefault(
                        self.qpm.pca_target_dim,
                        deque(maxlen=self.qpm.max_memory_size_per_dimension),
                    ).append(pattern)

            self.qpm.memory = new_memory

            num_loaded_patterns = sum(
                len(p_list) for p_list in self.qpm.memory.values()
            )
            logger.info(
                "QuantumPatternMemory carregada de %s. %s padrões em %s dimensões carregados.",
                path_to_load.as_posix(),
                num_loaded_patterns,
                len(self.qpm.memory),
            )
            if self.event_bus:
                self.event_bus.publish(
                    "memory.loaded",
                    MemoryLoaded(
                        path=path_to_load.as_posix(), patterns=num_loaded_patterns
                    ),
                )
            self.reindex_memory()
            return True
        except (
            OSError,
            json.JSONDecodeError,
            ValidationError,
            TypeError,
            ValueError,
        ) as e:
            logger.error(
                "QPM: Erro ao carregar QuantumPatternMemory: %s", e, exc_info=True
            )
            self.qpm.memory = {}
            return False
        except RuntimeError:  # pragma: no cover
            logger.exception(
                "QPM: Erro inesperado ao carregar QuantumPatternMemory",
                exc_info=True,
            )
            raise

    def reindex_memory(self) -> None:
        """Rebuild internal indexes after loading or manual modifications.

        Patterns lacking an explicit ``vector_dim`` are inspected and the value
        is determined from their stored :class:`QuantumSignaturePacket`. The
        memory is partitioned by this dimension so that each deque holds patterns
        with vectors of the same length. The number of patterns per dimension
        influencia o ajuste dinâmico do ``similarity_threshold``.
        """
        new_memory: Dict[int, Deque[Dict[str, Any]]] = {}
        self.qpm._id_index.clear()
        for patterns in self.qpm.memory.values():
            for pattern in patterns:
                qsp_dict = pattern.get("quantum_signature_packet")
                if not qsp_dict:
                    continue
                try:
                    qsp = QuantumSignaturePacket(**qsp_dict)
                except (ValidationError, TypeError, ValueError) as e:
                    logger.warning(
                        "QPM.reindex_memory: Falha ao reconstruir QSP a partir do dict: %s",
                        e,
                        exc_info=True,
                    )
                    continue
                except RuntimeError:  # pragma: no cover
                    logger.exception(
                        "QPM.reindex_memory: Erro inesperado ao reconstruir QSP",
                        exc_info=True,
                    )
                    raise
                pattern["qsp_obj"] = qsp
                dim = pattern.get("vector_dim")
                if dim is None:
                    dim = self.qpm._get_vector_dimension(qsp)
                    if dim is not None:
                        logger.debug(
                            "QPM.reindex_memory: dimensão padrão %s definida com base no vetor de assinatura",
                            dim,
                        )
                if dim is None:
                    continue
                pattern["vector_dim"] = dim
                pid = pattern.get("id")
                if pid:
                    self.qpm._id_index[pid] = {
                        "dimension": dim,
                        "timestamp": pattern.get("timestamp"),
                        "market_scenario": pattern.get("market_snapshot", {}).get(
                            "scenario_type"
                        ),
                        "pattern": pattern,
                    }
                new_memory.setdefault(
                    dim, deque(maxlen=self.qpm.max_memory_size_per_dimension)
                ).append(pattern)
        self.qpm.memory = new_memory
        logger.info(
            "QPM: Memória reindexada. Dimensões armazenadas: %s",
            list(self.qpm.memory.keys()),
        )

    def import_patterns_from_backtest(self, file_path: str) -> int:
        """Import patterns saved from previous backtests.

        Parameters
        ----------
        file_path : str
            Path to a JSON file produced by a backtesting run.

        Returns
        -------
        int
            Number of patterns successfully imported.
        """
        path = Path(file_path)
        if not path.exists():
            logger.warning(
                "QPM.import_patterns_from_backtest: arquivo %s não encontrado.",
                path.as_posix(),
            )
            return 0
        try:
            with open(path, "r", encoding="utf-8") as f:
                data = json.load(f)
            memory_data = data.get("memory", {})
            imported = 0
            for dim_str, patterns in memory_data.items():
                dim = int(dim_str)
                self.qpm.memory.setdefault(
                    dim, deque(maxlen=self.qpm.max_memory_size_per_dimension)
                )
                for p in patterns:
                    qsp_dict = p.get("quantum_signature_packet")
                    if isinstance(qsp_dict, dict):
                        try:
                            p["qsp_obj"] = QuantumSignaturePacket(**qsp_dict)
                        except (ValidationError, TypeError, ValueError) as exc:
                            logger.debug(
                                "QPM.import_patterns_from_backtest: falha ao reconstruir QSP: %s",
                                exc,
                                exc_info=True,
                            )
                            p["qsp_obj"] = None
                        except RuntimeError:
                            logger.exception(
                                "QPM.import_patterns_from_backtest: erro inesperado ao reconstruir QSP",
                                exc_info=True,
                            )
                            raise
                    self.qpm.memory[dim].append(p)
                imported += len(patterns)

            logger.info("QPM: %s padrões importados de %s", imported, path.as_posix())
            if imported > 0 and self.qpm.auto_persist and self.qpm.persistence_path:
                self.save_to_file()
            return imported
        except (
            OSError,
            json.JSONDecodeError,
            ValidationError,
            TypeError,
            ValueError,
        ) as e:
            logger.error(
                "QPM: Falha ao importar padrões de %s: %s",
                path.as_posix(),
                e,
                exc_info=True,
            )
            return 0
        except RuntimeError:  # pragma: no cover
            logger.exception(
                "QPM: Erro inesperado ao importar padrões de backtest",
                exc_info=True,
            )
            raise


__all__ = ["PatternPersistence"]
