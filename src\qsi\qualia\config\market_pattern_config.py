from __future__ import annotations

from dataclasses import dataclass, field
import json
from typing import List

from ..config.settings import get_env
from ..utils.logger import get_logger

logger = get_logger(__name__)


def _load_timeframes() -> List[str]:
    """Return default timeframes list from env or fallback."""
    raw = get_env("QUALIA_MARKET_TIMEFRAMES", "", warn=False)
    if raw:
        raw = raw.strip()
        try:
            if raw.startswith("["):
                data = json.loads(raw)
                if isinstance(data, list):
                    return [str(v) for v in data if str(v).strip()]
            else:
                return [v.strip() for v in raw.split(",") if v.strip()]
        except Exception as exc:  # pragma: no cover - unexpected
            logger.warning("Invalid QUALIA_MARKET_TIMEFRAMES value: %s", exc)
    return ["1m", "5m", "15m", "1h", "4h", "1d"]


@dataclass
class MarketPatternConfig:
    """Configuration options for :class:`QUALIAMarketPatternDetector`."""

    timeframes: List[str] = field(default_factory=_load_timeframes)


__all__ = ["MarketPatternConfig"]
