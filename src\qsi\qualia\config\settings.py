"""Load global settings from environment variables for the QUALIA framework.

The :func:`load_settings` helper collects configuration using multiple
``QUALIA_*`` environment variables such as ``QUALIA_CACHE_DIR`` or
``QUALIA_LOGS_DIR`` and returns a :class:`Settings` instance.
"""

import os
from pathlib import Path, PureWindowsPath
from ..utils.logger import get_logger
from dataclasses import dataclass
from typing import Optional, Set
import json
try:
    import yaml
except ModuleNotFoundError:
    # Create a dummy yaml module if PyYAML is not installed.
    # This allows the system to run with default settings even if
    # the config dependency is missing, increasing resilience for testing.
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
from .vector_types import VectorType


DEFAULT_CACHE_DIR = "data/cache"
DEFAULT_LOGS_DIR = "logs"
DEFAULT_RESULTS_DIR = "results"
DEFAULT_STRATEGY_CONFIG = os.path.join("config", "strategy_parameters.yaml")
DEFAULT_FOLDING_ALPHA = 0.8
DEFAULT_EMERGENCE_BETA = 0.2
DEFAULT_RESONANCE_STRENGTH = 0.12
DEFAULT_OBSERVER_ATOL = 1e-8
DEFAULT_RETROCAUSALITY_GAMMA = 0.3
DEFAULT_RETRO_OPERATOR_ENABLED = True
DEFAULT_RETRO_OPERATOR_GAMMA = 0.3
DEFAULT_PID_PERFORMANCE_FILE = "pid_performance.json"
DEFAULT_PID_COEFFS_FILE = "pid_coeffs.json"
DEFAULT_RATE_LIMIT = 3.5
DEFAULT_BACKEND_DEVICE = "CPU"
DEFAULT_QUALIA_EXPERIMENTAL = False
BACKTEST_SUBDIR = "qast_backtests"

# Fallback defaults for QUALIAConsciousness parameters
DEFAULT_CONSCIOUSNESS_N_QUBITS = 8
DEFAULT_CONSCIOUSNESS_N_STEPS = 10
DEFAULT_CONSCIOUSNESS_THERMAL_COEFFICIENT = 0.1
DEFAULT_CONSCIOUSNESS_RETRO_MODE = "none"
DEFAULT_CONSCIOUSNESS_PERCEPTION_DEPTH = 3
DEFAULT_CONSCIOUSNESS_SELF_REFLECTION_ENABLED = True
DEFAULT_CONSCIOUSNESS_ENTROPY_SENSITIVITY = 0.02
DEFAULT_CONSCIOUSNESS_HISTORY_MAXLEN = 256
DEFAULT_CONSCIOUSNESS_PCA_N_COMPONENTS_VARIANCE = 0.95
DEFAULT_CONSCIOUSNESS_PCA_MAX_COMPONENTS = 16
DEFAULT_CONSCIOUSNESS_KMEANS_K_RANGE = (2, 10)
DEFAULT_CONSCIOUSNESS_INIT_SYMBOLIC_PROCESSOR = True
DEFAULT_HIGH_ENTROPY_THRESHOLD = 0.7
DEFAULT_LOW_COHERENCE_THRESHOLD = 0.4

# Additional paths used by QUALIA
DEFAULT_OPEN_POSITIONS_FILE = "open_positions.json"
DEFAULT_QPM_MEMORY_FILE = "qpm_memory.json"
DEFAULT_MEMORY_FILE = "memories.json"
DEFAULT_RUNS_JSON_SUBDIR = "qualia_runs_json_config"
DEFAULT_TRADING_LOG_FILE = "qualia_trading.log"
DEFAULT_ORDER_JOURNAL_FILE = "order_journal.jsonl"
DEFAULT_OBSERVER_PERSISTENCE_DIR = DEFAULT_CACHE_DIR
DEFAULT_OBSERVER_PERSISTENCE_FILENAME = "qualia_self_observer_memory.json"
DEFAULT_COLLECTOR_STATE_FILE = "collector_state.json"

# Defaults for runtime parameters
DEFAULT_METACOG_DIRECTIVE_TTL_SECONDS = 900
DEFAULT_REDUCE_EXPOSURE_MIN_CONFIDENCE = 0.6
DEFAULT_REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES = 5.0
DEFAULT_REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION = False
DEFAULT_REDUCE_EXPOSURE_DEFAULT_PCT = 50.0
DEFAULT_ORDER_EXEC_TIMEOUT = 30.0
DEFAULT_SLEEP_TIME_WARNING_THRESHOLD = 3600
DEFAULT_TICKER_RETRY_ATTEMPTS = 2
DEFAULT_TICKER_RECONNECT_ATTEMPTS = 3
DEFAULT_CONNECTION_RETRY_WAIT = 0.5
DEFAULT_CONNECTION_FAILURE_LIMIT = 3
DEFAULT_CONNECTION_FAILURE_COOLDOWN = 30.0
DEFAULT_MAX_ERROR_HISTORY = 1000
DEFAULT_ENABLE_BAYES_OPT = False

# Root directory of the QUALIA repository
# ``parents[3]`` ensures the returned path points to the project root
# rather than the ``src`` directory.
REPO_ROOT = Path(__file__).resolve().parents[3]


def get_config_file_path(name: str) -> Path:
    """Return absolute path to a file inside the ``config`` directory."""

    return REPO_ROOT / "config" / name


@dataclass
class Settings:
    """Configuration values for QUALIA paths."""

    cache_dir: str
    logs_dir: str
    log_dir: str
    results_dir: str
    qast_backtest_results_dir: str
    open_positions_file: str
    qpm_memory_file: str
    memory_file: str
    runs_json_dir: str
    trading_log_file: str
    order_journal_file: str
    collector_state_file: str
    observer_persistence_dir: str
    observer_persistence_filename: str
    strategy_parameters_path: str
    qpm_metrics_enabled: bool
    market_metrics_enabled: bool
    stm_metrics_enabled: bool
    pid_metrics_enabled: bool
    metrics_enabled: bool
    emergence_beta: float
    folding_alpha: float
    resonance_strength: float
    observer_unitary_atol: float
    retrocausality_gamma: float
    retro_operator_enabled: bool
    retro_operator_gamma: float
    experimental_enabled: bool
    allowed_vector_types: Set[str]
    pid_performance_file: str
    pid_coeffs_file: str
    rate_limit: float
    consciousness_n_qubits: int
    consciousness_n_steps: int
    consciousness_thermal_coefficient: float
    consciousness_retro_mode: str
    consciousness_perception_depth: int
    consciousness_self_reflection_enabled: bool
    consciousness_entropy_sensitivity: float
    consciousness_history_maxlen: int
    consciousness_pca_n_components_variance: float
    consciousness_pca_max_components: int
    consciousness_kmeans_k_min: int
    consciousness_kmeans_k_max: int
    consciousness_init_symbolic_processor: bool
    high_entropy_threshold: float
    low_coherence_threshold: float
    backend_device: str
    metacog_directive_ttl_seconds: int
    reduce_exposure_min_confidence: float
    reduce_exposure_min_position_age_minutes: float
    reduce_exposure_close_single_position: bool
    reduce_exposure_default_pct: float
    order_exec_timeout: float
    sleep_time_warning_threshold: int
    ticker_retry_attempts: int
    ticker_reconnect_attempts: int
    connection_retry_wait: float
    connection_failure_limit: int
    connection_failure_cooldown: float
    max_error_history: int
    enable_bayes_opt: bool


logger = get_logger(__name__)


_UNSET = object()


def get_env(
    name: str,
    default: Optional[str] = _UNSET,
    *,
    warn: bool = True,
) -> Optional[str]:
    """Return environment variable or default value.

    Parameters
    ----------
    name
        Environment variable to retrieve.
    default
        Fallback value if the variable is not set. If omitted, the variable is
        considered required and an ``EnvironmentError`` will be raised when it
        is missing.
    warn
        Emit a warning when ``default`` is returned. Ignored when ``default`` is
        ``None`` or the variable is required.

    Returns
    -------
    Optional[str]
        The environment value or ``default`` when provided.
    """
    value = os.getenv(name)
    if value is not None:
        value = value.strip()

    if not value:
        if default is _UNSET:
            raise EnvironmentError(f"Required environment variable '{name}' is not set")
        if default is not None and warn:
            logger.warning(
                "Environment variable %s is missing, using default '%s'",
                name,
                default,
            )
        return default
    return value


def require_env(name: str) -> str:
    """Return the value of a critical environment variable.

    Parameters
    ----------
    name:
        Name of the variable to retrieve.

    Returns
    -------
    str
        The environment value.

    Raises
    ------
    EnvironmentError
        If the variable is not set or contains the insecure placeholder
        ``dev_secret_key``.
    """

    value = get_env(name)
    if value == "dev_secret_key":
        raise EnvironmentError(
            f"Environment variable '{name}' must not be 'dev_secret_key'"
        )
    return value


def _env_bool(name: str, default: bool, *, warn: bool = False) -> bool:
    """Return boolean value from an environment variable.

    Parameters
    ----------
    name
        Variable to read.
    default
        Value to use when the variable is absent.
    warn
        Whether to emit a warning when ``default`` is returned.

    Returns
    -------
    bool
        ``True`` when the variable evaluates to a truthy value.
    """

    value = get_env(name, str(default), warn=warn)
    return str(value).strip().lower() in {"1", "true", "yes", "y", "t"}


def _resolve_path(path: str) -> str:
    """Return absolute path resolved against the repository root.

    The helper considers Windows-style absolute paths even when running on
    Unix-like systems. This ensures that configuration files referencing
    ``C:\\`` drive locations remain valid in heterogeneous environments.
    """

    if _is_absolute(path):
        return str(Path(path))

    return str(REPO_ROOT / Path(path))


def _is_absolute(path: str) -> bool:
    """Return ``True`` when ``path`` is absolute on any platform."""

    p = Path(path)
    if p.is_absolute():
        return True

    return PureWindowsPath(path).is_absolute()


def _resolve_cache_file(cache_dir: str, path: str) -> str:
    """Return absolute path resolved against ``cache_dir`` when relative."""

    if _is_absolute(path):
        return str(Path(path))

    return str(Path(cache_dir) / path)


def _load_allowed_vector_types() -> Set[str]:
    """Return set of allowed ``VectorType`` names."""

    config_file = os.getenv("QUALIA_VECTOR_TYPES_FILE")
    if config_file:
        try:
            with open(config_file, "r", encoding="utf-8") as fh:
                if config_file.endswith((".yaml", ".yml")):
                    data = yaml.safe_load(fh)
                else:
                    data = json.load(fh)
            if isinstance(data, list):
                return {str(item) for item in data}
            logger.error("Vector types config must be a list: %s", config_file)
        except FileNotFoundError:
            logger.warning("Vector types file not found at %s", config_file)
        except Exception as exc:  # pragma: no cover - unexpected errors
            logger.error("Failed to load vector types %s: %s", config_file, exc)

    env_value = os.getenv("QUALIA_ALLOWED_VECTOR_TYPES")
    if env_value:
        return {v.strip() for v in env_value.split(",") if v.strip()}

    return {vt.value for vt in VectorType}


def load_settings() -> Settings:
    """Load settings from environment variables with defaults."""
    cache_dir = _resolve_path(
        get_env("QUALIA_CACHE_DIR", DEFAULT_CACHE_DIR, warn=False)
    )
    logs_dir = _resolve_path(get_env("QUALIA_LOGS_DIR", DEFAULT_LOGS_DIR, warn=False))
    log_dir = _resolve_path(get_env("QUALIA_LOG_DIR", logs_dir, warn=False))
    results_dir = _resolve_path(
        get_env("QUALIA_RESULTS_DIR", DEFAULT_RESULTS_DIR, warn=False)
    )
    qast_backtest_results_dir = _resolve_path(
        get_env(
            "QUALIA_QAST_BACKTEST_RESULTS_DIR",
            os.path.join(results_dir, BACKTEST_SUBDIR),
            warn=False,
        )
    )

    open_positions_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_OPEN_POSITIONS_FILE",
            DEFAULT_OPEN_POSITIONS_FILE,
            warn=False,
        ),
    )
    qpm_memory_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_QPM_MEMORY_FILE",
            DEFAULT_QPM_MEMORY_FILE,
            warn=False,
        ),
    )
    memory_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_MEMORY_FILE",
            DEFAULT_MEMORY_FILE,
            warn=False,
        ),
    )
    runs_json_dir = _resolve_path(
        get_env(
            "QUALIA_RUNS_JSON_DIR",
            os.path.join(results_dir, DEFAULT_RUNS_JSON_SUBDIR),
            warn=False,
        )
    )
    trading_log_file = _resolve_path(
        get_env(
            "QUALIA_TRADING_LOG_FILE",
            os.path.join(logs_dir, DEFAULT_TRADING_LOG_FILE),
            warn=False,
        )
    )
    order_journal_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_ORDER_JOURNAL_FILE",
            DEFAULT_ORDER_JOURNAL_FILE,
            warn=False,
        ),
    )
    collector_state_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_COLLECTOR_STATE_FILE",
            DEFAULT_COLLECTOR_STATE_FILE,
            warn=False,
        ),
    )
    observer_persistence_dir = _resolve_path(
        get_env(
            "QUALIA_OBSERVER_PERSISTENCE_DIR",
            DEFAULT_OBSERVER_PERSISTENCE_DIR,
            warn=False,
        )
    )
    observer_persistence_filename = get_env(
        "QUALIA_OBSERVER_PERSISTENCE_FILENAME",
        DEFAULT_OBSERVER_PERSISTENCE_FILENAME,
        warn=False,
    )
    strategy_parameters_path = _resolve_path(
        get_env(
            "QUALIA_STRATEGY_CONFIG",
            DEFAULT_STRATEGY_CONFIG,
            warn=False,
        )
    )
    qpm_metrics_enabled = _env_bool("QUALIA_QPM_METRICS_ENABLED", False)
    market_metrics_enabled = _env_bool("QUALIA_MARKET_METRICS_ENABLED", False)
    stm_metrics_enabled = _env_bool("QUALIA_STM_METRICS_ENABLED", False)
    pid_metrics_enabled = _env_bool("QUALIA_PID_METRICS_ENABLED", False)
    metrics_enabled = _env_bool("QUALIA_METRICS_ENABLED", False)
    emergence_beta = float(
        get_env("QUALIA_EMERGENCE_BETA", str(DEFAULT_EMERGENCE_BETA), warn=False)
    )
    folding_alpha = float(
        get_env("QUALIA_FOLDING_ALPHA", str(DEFAULT_FOLDING_ALPHA), warn=False)
    )
    resonance_strength = float(
        get_env(
            "QUALIA_RESONANCE_STRENGTH", str(DEFAULT_RESONANCE_STRENGTH), warn=False
        )
    )
    observer_unitary_atol = float(
        get_env("QUALIA_OBSERVER_ATOL", str(DEFAULT_OBSERVER_ATOL), warn=False)
    )
    retrocausality_gamma = float(
        get_env(
            "QUALIA_RETROCAUSALITY_GAMMA",
            str(DEFAULT_RETROCAUSALITY_GAMMA),
            warn=False,
        )
    )
    retro_operator_enabled = _env_bool(
        "QUALIA_RETRO_OPERATOR_ENABLED",
        DEFAULT_RETRO_OPERATOR_ENABLED,
    )
    retro_operator_gamma = float(
        get_env(
            "QUALIA_RETRO_OPERATOR_GAMMA",
            str(DEFAULT_RETRO_OPERATOR_GAMMA),
            warn=False,
        )
    )
    experimental_enabled = _env_bool(
        "QUALIA_EXPERIMENTAL",
        DEFAULT_QUALIA_EXPERIMENTAL,
    )
    pid_performance_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_PID_PERFORMANCE_FILE",
            DEFAULT_PID_PERFORMANCE_FILE,
            warn=False,
        ),
    )
    pid_coeffs_file = _resolve_cache_file(
        cache_dir,
        get_env(
            "QUALIA_PID_COEFFS_FILE",
            DEFAULT_PID_COEFFS_FILE,
            warn=False,
        ),
    )
    rate_limit = float(
        get_env("QUALIA_RATE_LIMIT", str(DEFAULT_RATE_LIMIT), warn=False)
    )
    backend_device = get_env(
        "QUALIA_BACKEND_DEVICE",
        DEFAULT_BACKEND_DEVICE,
        warn=False,
    ).upper()
    metacog_directive_ttl_seconds = int(
        get_env(
            "METACOG_DIRECTIVE_TTL_SECONDS",
            str(DEFAULT_METACOG_DIRECTIVE_TTL_SECONDS),
            warn=False,
        )
    )
    reduce_exposure_min_confidence = float(
        get_env(
            "REDUCE_EXPOSURE_MIN_CONFIDENCE",
            str(DEFAULT_REDUCE_EXPOSURE_MIN_CONFIDENCE),
            warn=False,
        )
    )
    reduce_exposure_min_position_age_minutes = float(
        get_env(
            "REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES",
            str(DEFAULT_REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES),
            warn=False,
        )
    )
    reduce_exposure_close_single_position = _env_bool(
        "REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION",
        DEFAULT_REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION,
    )
    reduce_exposure_default_pct = float(
        get_env(
            "REDUCE_EXPOSURE_DEFAULT_PCT",
            str(DEFAULT_REDUCE_EXPOSURE_DEFAULT_PCT),
            warn=False,
        )
    )
    order_exec_timeout = float(
        get_env(
            "ORDER_EXEC_TIMEOUT",
            str(DEFAULT_ORDER_EXEC_TIMEOUT),
            warn=False,
        )
    )
    sleep_time_warning_threshold = int(
        get_env(
            "SLEEP_TIME_WARNING_THRESHOLD",
            str(DEFAULT_SLEEP_TIME_WARNING_THRESHOLD),
            warn=False,
        )
    )
    ticker_retry_attempts = int(
        get_env(
            "TICKER_RETRY_ATTEMPTS",
            str(DEFAULT_TICKER_RETRY_ATTEMPTS),
            warn=False,
        )
    )
    ticker_reconnect_attempts = int(
        get_env(
            "TICKER_RECONNECT_ATTEMPTS",
            str(DEFAULT_TICKER_RECONNECT_ATTEMPTS),
            warn=False,
        )
    )
    connection_retry_wait = float(
        get_env(
            "CONNECTION_RETRY_WAIT",
            str(DEFAULT_CONNECTION_RETRY_WAIT),
            warn=False,
        )
    )
    connection_failure_limit = int(
        get_env(
            "CONNECTION_FAILURE_LIMIT",
            str(DEFAULT_CONNECTION_FAILURE_LIMIT),
            warn=False,
        )
    )
    connection_failure_cooldown = float(
        get_env(
            "CONNECTION_FAILURE_COOLDOWN",
            str(DEFAULT_CONNECTION_FAILURE_COOLDOWN),
            warn=False,
        )
    )
    max_error_history = int(
        get_env(
            "QUALIA_MAX_ERROR_HISTORY",
            str(DEFAULT_MAX_ERROR_HISTORY),
            warn=False,
        )
    )
    enable_bayes_opt = _env_bool(
        "QUALIA_ENABLE_BAYES_OPT",
        DEFAULT_ENABLE_BAYES_OPT,
    )
    allowed_vector_types = _load_allowed_vector_types()

    from .consciousness_defaults import load_consciousness_defaults

    consciousness_cfg = load_consciousness_defaults()
    n_qubits = int(consciousness_cfg.get("n_qubits", DEFAULT_CONSCIOUSNESS_N_QUBITS))
    n_steps = int(consciousness_cfg.get("n_steps", DEFAULT_CONSCIOUSNESS_N_STEPS))
    thermal_coefficient = float(
        consciousness_cfg.get(
            "thermal_coefficient", DEFAULT_CONSCIOUSNESS_THERMAL_COEFFICIENT
        )
    )
    retro_mode = str(
        consciousness_cfg.get("retro_mode", DEFAULT_CONSCIOUSNESS_RETRO_MODE)
    )
    perception_depth = int(
        consciousness_cfg.get(
            "perception_depth", DEFAULT_CONSCIOUSNESS_PERCEPTION_DEPTH
        )
    )
    self_reflection_enabled = bool(
        consciousness_cfg.get(
            "self_reflection_enabled",
            DEFAULT_CONSCIOUSNESS_SELF_REFLECTION_ENABLED,
        )
    )
    entropy_sensitivity = float(
        consciousness_cfg.get(
            "entropy_sensitivity",
            DEFAULT_CONSCIOUSNESS_ENTROPY_SENSITIVITY,
        )
    )
    history_maxlen = int(
        consciousness_cfg.get("history_maxlen", DEFAULT_CONSCIOUSNESS_HISTORY_MAXLEN)
    )
    pca_n_components_variance = float(
        consciousness_cfg.get(
            "pca_n_components_variance",
            DEFAULT_CONSCIOUSNESS_PCA_N_COMPONENTS_VARIANCE,
        )
    )
    pca_max_components = int(
        consciousness_cfg.get(
            "pca_max_components",
            DEFAULT_CONSCIOUSNESS_PCA_MAX_COMPONENTS,
        )
    )
    kmeans_range = consciousness_cfg.get(
        "kmeans_k_range", list(DEFAULT_CONSCIOUSNESS_KMEANS_K_RANGE)
    )
    if isinstance(kmeans_range, (list, tuple)) and len(kmeans_range) == 2:
        kmeans_k_min, kmeans_k_max = int(kmeans_range[0]), int(kmeans_range[1])
    else:
        kmeans_k_min, kmeans_k_max = DEFAULT_CONSCIOUSNESS_KMEANS_K_RANGE
    init_symbolic_processor = bool(
        consciousness_cfg.get(
            "init_symbolic_processor",
            DEFAULT_CONSCIOUSNESS_INIT_SYMBOLIC_PROCESSOR,
        )
    )
    return Settings(
        cache_dir=cache_dir,
        logs_dir=logs_dir,
        log_dir=log_dir,
        results_dir=results_dir,
        qast_backtest_results_dir=qast_backtest_results_dir,
        open_positions_file=open_positions_file,
        qpm_memory_file=qpm_memory_file,
        memory_file=memory_file,
        runs_json_dir=runs_json_dir,
        trading_log_file=trading_log_file,
        order_journal_file=order_journal_file,
        collector_state_file=collector_state_file,
        observer_persistence_dir=observer_persistence_dir,
        observer_persistence_filename=observer_persistence_filename,
        strategy_parameters_path=strategy_parameters_path,
        qpm_metrics_enabled=qpm_metrics_enabled,
        market_metrics_enabled=market_metrics_enabled,
        stm_metrics_enabled=stm_metrics_enabled,
        pid_metrics_enabled=pid_metrics_enabled,
        metrics_enabled=metrics_enabled,
        emergence_beta=emergence_beta,
        folding_alpha=folding_alpha,
        resonance_strength=resonance_strength,
        observer_unitary_atol=observer_unitary_atol,
        retrocausality_gamma=retrocausality_gamma,
        retro_operator_enabled=retro_operator_enabled,
        retro_operator_gamma=retro_operator_gamma,
        experimental_enabled=experimental_enabled,
        allowed_vector_types=allowed_vector_types,
        pid_performance_file=pid_performance_file,
        pid_coeffs_file=pid_coeffs_file,
        rate_limit=rate_limit,
        consciousness_n_qubits=n_qubits,
        consciousness_n_steps=n_steps,
        consciousness_thermal_coefficient=thermal_coefficient,
        consciousness_retro_mode=retro_mode,
        consciousness_perception_depth=perception_depth,
        consciousness_self_reflection_enabled=self_reflection_enabled,
        consciousness_entropy_sensitivity=entropy_sensitivity,
        consciousness_history_maxlen=history_maxlen,
        consciousness_pca_n_components_variance=pca_n_components_variance,
        consciousness_pca_max_components=pca_max_components,
        consciousness_kmeans_k_min=kmeans_k_min,
        consciousness_kmeans_k_max=kmeans_k_max,
        consciousness_init_symbolic_processor=init_symbolic_processor,
        backend_device=backend_device,
        metacog_directive_ttl_seconds=metacog_directive_ttl_seconds,
        reduce_exposure_min_confidence=reduce_exposure_min_confidence,
        reduce_exposure_min_position_age_minutes=reduce_exposure_min_position_age_minutes,
        reduce_exposure_close_single_position=reduce_exposure_close_single_position,
        reduce_exposure_default_pct=reduce_exposure_default_pct,
        order_exec_timeout=order_exec_timeout,
        sleep_time_warning_threshold=sleep_time_warning_threshold,
        ticker_retry_attempts=ticker_retry_attempts,
        ticker_reconnect_attempts=ticker_reconnect_attempts,
        connection_retry_wait=connection_retry_wait,
        connection_failure_limit=connection_failure_limit,
        connection_failure_cooldown=connection_failure_cooldown,
        max_error_history=max_error_history,
        enable_bayes_opt=enable_bayes_opt,
        high_entropy_threshold=float(
            consciousness_cfg.get(
                "high_entropy_threshold",
                DEFAULT_HIGH_ENTROPY_THRESHOLD,
            )
        ),
        low_coherence_threshold=float(
            consciousness_cfg.get(
                "low_coherence_threshold",
                DEFAULT_LOW_COHERENCE_THRESHOLD,
            )
        ),
    )


settings = load_settings()

cache_dir = settings.cache_dir
logs_dir = settings.logs_dir
log_dir = settings.log_dir
results_dir = settings.results_dir
qast_backtest_results_dir = settings.qast_backtest_results_dir
open_positions_file = settings.open_positions_file
qpm_memory_file = settings.qpm_memory_file
memory_file = settings.memory_file
runs_json_dir = settings.runs_json_dir
trading_log_file = settings.trading_log_file
order_journal_file = settings.order_journal_file
collector_state_file = settings.collector_state_file
observer_persistence_dir = settings.observer_persistence_dir
observer_persistence_filename = settings.observer_persistence_filename
strategy_parameters_path = settings.strategy_parameters_path
qpm_metrics_enabled = settings.qpm_metrics_enabled
market_metrics_enabled = settings.market_metrics_enabled
stm_metrics_enabled = settings.stm_metrics_enabled
pid_metrics_enabled = settings.pid_metrics_enabled
metrics_enabled = settings.metrics_enabled
emergence_beta = settings.emergence_beta
folding_alpha = settings.folding_alpha
resonance_strength = settings.resonance_strength
observer_unitary_atol = settings.observer_unitary_atol
retrocausality_gamma = settings.retrocausality_gamma
retro_operator_enabled = settings.retro_operator_enabled
retro_operator_gamma = settings.retro_operator_gamma
experimental_enabled = settings.experimental_enabled
allowed_vector_types = settings.allowed_vector_types
pid_performance_file = settings.pid_performance_file
pid_coeffs_file = settings.pid_coeffs_file
rate_limit = settings.rate_limit
consciousness_n_qubits = settings.consciousness_n_qubits
consciousness_n_steps = settings.consciousness_n_steps
consciousness_thermal_coefficient = settings.consciousness_thermal_coefficient
consciousness_retro_mode = settings.consciousness_retro_mode
consciousness_perception_depth = settings.consciousness_perception_depth
consciousness_self_reflection_enabled = settings.consciousness_self_reflection_enabled
consciousness_entropy_sensitivity = settings.consciousness_entropy_sensitivity
consciousness_history_maxlen = settings.consciousness_history_maxlen
consciousness_pca_n_components_variance = (
    settings.consciousness_pca_n_components_variance
)
consciousness_pca_max_components = settings.consciousness_pca_max_components
consciousness_kmeans_k_min = settings.consciousness_kmeans_k_min
consciousness_kmeans_k_max = settings.consciousness_kmeans_k_max
consciousness_init_symbolic_processor = settings.consciousness_init_symbolic_processor

backend_device = settings.backend_device
metacog_directive_ttl_seconds = settings.metacog_directive_ttl_seconds
reduce_exposure_min_confidence = settings.reduce_exposure_min_confidence
reduce_exposure_min_position_age_minutes = (
    settings.reduce_exposure_min_position_age_minutes
)
reduce_exposure_close_single_position = settings.reduce_exposure_close_single_position
reduce_exposure_default_pct = settings.reduce_exposure_default_pct
order_exec_timeout = settings.order_exec_timeout
sleep_time_warning_threshold = settings.sleep_time_warning_threshold
ticker_retry_attempts = settings.ticker_retry_attempts
ticker_reconnect_attempts = settings.ticker_reconnect_attempts
connection_retry_wait = settings.connection_retry_wait
connection_failure_limit = settings.connection_failure_limit
connection_failure_cooldown = settings.connection_failure_cooldown
max_error_history = settings.max_error_history
enable_bayes_opt = settings.enable_bayes_opt
high_entropy_threshold = settings.high_entropy_threshold
low_coherence_threshold = settings.low_coherence_threshold


__all__ = [
    "Settings",
    "settings",
    "load_settings",
    "get_env",
    "require_env",
    "qpm_metrics_enabled",
    "market_metrics_enabled",
    "stm_metrics_enabled",
    "pid_metrics_enabled",
    "metrics_enabled",
    "emergence_beta",
    "folding_alpha",
    "resonance_strength",
    "observer_unitary_atol",
    "retrocausality_gamma",
    "retro_operator_enabled",
    "retro_operator_gamma",
    "experimental_enabled",
    "allowed_vector_types",
    "pid_performance_file",
    "pid_coeffs_file",
    "rate_limit",
    "consciousness_n_qubits",
    "consciousness_n_steps",
    "consciousness_thermal_coefficient",
    "consciousness_retro_mode",
    "consciousness_perception_depth",
    "consciousness_self_reflection_enabled",
    "consciousness_entropy_sensitivity",
    "consciousness_history_maxlen",
    "consciousness_pca_n_components_variance",
    "consciousness_pca_max_components",
    "consciousness_kmeans_k_min",
    "consciousness_kmeans_k_max",
    "consciousness_init_symbolic_processor",
    "backend_device",
    "metacog_directive_ttl_seconds",
    "reduce_exposure_min_confidence",
    "reduce_exposure_min_position_age_minutes",
    "reduce_exposure_close_single_position",
    "reduce_exposure_default_pct",
    "order_exec_timeout",
    "sleep_time_warning_threshold",
    "ticker_retry_attempts",
    "ticker_reconnect_attempts",
    "connection_retry_wait",
    "connection_failure_limit",
    "connection_failure_cooldown",
    "max_error_history",
    "enable_bayes_opt",
    "open_positions_file",
    "qpm_memory_file",
    "memory_file",
    "runs_json_dir",
    "trading_log_file",
    "order_journal_file",
    "collector_state_file",
    "observer_persistence_dir",
    "observer_persistence_filename",
    "high_entropy_threshold",
    "low_coherence_threshold",
    "get_config_file_path",
]
