"""Base interface for QUALIA memory backends.

Example
-------
>>> class InMemoryBackend(BaseMemory):
...     _store: Dict[str, Any] = {}
...     def store(
...         self,
...         key: str,
...         value: Any,
...         metadata: Optional[Dict[str, Any]] = None,
...     ) -> None:
...         self._store[key] = value
...     def retrieve(self, key: str) -> Optional[Any]:
...         return self._store.get(key)
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

# YAA: Importações de QUALIA devem usar src.qualia
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BaseMemory(ABC):
    """Abstract interface for memory systems used by QUALIA."""

    @abstractmethod
    def store(
        self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Store a value under a key.

        Args:
            key: Unique identifier for the value.
            value: Object to be stored.
            metadata: Optional metadata such as TTL.
        """
        pass

    @abstractmethod
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve a value by key.

        Args:
            key: Identifier of the value to fetch.

        Returns:
            The stored object or ``None`` if not found.
        """
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete a value by key.

        Args:
            key: Identifier to remove from memory.

        Returns:
            ``True`` if the key existed and was removed.
        """
        pass

    @abstractmethod
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """List all stored keys.

        Args:
            prefix: If provided, only keys starting with this prefix are returned.

        Returns:
            A list of key strings.
        """
        pass

    @abstractmethod
    def clear(self) -> None:
        """Remove all entries from memory."""
        pass

    def get_info(self) -> Dict[str, Any]:
        """Return information about the current memory status."""

        return {"type": self.__class__.__name__, "status": "Not implemented"}


__all__ = ["BaseMemory"]
