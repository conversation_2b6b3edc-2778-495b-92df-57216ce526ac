from __future__ import annotations

"""Threshold parameter optimizer for NEXUS.

Performs grid search over alpha/offset to maximize F1 on historical data.
Outputs best params to YAML config file.
"""

import itertools
import json
try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
from pathlib import Path
from typing import Dict, Tuple

from .historical_validator import validate, prf1
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def grid_search(
    history_path: str | Path,
    events_path: str | Path,
    alphas=(0.05, 0.1, 0.2, 0.3),
    offsets=(0.02, 0.05, 0.1, 0.15),
    window_days=30,
) -> Tuple[Dict[str, float], Dict[str, float]]:
    best_params: Dict[str, float] = {}
    best_score = -1.0
    best_metrics: Dict[str, float] = {}
    for a, off in itertools.product(alphas, offsets):
        results = validate(history_path, events_path, window_days)
        metrics = prf1(results)
        f1 = metrics["f1"]
        logger.debug("alpha=%.2f off=%.2f -> F1 %.3f", a, off, f1)
        if f1 > best_score:
            best_score = f1
            best_params = {"alpha": a, "offset": off}
            best_metrics = metrics
    return best_params, best_metrics


def save_config(params: Dict[str, float], path: str | Path) -> None:
    with open(path, "w", encoding="utf-8") as fh:
        yaml.safe_dump(params, fh)
    logger.info("Threshold params salvos em %s", path)


if __name__ == "__main__":  # pragma: no cover
    import argparse

    ap = argparse.ArgumentParser(description="Optimize threshold parameters.")
    ap.add_argument("--history", required=True)
    ap.add_argument("--events", required=True)
    ap.add_argument("--output", default="config/nexus_threshold.yaml")
    args = ap.parse_args()
    best_p, best_m = grid_search(args.history, args.events)
    logger.info("Best params %s %s", best_p, best_m)
    save_config(best_p, args.output)
