from __future__ import annotations

try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
import os
import time
import asyncio
from typing import Any, Dict, List, Optional, TYPE_CHECKING
from collections import defaultdict

import numpy as np
from pydantic import ValidationError

from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger
from ..config.settings import REPO_ROOT

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from .quantum_pattern_memory import QuantumPatternMemory

logger = get_logger(__name__)

DEFAULT_WARMSTART_CONFIG_PATH = REPO_ROOT / "config" / "warmstart.yaml"


def load_warmstart_scenarios(path: Optional[str] = None) -> List[Dict[str, Any]]:
    """Load warm-start scenarios from a YAML configuration file.

    Parameters
    ----------
    path : Optional[str], default ``None``
        Custom configuration path. When ``None``, the environment variable
        ``QPM_WARMSTART_CONFIG`` is consulted. If it is unset, the function
        falls back to :data:`DEFAULT_WARMSTART_CONFIG_PATH`.
    """

    config_path = path or os.getenv(
        "QPM_WARMSTART_CONFIG", str(DEFAULT_WARMSTART_CONFIG_PATH)
    )
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f) or {}
        scenarios = data.get("scenarios", [])
        if not scenarios:
            logger.warning("QPM: Nenhum cenário encontrado em %s", config_path)
        return scenarios
    except FileNotFoundError:
        logger.warning(
            "QPM: Arquivo de configuração do warm-start não encontrado em %s",
            config_path,
        )
    except (yaml.YAMLError, OSError) as exc:
        logger.error(
            "QPM: Falha ao carregar configuração do warm-start %s: %s",
            config_path,
            exc,
        )
    return []


class WarmStartManager:
    """Generate synthetic patterns to bootstrap :class:`QuantumPatternMemory`.

    This helper encapsulates the warm-start workflow. It requires a reference to
    ``QuantumPatternMemory`` and is responsible for creating and storing
    synthetic examples when the real dataset is too small.
    """

    def __init__(self, qpm: "QuantumPatternMemory") -> None:
        self.qpm = qpm
        self.scenario_metrics: Dict[str, Dict[str, List[float]]] = defaultdict(
            lambda: {"quantum_scores": [], "pnls": []}
        )
        self._update_task: Optional[asyncio.Task] = None

    def _is_memory_insufficient(self) -> bool:
        """Return ``True`` when stored patterns do not meet ``warmstart_min_patterns``.

        The check aggregates the number of patterns across all vector
        dimensionalities. When the count is lower than the configured
        threshold a warm start may be triggered.
        """
        return (
            sum(len(patterns) for patterns in self.qpm.memory.values())
            < self.qpm.warmstart_min_patterns
        )

    def warm_start(self) -> None:
        """Populate memory with synthetic patterns when it is considered empty.

        When ``enable_warmstart`` is ``True`` and the current number of stored
        patterns is below :attr:`warmstart_min_patterns`, this method generates
        a predefined set of synthetic patterns so that similarity retrieval has
        some data to work with.
        """
        start_time = time.perf_counter()
        if not self.qpm.enable_warmstart:
            logger.info("QPM.warm_start: warm-start desabilitado")
            if self.qpm.statsd:
                self.qpm.statsd.increment("qpm.warmstart.disabled")
                self.qpm.statsd.timing(
                    "qpm.warmstart_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return
        if not self._is_memory_insufficient():
            logger.debug("QPM.warm_start: memória já populada, pulando")
            if self.qpm.statsd:
                self.qpm.statsd.increment("qpm.warmstart.skipped")
                self.qpm.statsd.timing(
                    "qpm.warmstart_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
            return
        self._perform_warmstart()
        if self.qpm.statsd:
            self.qpm.statsd.increment("qpm.warmstart.executed")
            self.qpm.statsd.timing(
                "qpm.warmstart_ms",
                (time.perf_counter() - start_time) * 1000,
            )

    def _perform_warmstart(self) -> None:
        """Generate a batch of profitable synthetic patterns.

        The method iterates through a set of hard coded market scenarios and
        creates several variations of each one using
        :meth:`_create_synthetic_pattern`. Generated patterns are stored in the
        underlying memory and optionally persisted when
        ``self.persistence_path`` is configured.
        """
        start_time = time.perf_counter()
        logger.info("P-9: Iniciando warm-start com padrões sintéticos rentáveis...")

        profitable_patterns = self.qpm.warmstart_scenarios or []
        if not profitable_patterns:
            logger.warning("QPM: Nenhum cenário de warm-start configurado")
            # YAA: Criar cenários genéricos como fallback para evitar loop infinito
            profitable_patterns = [
                {
                    "dimension": 1024,
                    "base_vector_seed_params": [0.5, 0.3, 0.7, 0.2, 0.6, 0.4, 0.8, 0.1],
                    "market_scenario": "fallback_generic",
                    "expected_pnl_pct": 1.0,
                    "confidence": 0.6,
                    "signal_type": "HOLD",
                }
            ]
            logger.info("P-9: Usando cenários genéricos de fallback para warm-start")
            if self.qpm.statsd:
                self.qpm.statsd.increment("qpm.warmstart.fallback_scenarios")

        patterns_created = 0
        quantum_scores = []
        for pattern_config in profitable_patterns:
            variations = pattern_config.get(
                "num_variations", self.qpm.warmstart_variations
            )
            for variation in range(variations):
                synthetic_q_score = self._create_synthetic_pattern(
                    pattern_config, variation
                )
                if synthetic_q_score is not None:
                    patterns_created += 1
                    quantum_scores.append(synthetic_q_score)

        # Guarantee minimum patterns when scenarios are insufficient
        idx = 0
        while patterns_created < self.qpm.warmstart_min_patterns:
            config = profitable_patterns[idx % len(profitable_patterns)]
            synthetic_q_score = self._create_synthetic_pattern(config, idx + variations)
            if synthetic_q_score is not None:
                patterns_created += 1
                quantum_scores.append(synthetic_q_score)
            idx += 1

        distribution_msg = "sem scores"
        if quantum_scores:
            distribution_msg = (
                f"min={float(np.min(quantum_scores)):.3f}, "
                f"max={float(np.max(quantum_scores)):.3f}, "
                f"avg={float(np.mean(quantum_scores)):.3f}"
            )

        logger.info(
            f"P-9: Warm-start concluído. {patterns_created} padrões sintéticos "
            f"criados. Distribuição dos quantum_scores: {distribution_msg}."
        )

        if self.qpm.persistence_path and getattr(self.qpm, "auto_persist", False):
            self.qpm.save_to_file()
            logger.info("P-9: Padrões sintéticos salvos no arquivo de persistência.")

        if patterns_created and (
            self.qpm.similarity_threshold is None or self.qpm.similarity_threshold > 0.5
        ):
            self.qpm.set_similarity_threshold(0.5)
            logger.info("P-9: similarity_threshold ajustado para 0.5 após warm-start")
        if self.qpm.statsd:
            self.qpm.statsd.increment("qpm.warmstart.batch")
            self.qpm.statsd.timing(
                "qpm.warmstart_ms",
                (time.perf_counter() - start_time) * 1000,
            )

    def _perform_warmstart_for_dimension(self, dimension: int) -> None:
        """Warm-start a specific vector dimension.

        Parameters
        ----------
        dimension : int
            Dimension whose partition is currently empty.

        This helper is invoked automatically when a retrieval is requested for
        a dimension that has no stored patterns. It creates a small set of
        generic synthetic examples using :meth:`_create_synthetic_pattern`.
        """
        if not self._is_memory_insufficient():
            logger.info(
                "QPM: Warm-start para %sD ignorado; memória já contém %s padrões.",
                dimension,
                sum(len(p) for p in self.qpm.memory.values()),
            )
            if self.qpm.statsd:
                self.qpm.statsd.increment("qpm.warmstart.skip_dimension")
            return

        start_time = time.perf_counter()
        base_configs = [
            {
                "dimension": dimension,
                "base_vector_seed_params": [0.5, 0.3, 0.7, 0.2, 0.6, 0.4, 0.8, 0.1],
                "market_scenario": "auto_warmstart_generic",
                "expected_pnl_pct": 1.0,
                "confidence": 0.6,
                "signal_type": "HOLD",
            }
        ]

        patterns_before = len(self.qpm.memory.get(dimension, []))
        quantum_scores = []
        for config in base_configs:
            for variation in range(self.qpm.warmstart_variations):
                q_score = self._create_synthetic_pattern(config, variation)
                if q_score is not None:
                    quantum_scores.append(q_score)

        patterns_after = len(self.qpm.memory.get(dimension, []))
        distribution_msg = "sem scores"
        if quantum_scores:
            distribution_msg = (
                f"min={float(np.min(quantum_scores)):.3f}, "
                f"max={float(np.max(quantum_scores)):.3f}, "
                f"avg={float(np.mean(quantum_scores)):.3f}"
            )
        logger.info(
            f"QPM: Warm-start para {dimension}D gerou {patterns_after - patterns_before} padrões. "
            f"Distribuição dos quantum_scores: {distribution_msg}."
        )
        if self.qpm.persistence_path and getattr(self.qpm, "auto_persist", False):
            self.qpm.save_to_file()
            logger.info("QPM: Padrões de warm-start salvos no arquivo de persistência.")
        if self.qpm.statsd:
            self.qpm.statsd.increment("qpm.warmstart.dimension")
            self.qpm.statsd.timing(
                "qpm.warmstart_ms",
                (time.perf_counter() - start_time) * 1000,
            )

    def _create_synthetic_pattern(
        self, config: Dict[str, Any], variation_index: int
    ) -> Optional[float]:
        """Create a single synthetic pattern instance.

        Parameters
        ----------
        config : dict
            Configuration describing the base scenario to mimic.
        variation_index : int
            Index used to vary the random seed to generate distinct patterns.

        Returns
        -------
        Optional[float]
            The generated ``quantum_score`` if the pattern was successfully
            stored, otherwise ``None``.
        """
        try:
            dimension = config["dimension"]
            base_vector_seed_params = config["base_vector_seed_params"]

            rng_seed = 42 + variation_index + int(sum(base_vector_seed_params) * 1000)
            rng = np.random.default_rng(rng_seed)

            base_params = np.array(base_vector_seed_params, dtype=float)
            param_noise = rng.normal(
                0.0, 0.02 * (variation_index + 1), size=base_params.shape
            )
            adjusted_params = np.clip(base_params + param_noise, 0.0, 1.0)

            noise_factor = 0.05 + variation_index * 0.01
            noise_factor += np.std(adjusted_params) * 0.05

            repeated_params = np.tile(
                adjusted_params, int(np.ceil(dimension / len(adjusted_params)))
            )
            base_vector_target_dim = repeated_params[:dimension]

            varied_vector = []
            for base_val in base_vector_target_dim:
                noise = rng.normal(0, noise_factor)
                new_val = np.clip(base_val + noise, 0.0, 1.0)
                varied_vector.append(float(new_val))

            base_conf = config.get("confidence", 0.5)
            quantum_score = float(
                np.clip(base_conf + rng.uniform(-0.1, 0.15), 0.0, 1.0)
            )

            synthetic_qsp = QuantumSignaturePacket(
                vector=varied_vector,
                metrics={
                    "confidence": config["confidence"] + rng.normal(0, 0.05),
                    "signal_strength": rng.uniform(0.6, 0.9),
                    "synthetic": True,
                    "warmstart_pattern": True,
                    "base_scenario": config["market_scenario"],
                    "quantum_score": quantum_score,
                },
                timestamp=time.time() - rng.uniform(86400, 604800),
                source_details={
                    "encoder_name": f"synthetic_{config['market_scenario']}",
                    "source": "warmstart_generator",
                    "variation_index": variation_index,
                    "n_qubits": 9,
                    "notes": "Synthetic QSP for QPM warmstart, 1024-dim vector from 9 effective qubits.",
                },
            )

            market_snapshot = {
                "scenario_type": config["market_scenario"],
                "price": 50000 + rng.uniform(-5000, 5000),
                "volume": rng.uniform(100, 1000),
                "volatility": rng.uniform(0.015, 0.045),
                "rsi": rng.uniform(30, 70),
                "synthetic": True,
                "timestamp": time.time() - rng.uniform(86400, 604800),
            }

            pnl_base = config["expected_pnl_pct"]
            pnl_variation = pnl_base + rng.normal(0, pnl_base * 0.2)

            outcome = {
                "signal_at_entry": config["signal_type"],
                "pnl_percentage": pnl_variation,
                "pnl": pnl_variation * market_snapshot["price"] * 0.01,
                "final_signal_before_close": config["signal_type"],
                "side": (
                    "long"
                    if config["signal_type"] == "BUY"
                    else "short" if config["signal_type"] == "SELL" else "neutral"
                ),
                "confidence_at_entry": config["confidence"],
                "profitable": pnl_variation > 0.5,
                "synthetic": True,
                "warmstart_source": True,
            }

            decision_context = {
                "strategy_id": f"synthetic_warmstart_{config['market_scenario']}",
                "timeframe": "5m",
                "market_conditions": config["market_scenario"],
                "synthetic": True,
                "warmstart_generated": True,
                "generation_timestamp": time.time(),
            }

            stored = self.qpm.store_pattern(
                quantum_signature_packet=synthetic_qsp,
                market_snapshot=market_snapshot,
                outcome=outcome,
                decision_context=decision_context,
            )

            if stored:
                metrics = self.scenario_metrics[config["market_scenario"]]
                metrics["quantum_scores"].append(quantum_score)
                metrics["pnls"].append(outcome["pnl_percentage"])
                return quantum_score

            return None

        except (ValidationError, ValueError, TypeError) as e:
            logger.error("P-9: Erro ao criar padrão sintético: %s", e, exc_info=True)
            return None
        except Exception as exc:  # pragma: no cover
            logger.exception(
                "P-9: Erro inesperado ao criar padrão sintético", exc_info=exc
            )
            raise

    def get_warmstart_stats(self) -> Dict[str, Any]:
        """Return statistics about synthetic patterns currently stored.

        Returns
        -------
        Dict[str, Any]
            Summary with the total number of synthetic patterns, how many were
            flagged as profitable and additional warm-start configuration
            values.
        """
        synthetic_count = 0
        profitable_synthetic = 0

        for patterns_list in self.qpm.memory.values():
            for pattern in patterns_list:
                outcome = pattern.get("outcome", {})
                if outcome.get("synthetic", False):
                    synthetic_count += 1
                    if outcome.get("profitable", False):
                        profitable_synthetic += 1

        return {
            "total_synthetic_patterns": synthetic_count,
            "profitable_synthetic_patterns": profitable_synthetic,
            "synthetic_profitability_rate": (
                profitable_synthetic / synthetic_count if synthetic_count > 0 else 0.0
            ),
            "warmstart_enabled": self.qpm.enable_warmstart,
            "min_patterns_threshold": self.qpm.warmstart_min_patterns,
            "configured_scenarios": len(self.qpm.warmstart_scenarios or []),
            "warmstart_variations": self.qpm.warmstart_variations,
        }

    def update_scenarios(self, path: Optional[str] = None) -> None:
        """Rewrite the warm-start configuration with average metrics."""

        config_path = path or os.getenv(
            "QPM_WARMSTART_CONFIG", str(DEFAULT_WARMSTART_CONFIG_PATH)
        )
        scenarios = load_warmstart_scenarios(config_path)
        if not scenarios:
            logger.warning("QPM.update_scenarios: nenhuma configuração encontrada")
            return

        for scenario in scenarios:
            perf = self.scenario_metrics.get(scenario.get("market_scenario"))
            if not perf:
                continue
            if perf["quantum_scores"]:
                scenario["confidence"] = float(np.mean(perf["quantum_scores"]))
            if perf["pnls"]:
                scenario["expected_pnl_pct"] = float(np.mean(perf["pnls"]))

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                yaml.safe_dump({"scenarios": scenarios}, f, sort_keys=False)
            logger.info("QPM: warmstart.yaml atualizado em %s", config_path)
        except OSError as exc:
            logger.error("QPM: Falha ao atualizar warmstart.yaml: %s", exc)

    def start_auto_update(
        self, interval_seconds: float, path: Optional[str] = None
    ) -> None:
        """Start periodic scenario updates."""

        if self._update_task:
            self._update_task.cancel()
        self._update_task = asyncio.create_task(
            self._auto_update_loop(interval_seconds, path)
        )

    async def _auto_update_loop(
        self, interval_seconds: float, path: Optional[str]
    ) -> None:
        try:
            while True:
                await asyncio.sleep(interval_seconds)
                self.update_scenarios(path)
        except asyncio.CancelledError:
            logger.info("QPM: auto update loop cancelado")

    def stop_auto_update(self) -> None:
        """Cancel periodic scenario updates."""

        if self._update_task:
            self._update_task.cancel()
            self._update_task = None


__all__ = ["WarmStartManager", "load_warmstart_scenarios"]
