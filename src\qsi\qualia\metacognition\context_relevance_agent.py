from __future__ import annotations

"""Context Relevance Agent.

Monitora eventos de mercado para ajustar dinamicamente o ritmo de coleta de dados
no :class:`RealDataCollector` e publicar métricas de foco no ``event_bus``.
"""

import time
from dataclasses import dataclass
from typing import List

from ..utils.logger import get_logger
from ..memory.event_bus import SimpleEventBus
from ..utils.event_bus import MarketDataUpdated
from ..events import FocusMetricEvent
from ..consciousness.real_data_collectors import RealDataCollector, MarketDataPoint
from . import service

logger = get_logger(__name__)


@dataclass
class FocusMetric:
    """Estrutura publicada com o nível de foco calculado."""

    focus: float
    interval: float
    timestamp: float


class ContextRelevanceAgent:
    """Ajusta a coleta conforme a volatilidade observada."""

    def __init__(
        self,
        collector: RealDataCollector,
        event_bus: SimpleEventBus,
        *,
        high_vol_threshold: float = 5.0,
        low_vol_threshold: float = 1.0,
        min_interval: float = 10.0,
        max_interval: float = 120.0,
    ) -> None:
        self.collector = collector
        self.event_bus = event_bus
        self.high_vol = high_vol_threshold
        self.low_vol = low_vol_threshold
        self.min_interval = min_interval
        self.max_interval = max_interval
        event_bus.subscribe("market.data.updated", self._on_market_update)
        logger.info("ContextRelevanceAgent iniciado")

    # ------------------------------------------------------------------
    def _calculate_interval(self, volatility: float) -> float:
        if volatility >= self.high_vol:
            return self.min_interval
        if volatility <= self.low_vol:
            return self.max_interval
        ratio = (volatility - self.low_vol) / (self.high_vol - self.low_vol)
        return self.max_interval - ratio * (self.max_interval - self.min_interval)

    # ------------------------------------------------------------------
    def _on_market_update(self, payload: MarketDataUpdated) -> None:
        data: List[MarketDataPoint] = payload.market_data
        if not data:
            return
        vol = sum(abs(p.change_24h) for p in data) / len(data)
        interval = self._calculate_interval(vol)
        self.collector.set_collection_interval(interval)
        focus = (self.max_interval - interval) / (self.max_interval - self.min_interval)
        service.push_metric("foco", focus)
        metric = FocusMetricEvent(focus=focus, interval=interval, timestamp=time.time())
        self.event_bus.publish("metacog.focus", metric)
        logger.debug(
            "ContextRelevanceAgent: vol=%.3f -> interval=%.1f foco=%.2f",
            vol,
            interval,
            focus,
        )


__all__ = ["ContextRelevanceAgent", "FocusMetric"]
