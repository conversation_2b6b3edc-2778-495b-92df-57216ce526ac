from __future__ import annotations

"""Dash dashboard visualizing GA evolution of threshold parameters."""

import datetime
from typing import List, Tuple

import dash
import plotly.graph_objects as go
from dash import html, dcc, Input, Output

from qualia.analysis.ga_threshold_evolver import GLOBAL_EVENT_BUS
from qualia.utils.logger import get_logger
from qualia.memory.event_bus import SimpleEventBus

logger = get_logger(__name__)

_GA_HISTORY: List[Tuple[int, float, float, float, datetime.datetime]] = []


def init_app(bus: SimpleEventBus | None = None) -> dash.Dash:
    app = dash.Dash(__name__)
    app.layout = html.Div(
        [
            html.H3("NEXUS – GA Evolution"),
            dcc.Graph(id="ga-params"),
            dcc.Graph(id="ga-f1"),
            dcc.Interval(id="ga-interval", interval=2000, n_intervals=0),
        ]
    )

    @app.callback(
        Output("ga-params", "figure"),
        Output("ga-f1", "figure"),
        Input("ga-interval", "n_intervals"),
    )
    def _update(_: int) -> tuple[go.Figure, go.Figure]:  # noqa: D401
        if not _GA_HISTORY:
            return go.Figure(), go.Figure()
        gens, alphas, offsets, f1s, tss = zip(*_GA_HISTORY)
        fig_params = go.Figure()
        fig_params.add_trace(
            go.Scatter(x=gens, y=alphas, mode="lines+markers", name="alpha")
        )
        fig_params.add_trace(
            go.Scatter(x=gens, y=offsets, mode="lines+markers", name="offset")
        )
        fig_params.update_layout(
            title="Alpha / Offset por Geração", xaxis_title="Geração"
        )

        fig_f1 = go.Figure()
        fig_f1.add_trace(go.Scatter(x=gens, y=f1s, mode="lines+markers", name="F1"))
        fig_f1.update_layout(
            title="F1 por Geração", xaxis_title="Geração", yaxis_range=[0, 1]
        )
        return fig_params, fig_f1

    logger.info("Dashboard NEXUS-GA inicializado")

    # subscribe after callbacks set
    event_bus = bus or GLOBAL_EVENT_BUS
    if event_bus is not None:
        event_bus.subscribe(
            "nexus.ga.generation",
            lambda p: _GA_HISTORY.append(
                (p["gen"], p["alpha"], p["offset"], p["f1"], datetime.datetime.utcnow())
            ),
        )
    else:
        logger.warning(
            "NEXUS-GA dashboard: event bus not available; no data will be shown."
        )

    return app
