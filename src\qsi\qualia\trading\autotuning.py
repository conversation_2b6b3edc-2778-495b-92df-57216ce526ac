from __future__ import annotations

"""Optuna-based hyperparameter tuning utilities."""

from typing import Any, Callable, Dict, Sequence

import optuna

from ..config.settings import settings
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BacktestRequiredError(RuntimeError):
    """Raised when no backtest metrics are supplied."""


def _require_metrics(metrics: Sequence[Dict[str, Any]]) -> None:
    if not metrics:
        raise BacktestRequiredError(
            "Bayesian optimisation requires offline backtest metrics"
        )


def bayes_optimize(
    objective: Callable[[Dict[str, Any], Sequence[Dict[str, Any]]], float],
    param_space: Dict[str, Dict[str, Any]],
    metrics: Sequence[Dict[str, Any]],
    n_trials: int = 10,
    *,
    sampler_seed: int | None = None,
) -> Dict[str, Any]:
    """Run Bayesian optimisation over ``param_space`` using ``optuna``.

    Parameters
    ----------
    objective
        Function evaluating a parameter set against ``metrics``.
    param_space
        Dictionary describing the search space. Each entry must specify
        ``type`` and bounds plus an optional ``default``.
    metrics
        Offline backtest metrics guiding the search.
    n_trials
        Number of optimisation trials.
    sampler_seed
        Optional seed for reproducible sampling.

    Returns
    -------
    Dict[str, Any]
        Best parameter values found. When optimisation is disabled via
        configuration, the defaults from ``param_space`` are returned.
    """
    if not settings.enable_bayes_opt:
        logger.info("Bayesian optimisation disabled via configuration")
        return {k: v.get("default") for k, v in param_space.items()}

    _require_metrics(metrics)

    sampler = optuna.samplers.TPESampler(seed=sampler_seed)
    study = optuna.create_study(direction="maximize", sampler=sampler)

    def _objective(trial: optuna.Trial) -> float:
        params: Dict[str, Any] = {}
        for name, spec in param_space.items():
            ptype = spec.get("type")
            if ptype == "float":
                params[name] = trial.suggest_float(name, spec["low"], spec["high"])
            elif ptype == "int":
                params[name] = trial.suggest_int(name, spec["low"], spec["high"])
            elif ptype == "categorical":
                params[name] = trial.suggest_categorical(name, spec["choices"])
            else:
                raise ValueError(f"Unsupported parameter type: {ptype}")
        return float(objective(params, metrics))

    study.optimize(_objective, n_trials=n_trials)
    logger.info("Bayesian optimisation completed: %s", study.best_params)
    return study.best_params


__all__ = ["bayes_optimize", "BacktestRequiredError"]
