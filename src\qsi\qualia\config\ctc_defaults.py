"""Provide defaults for Circular Tendency Correction utilities.

Settings are read from ``config/ctc_defaults.yaml`` unless overridden by
``QUALIA_CTC_DEFAULTS``.
"""

from __future__ import annotations

from typing import Any, Dict

try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()  # type: ignore

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("ctc_defaults.yaml")


def load_ctc_defaults() -> Dict[str, Any]:
    """Load default parameters for CTC utilities from YAML.

    The ``QUALIA_CTC_DEFAULTS`` environment variable can override the bundled
    ``config/ctc_defaults.yaml`` file.

    Returns
    -------
    Dict[str, Any]
        Mapping of parameter names to values. Returns an empty dict on error.
    """

    return load_yaml_config(
        "QUALIA_CTC_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_ctc_defaults"]
