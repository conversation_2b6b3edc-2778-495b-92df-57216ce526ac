"""Network resilience utilities for API calls.

This module provides a simple asynchronous circuit breaker and helper
function for performing API calls with exponential backoff. It is used
by ``KrakenIntegration`` to harden requests to external exchanges.

Default parameters are loaded from ``config/utils.yaml`` when the feature
flag ``QUALIA_FT_UTILS_V2`` está habilitado. Valores individuais podem
ser sobrescritos pelas variáveis de ambiente ``QUALIA_CB_FAIL_THRESHOLD``
e ``QUALIA_CALL_MAX_RETRIES``.
"""

from __future__ import annotations

import asyncio
import time
from typing import Any, Awaitable, Callable

import os
from datadog import DogStatsd

from ..config.utils_defaults import load_utils_defaults
from ..config.feature_flags import feature_toggle
from ..memory.event_bus import SimpleEventBus

from ..config.settings import market_metrics_enabled
from .logger import get_logger
from .tracing import instrument_logger, is_tracing_enabled
from .metrics_persistence import persist_metric_to_qpm

"""Event name published when a circuit breaker changes state."""
CIRCUIT_BREAKER_STATE_EVENT = "utils.circuit_breaker_state"

logger = get_logger(__name__)

_DEFAULTS = load_utils_defaults() if feature_toggle("utils_v2") else {}
_TRACE_LOGGING = bool(_DEFAULTS.get("trace_instrumentation_enabled", False))

if is_tracing_enabled():  # pragma: no cover - configuration
    instrument_logger(logger.logger)
_PARAMS = _DEFAULTS.get("network_resilience", {}) if feature_toggle("utils_v2") else {}

CB_FAIL_THRESHOLD = int(
    os.getenv(
        "QUALIA_CB_FAIL_THRESHOLD",
        _PARAMS.get("circuit_breaker_fail_threshold", 5),
    )
)
CB_RECOVERY_TIMEOUT = float(_PARAMS.get("circuit_breaker_recovery_timeout", 60.0))
CALL_MAX_RETRIES = int(
    os.getenv(
        "QUALIA_CALL_MAX_RETRIES",
        _PARAMS.get("call_max_retries", 3),
    )
)
CALL_BASE_DELAY = float(_PARAMS.get("call_base_delay", 1.0))
CALL_MAX_DELAY = float(_PARAMS.get("call_max_delay", 30.0))


class CircuitBreakerOpenError(Exception):
    """Raised when the circuit breaker is open."""


class CircuitBreaker:
    """Simple circuit breaker for asynchronous API calls.

    Parameters
    ----------
    fail_threshold
        Number of consecutive failures before opening the breaker.
    recovery_timeout
        Seconds to wait before allowing a new request when open.
    name
        Metric namespace used when emitting StatsD metrics.
    statsd_client
        Optional ``DogStatsd`` instance for metrics emission. When ``None`` and
        :data:`market_metrics_enabled` is ``True`` a new client is created.
    event_bus
        Optional :class:`~src.qualia.memory.event_bus.SimpleEventBus` used to
        publish ``utils.circuit_breaker_state`` events when the circuit opens or
        closes.
    """

    def __init__(
        self,
        fail_threshold: int = CB_FAIL_THRESHOLD,
        recovery_timeout: float = CB_RECOVERY_TIMEOUT,
        *,
        name: str = "circuit_breaker",
        statsd_client: DogStatsd | None = None,
        event_bus: SimpleEventBus | None = None,
    ) -> None:
        self.fail_threshold = max(1, fail_threshold)
        self.recovery_timeout = max(0.01, recovery_timeout)
        self._failure_count = 0
        self._opened_at: float | None = None
        self.name = name
        self.statsd = statsd_client or (DogStatsd() if market_metrics_enabled else None)
        self.event_bus = event_bus
        self.publish_events = feature_toggle("utils_v2")

    @property
    def is_open(self) -> bool:
        """Return ``True`` when the circuit breaker is open."""

        return self._opened_at is not None and (
            time.time() - self._opened_at < self.recovery_timeout
        )

    def time_until_recovery(self) -> float:
        """Return seconds remaining until the circuit allows a new request."""

        if self._opened_at is None:
            return 0.0
        elapsed = time.time() - self._opened_at
        return max(0.0, self.recovery_timeout - elapsed)

    def allow_request(self) -> bool:
        """Return ``True`` if a request should be attempted."""
        if self._opened_at is None:
            return True
        if time.time() - self._opened_at >= self.recovery_timeout:
            logger.debug(
                "Circuit breaker %s half-open; allowing test request", self.name
            )
            if self.statsd:
                self.statsd.increment(f"{self.name}.half_open")
            self._opened_at = None
            self._failure_count = 0
            if self.event_bus and self.publish_events:
                self.event_bus.publish(
                    CIRCUIT_BREAKER_STATE_EVENT,
                    {"name": self.name, "state": "closed"},
                )
            return True
        if self.statsd:
            self.statsd.increment(f"{self.name}.blocked")
        remaining = self.time_until_recovery()
        logger.debug(
            "Circuit breaker %s blocked; %.2fs until recovery", self.name, remaining
        )
        return False

    def record_success(self) -> None:
        """Reset failure count after a successful call."""
        was_open = self._opened_at is not None
        self._failure_count = 0
        self._opened_at = None
        if self.statsd:
            self.statsd.increment(f"{self.name}.success")
        persist_metric_to_qpm(f"{self.name}.success", True)
        if was_open and self.event_bus and self.publish_events:
            self.event_bus.publish(
                CIRCUIT_BREAKER_STATE_EVENT,
                {"name": self.name, "state": "closed"},
            )

    def record_failure(self) -> None:
        """Record a failure and open the circuit if needed."""
        self._failure_count += 1
        if self.statsd:
            self.statsd.increment(f"{self.name}.failure")
        persist_metric_to_qpm(f"{self.name}.success", False)

        should_open = (
            self._failure_count >= self.fail_threshold and self._opened_at is None
        )
        if should_open:
            self._opened_at = time.time()
            logger.warning(
                "Circuit breaker %s opened after %s failures",
                self.name,
                self._failure_count,
            )
            if self.statsd:
                self.statsd.increment(f"{self.name}.opened")
            if self.event_bus and self.publish_events:
                self.event_bus.publish(
                    CIRCUIT_BREAKER_STATE_EVENT,
                    {"name": self.name, "state": "open"},
                )


class APICircuitBreaker(CircuitBreaker):
    """Circuit breaker especializado para chamadas de API."""

    def __init__(
        self,
        fail_threshold: int = CB_FAIL_THRESHOLD,
        recovery_timeout: float = CB_RECOVERY_TIMEOUT,
        *,
        name: str = "api_circuit_breaker",
        statsd_client: DogStatsd | None = None,
        event_bus: SimpleEventBus | None = None,
    ) -> None:
        super().__init__(
            fail_threshold,
            recovery_timeout,
            name=name,
            statsd_client=statsd_client,
            event_bus=event_bus,
        )


async def call_with_backoff(
    circuit_breaker: CircuitBreaker,
    func: Callable[..., Awaitable[Any]],
    *args: Any,
    max_retries: int = CALL_MAX_RETRIES,
    base_delay: float = CALL_BASE_DELAY,
    max_delay: float = CALL_MAX_DELAY,
    statsd_client: DogStatsd | None = None,
    **kwargs: Any,
) -> Any:
    """Execute ``func`` with retries, exponential backoff and a circuit breaker.

    Parameters
    ----------
    circuit_breaker
        ``CircuitBreaker`` guarding the request.
    func
        Awaitable callable to execute.
    max_retries
        Maximum number of attempts before giving up.
    base_delay
        Base delay in seconds for the exponential backoff.
    max_delay
        Maximum delay between retries.
    statsd_client
        Optional metrics client used to emit retry counters.
    **kwargs
        Forwarded to ``func``.

    Notes
    -----
    Default values can be overridden via ``config/utils.yaml`` when the
    feature flag ``QUALIA_FT_UTILS_V2`` is enabled.
    """

    statsd = statsd_client or circuit_breaker.statsd

    attempts = 0
    last_exc: Exception | None = None
    while attempts < max_retries:
        if not circuit_breaker.allow_request():
            raise CircuitBreakerOpenError("Circuit breaker open")
        try:
            result = await func(*args, **kwargs)
            circuit_breaker.record_success()
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.call_success")
            return result
        except Exception as exc:  # pragma: no cover - network or other error
            last_exc = exc
            circuit_breaker.record_failure()
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.call_failure")
            attempts += 1
            if attempts >= max_retries:
                break
            delay = min(base_delay * 2 ** (attempts - 1), max_delay)
            logger.debug("Retrying in %.2fs after failure: %s", delay, exc)
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.retry")
            await asyncio.sleep(delay)
    if last_exc:
        raise last_exc
    raise RuntimeError("call_with_backoff failed without exception")


async def wait_for_recovery(circuit_breaker: CircuitBreaker) -> None:
    """Wait for a circuit breaker to allow requests again."""

    if circuit_breaker.is_open:
        delay = circuit_breaker.time_until_recovery()
        logger.info(
            "Circuit breaker %s open; waiting %.2fs for recovery",
            circuit_breaker.name,
            delay,
        )
        await asyncio.sleep(delay)


__all__ = [
    "CircuitBreaker",
    "APICircuitBreaker",
    "CircuitBreakerOpenError",
    "call_with_backoff",
    "wait_for_recovery",
    "CIRCUIT_BREAKER_STATE_EVENT",
]
