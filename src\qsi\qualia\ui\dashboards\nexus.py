from __future__ import annotations

"""Dash dashboard para o módulo NEXUS RETRO.

Exibe em tempo real o valor de *cross-modal coherence* publicado pelo
``CoherenceMonitor``.
"""

import dash
from dash import html, dcc, Output, Input
from typing import Any
import plotly.graph_objects as go
import datetime

from qualia.memory.event_bus import SimpleEventBus
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

event_bus: SimpleEventBus | None = None  # set via init_app


def init_app(external_bus: SimpleEventBus) -> dash.Dash:
    global event_bus
    event_bus = external_bus

    app = dash.Dash(__name__)
    app.layout = html.Div(
        [
            html.H2("NEXUS • Cross-Modal Coherence"),
            dcc.Graph(id="coherence-graph"),
            dcc.Interval(id="interval", interval=1000, n_intervals=0),
        ]
    )

    @app.callback(Output("coherence-graph", "figure"), Input("interval", "n_intervals"))
    def _update(_: int) -> go.Figure:  # noqa: D401
        # Coherence values could be stored elsewhere; for demo use simple global list
        if not hasattr(_update, "data"):
            _update.data = []  # type: ignore[attr-defined]

        # We'll pull the latest value from a shared list populated by event handler
        data = _update.data  # type: ignore[attr-defined]
        fig = go.Figure()
        if data:
            times, values = zip(*data)
            fig.add_trace(go.Scatter(x=times, y=values, mode="lines+markers"))
            fig.update_yaxes(range=[0, 1])
        fig.update_layout(xaxis_title="Time", yaxis_title="Coherence (0-1)")
        return fig

    # Event handler to collect coherence events
    def _on_coherence(payload: dict[str, Any]) -> None:  # type: ignore[override]
        ts = datetime.datetime.fromtimestamp(payload.get("timestamp", 0))
        val = payload.get("coherence", 0.0)
        if not hasattr(_update, "data"):
            _update.data = []  # type: ignore[attr-defined]
        _update.data.append((ts, val))  # type: ignore[attr-defined]
        # Keep last 120 pts
        _update.data = _update.data[-120:]  # type: ignore[attr-defined]

    external_bus.subscribe("nexus.cross_modal_coherence", _on_coherence)
    logger.info("Nexus dashboard inicializado e assinou nexus.cross_modal_coherence")

    return app
