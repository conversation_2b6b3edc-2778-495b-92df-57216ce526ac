"""Utilities for generating mock trading data."""

from __future__ import annotations

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List

import numpy as np


def generate_mock_market_data(symbols: List[str]) -> Dict[str, Any]:
    """Generate mock OHLCV data for development."""
    data: Dict[str, Any] = {}
    timestamp_base = datetime.now(timezone.utc)
    for symbol in symbols:
        if "BTC" in symbol:
            base_price = 65000
            volatility = 0.005
        elif "ETH" in symbol:
            base_price = 3500
            volatility = 0.008
        else:
            base_price = 100
            volatility = 0.01

        candles = 336
        times = [timestamp_base - timedelta(minutes=i * 5) for i in range(candles)]
        times.reverse()

        price = base_price
        prices: List[float] = []
        for _ in range(candles):
            change = np.random.normal(0, volatility)
            price *= 1 + change
            prices.append(price)

        high = [p * (1 + np.random.uniform(0, 0.005)) for p in prices]
        low = [p * (1 - np.random.uniform(0, 0.005)) for p in prices]
        open_prices = [
            prev_close + ((p - prev_close) * 0.3)
            for prev_close, p in zip([prices[0]] + prices[:-1], prices)
        ]
        volume = [
            base_price * 10 * (1 + np.random.uniform(-0.5, 1.5)) for _ in range(candles)
        ]
        order_book_imbalance = float(np.random.uniform(-1.0, 1.0))
        funding_rate = float(np.random.normal(0.0, 0.0001))
        ts_embedding = list(np.linspace(-1.0, 1.0, 4) + np.random.normal(0, 0.01, 4))
        open_interest = float(np.random.uniform(0.0, 1.0))

        data[symbol] = {
            "5m": {
                "timestamps": [t.isoformat() for t in times],
                "open": open_prices,
                "high": high,
                "low": low,
                "close": prices,
                "volume": volume,
                "order_book_imbalance": order_book_imbalance,
                "funding_rate": funding_rate,
                "open_interest": open_interest,
                "ts_embedding": ts_embedding,
            }
        }
    return data


def simulate_positions(symbols: List[str], capital: float) -> List[Dict[str, Any]]:
    """Generate mock open positions."""
    positions: List[Dict[str, Any]] = []
    for symbol in symbols:
        if np.random.random() < 0.4:
            side = "buy" if np.random.random() > 0.5 else "sell"
            if "BTC" in symbol:
                entry_price = 65000 * (1 + np.random.uniform(-0.02, 0.02))
                current_price = entry_price * (1 + np.random.uniform(-0.01, 0.01))
                quantity = np.round(capital * 0.2 / entry_price, 8)
            elif "ETH" in symbol:
                entry_price = 3500 * (1 + np.random.uniform(-0.02, 0.02))
                current_price = entry_price * (1 + np.random.uniform(-0.01, 0.01))
                quantity = np.round(capital * 0.15 / entry_price, 8)
            else:
                entry_price = 100 * (1 + np.random.uniform(-0.02, 0.02))
                current_price = entry_price * (1 + np.random.uniform(-0.01, 0.01))
                quantity = np.round(capital * 0.1 / entry_price, 8)

            if side == "buy":
                unrealized_pnl = (current_price - entry_price) * quantity
                unrealized_pnl_pct = ((current_price / entry_price) - 1) * 100
            else:
                unrealized_pnl = (entry_price - current_price) * quantity
                unrealized_pnl_pct = ((entry_price / current_price) - 1) * 100

            duration = np.random.randint(5 * 60, 2 * 60 * 60)
            positions.append(
                {
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "entry_price": entry_price,
                    "current_price": current_price,
                    "unrealized_pnl": unrealized_pnl,
                    "unrealized_pnl_pct": unrealized_pnl_pct,
                    "duration": duration,
                }
            )
    return positions


def simulate_trade_history(
    symbols: List[str], capital: float, num_trades: int = 20
) -> List[Dict[str, Any]]:
    """Generate a fake trade history."""
    trades: List[Dict[str, Any]] = []
    for _ in range(num_trades):
        symbol = np.random.choice(symbols)
        side = "buy" if np.random.random() > 0.5 else "sell"
        timestamp = datetime.now(timezone.utc) - timedelta(
            hours=np.random.uniform(0, 48)
        )
        if "BTC" in symbol:
            price = 65000 * (1 + np.random.uniform(-0.05, 0.05))
            quantity = np.round(capital * np.random.uniform(0.05, 0.2) / price, 8)
        elif "ETH" in symbol:
            price = 3500 * (1 + np.random.uniform(-0.05, 0.05))
            quantity = np.round(capital * np.random.uniform(0.05, 0.15) / price, 8)
        else:
            price = 100 * (1 + np.random.uniform(-0.05, 0.05))
            quantity = np.round(capital * np.random.uniform(0.05, 0.1) / price, 8)
        is_profit = np.random.random() < 0.6
        pnl_pct = (
            np.random.uniform(0.2, 2.0) if is_profit else -np.random.uniform(0.2, 1.0)
        )
        realized_pnl = price * quantity * (pnl_pct / 100)
        trades.append(
            {
                "timestamp": timestamp.isoformat(),
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "price": price,
                "realized_pnl": realized_pnl,
                "realized_pnl_pct": pnl_pct,
                "status": "closed",
            }
        )
    trades.sort(key=lambda x: x["timestamp"], reverse=True)
    return trades


def simulate_performance_metrics(
    trade_history: List[Dict[str, Any]], initial_capital: float
) -> Dict[str, Any]:
    """Calculate simple performance metrics from trades."""
    if not trade_history:
        return {
            "pnl": 0,
            "pnl_percentage": 0,
            "win_rate": 0,
            "drawdown": 0,
            "sharpe": 0,
            "trades": 0,
        }

    pnl = sum(t["realized_pnl"] for t in trade_history)
    pnl_percentage = (pnl / initial_capital) * 100
    wins = sum(1 for t in trade_history if t["realized_pnl"] > 0)
    win_rate = wins / len(trade_history)
    drawdown = (
        np.random.uniform(0.05, 0.15) if pnl > 0 else np.random.uniform(0.15, 0.25)
    )
    sharpe = (
        1.5 + np.random.uniform(-0.5, 1.0) if pnl > 0 else np.random.uniform(-0.5, 0.8)
    )
    return {
        "pnl": pnl,
        "pnl_percentage": pnl_percentage,
        "win_rate": win_rate,
        "drawdown": drawdown,
        "sharpe": sharpe,
        "trades": len(trade_history),
    }


def simulate_risk_metrics(positions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Return simulated quantum risk metrics."""
    portfolio_value = sum(p.get("position_size", 0) for p in positions) or 10000
    var_levels = {
        "0.95": np.random.uniform(0.03, 0.08) * portfolio_value,
        "0.99": np.random.uniform(0.07, 0.15) * portfolio_value,
        "0.999": np.random.uniform(0.12, 0.25) * portfolio_value,
    }
    stress_impact = {
        "0.95": np.random.uniform(15, 35),
        "0.99": np.random.uniform(25, 55),
        "0.999": np.random.uniform(40, 70),
    }
    entropy = np.random.uniform(0.2, 0.9)
    coherence = np.random.uniform(0.3, 0.85)
    market_chaos = np.random.uniform(0.1, 0.7)
    extreme_event_probability = np.random.uniform(0.005, 0.15)
    var_pct = var_levels["0.99"] / portfolio_value * 100
    risk_components = [
        min(100, var_pct * 4),
        entropy * 100,
        entropy * (1 - coherence) * 100,
        min(100, extreme_event_probability * 100 * (100 / 15)),
        min(100, market_chaos * 100),
    ]
    risk_weights = [0.3, 0.15, 0.2, 0.25, 0.1]
    weighted_scores = [
        score * weight for score, weight in zip(risk_components, risk_weights)
    ]
    risk_level = sum(weighted_scores) / sum(risk_weights)
    if risk_level < 25:
        risk_category = "BAIXO"
    elif risk_level < 50:
        risk_category = "MODERADO"
    elif risk_level < 75:
        risk_category = "ELEVADO"
    elif risk_level < 90:
        risk_category = "ALTO"
    else:
        risk_category = "EXTREMO"
    return {
        "risk_level": risk_level,
        "risk_category": risk_category,
        "entropy": entropy,
        "coherence": coherence,
        "var_levels": var_levels,
        "stress_impact": stress_impact,
    }


__all__ = [
    "generate_mock_market_data",
    "simulate_positions",
    "simulate_trade_history",
    "simulate_performance_metrics",
    "simulate_risk_metrics",
]
