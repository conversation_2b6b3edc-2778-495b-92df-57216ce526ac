from __future__ import annotations

"""Processador simbólico do QUALIA."""

from collections import deque
from typing import Deque, Dict, Any, List
from datetime import datetime, timezone
import math

from ..utils.logger import get_logger

sp_logger = get_logger(__name__)


class QualiaSymbolicProcessor:
    """Processa sequências de símbolos detectando padrões e campos semânticos."""

    DEFAULT_HISTORY_MAXLEN = 256

    def __init__(
        self,
        dimension: int = 8,
        semantic_depth: int = 3,
        associative_threshold: float = 0.7,
        history_maxlen: int = DEFAULT_HISTORY_MAXLEN,
    ) -> None:
        self.dimension = dimension
        self.semantic_depth = semantic_depth
        self.associative_threshold = associative_threshold
        self.semantic_fields: Dict[str, Any] = {}
        self.history_maxlen = history_maxlen
        self.pattern_history: Deque[Dict[str, Any]] = deque(maxlen=history_maxlen)
        self.symbol_vectors: Dict[str, Any] = {}

    def process_symbols(self, symbols: str) -> Dict[str, Any]:
        symbols_list = list(symbols)
        patterns = self._detect_patterns(symbols)
        semantic_fields = self._extract_semantic_fields(symbols)
        entropic_analysis = self.symbolic_entropic_analysis(symbols_list)
        results = {
            "patterns": patterns,
            "semantic_fields": semantic_fields,
            "entropic_analysis": entropic_analysis,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        self.pattern_history.append(
            {
                "symbols_count": len(symbols),
                "patterns_count": len(patterns),
                "entropy": entropic_analysis["entropy"],
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )
        return results

    def _detect_patterns(self, symbols: str) -> List[Dict[str, Any]]:
        patterns: List[Dict[str, Any]] = []
        for n in range(2, min(6, len(symbols) // 2)):
            ngrams: Dict[str, int] = {}
            for i in range(len(symbols) - n + 1):
                gram = symbols[i : i + n]
                ngrams[gram] = ngrams.get(gram, 0) + 1
            frequent_ngrams = {g: f for g, f in ngrams.items() if f >= 2}
            for gram, freq in frequent_ngrams.items():
                significance = freq * math.log2(n) / math.log2(len(symbols))
                if significance > 0.1:
                    patterns.append(
                        {
                            "sequence": gram,
                            "length": n,
                            "frequency": freq,
                            "significance": significance,
                        }
                    )
        patterns.sort(key=lambda x: x["significance"], reverse=True)
        return patterns[:10]

    def _extract_semantic_fields(self, symbols: str) -> Dict[str, Dict[str, Any]]:
        words = symbols.split()
        fields: Dict[str, Dict[str, Any]] = {}
        semantic_mappings = {
            "quantum": [
                "quântico",
                "quântica",
                "quantum",
                "qubits",
                "superposição",
                "entrelançamento",
            ],
            "consciousness": [
                "consciência",
                "percepção",
                "consciente",
                "reflexão",
                "awareness",
            ],
            "information": [
                "informação",
                "dados",
                "padrões",
                "entropia",
                "significado",
                "simbólico",
            ],
            "emergence": [
                "emergente",
                "emergência",
                "auto-organização",
                "complexidade",
                "evolução",
            ],
        }
        for field_name, keywords in semantic_mappings.items():
            field_symbols: List[str] = []
            for word in words:
                if any(keyword.lower() in word.lower() for keyword in keywords):
                    field_symbols.append(word)
            if field_symbols:
                coherence = len(field_symbols) / len(words)
                if coherence >= 0.05:
                    fields[field_name] = {
                        "symbols": field_symbols,
                        "coherence": coherence,
                    }
        return fields

    def symbolic_entropic_analysis(self, symbols: List[str]) -> Dict[str, float]:
        freq: Dict[str, int] = {}
        for symbol in symbols:
            freq[symbol] = freq.get(symbol, 0) + 1
        total = len(symbols)
        probs = {s: f / total for s, f in freq.items()}
        entropy = -sum(p * math.log2(p) for p in probs.values())
        max_entropy = math.log2(len(freq)) if len(freq) > 0 else 1
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
        # Diversity measures the spread of symbols in the input. We use the
        # ratio of unique elements to the total number of symbols, which
        # approaches 1 when all symbols are distinct and 0 when a single symbol
        # dominates the sequence.
        diversity = len(freq) / total if total > 0 else 0
        complexity = normalized_entropy * (1 - abs(0.5 - normalized_entropy) * 2)
        return {
            "entropy": normalized_entropy,
            "diversity": diversity,
            "complexity": complexity,
        }

    def get_pattern_history(self) -> List[Dict[str, Any]]:
        return list(self.pattern_history)
