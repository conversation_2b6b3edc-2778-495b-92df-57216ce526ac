import logging
import os
import sys
os.environ["QUALIA_STRATEGY_CONFIG"] = "/tmp/strategy_parameters.yaml"
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from qualia.strategies.strategy_factory import StrategyFactory


def test_qualia_config_in_context_suppresses_log(caplog):
    caplog.set_level(logging.ERROR)
    qualia_config = {"ace_config": {}, "risk_profile_settings": {}}
    strategy = StrategyFactory.create_strategy(
        alias="NovaEstrategiaQUALIA",
        params={},
        context={"symbol": "BTC/USDT", "timeframe": "1h", "qualia_config": qualia_config},
    )
    assert strategy is not None
    assert "'qualia_config' ausente" not in caplog.text
