import numpy as np
import pytest
from hypothesis import given, strategies as st, assume

from qsi.geometry import build_phi_unitary
from qsi.validation import validate_quantum_state
from qsi.geometry import _compute_phi_unitary

# --- Testes de Propriedade para a Física Fundamental ---

@given(st.integers(min_value=2, max_value=64))
def test_build_phi_unitary_is_always_unitary(dimensions):
    """
    Teste de Propriedade: Garante que build_phi_unitary SEMPRE retorna
    uma matriz unitária para qualquer dimensão dentro de um intervalo razoável.
    """
    # GIVEN uma dimensão inteira aleatória
    # WHEN nós construímos a matriz U
    U = build_phi_unitary(dim=dimensions)
    
    # THEN U deve ser unitária (U†U = I)
    identity = np.eye(dimensions)
    assert np.allclose(U.conj().T @ U, identity, atol=1e-9)

# --- Estratégia do Hypothesis para gerar vetores de estado quântico ---

@st.composite
def state_strategy(draw):
    """Estratégia composta para gerar um estado (vetor, norma) e sua dimensão."""
    dimensions = draw(st.integers(min_value=2, max_value=16))
    
    # Limita a magnitude dos números complexos para evitar overflow e infinitos
    components = draw(st.lists(st.complex_numbers(min_magnitude=1e-50, max_magnitude=1e50, allow_nan=False, allow_infinity=False),
                               min_size=dimensions, max_size=dimensions))
    
    vector = np.array(components)
    norm = np.linalg.norm(vector)

    # Filtra casos extremos onde a norma é infinita ou zero
    assume(np.isfinite(norm))
    assume(norm > 1e-9)

    return (vector, norm, dimensions)

# --- Testes de Propriedade para o Decorador de Validação ---

class MockSystem:
    """Classe mock para testar um decorador que depende de um atributo 'self'."""
    def __init__(self, dimensions):
        self.dimensions = dimensions

    @validate_quantum_state
    def process_state(self, state: np.ndarray):
        return "Processed"

@given(state_data=state_strategy())
def test_validate_quantum_state_accepts_normalized_states(state_data):
    """
    Teste de Propriedade: Garante que o decorador aceita QUALQUER estado
    que seja devidamente normalizado.
    """
    # GIVEN um estado quântico aleatório
    vector, norm, dimensions = state_data
    
    # WHEN o estado é normalizado
    if norm > 1e-6:
        normalized_vector = vector / norm
        system = MockSystem(dimensions=dimensions)
        
        # THEN o decorador deve permitir a execução sem erros
        assert system.process_state(normalized_vector) == "Processed"

@given(state_data=state_strategy())
def test_validate_quantum_state_rejects_unnormalized_states(state_data):
    """
    Teste de Propriedade: Garante que o decorador REJEITA QUALQUER estado
    que não esteja normalizado.
    """
    # GIVEN um estado quântico aleatório
    vector, norm, dimensions = state_data
    system = MockSystem(dimensions=dimensions)

    # WHEN a norma não é 1 (e não é zero para evitar divisão por zero)
    if norm > 1e-6 and not np.isclose(norm, 1.0):
        # THEN o decorador deve levantar um ValueError
        with pytest.raises(ValueError, match="não normalizado"):
            system.process_state(vector)

@given(state_data=state_strategy(), wrong_dim_offset=st.integers(min_value=1, max_value=5))
def test_validate_quantum_state_rejects_wrong_dimensions(state_data, wrong_dim_offset):
    """
    Teste de Propriedade: Garante que o decorador REJEITA QUALQUER estado
    cuja dimensão não corresponda à do sistema.
    """
    # GIVEN um estado quântico aleatório e normalizado
    vector, norm, dimensions = state_data
    if norm > 1e-6:
        normalized_vector = vector / norm
        
        # WHEN o sistema é configurado com uma dimensão diferente
        wrong_dimensions = dimensions + wrong_dim_offset
        system = MockSystem(dimensions=wrong_dimensions)
        
        # THEN o decorador deve levantar um ValueError
        with pytest.raises(ValueError, match="Dimensões de estado inválidas|não normalizado"):
            system.process_state(normalized_vector)


def test_build_phi_unitary_uses_disk_cache(tmp_path):
    dim = 4
    phi = 1.7

    # Primeira construção gera e salva no disco
    u1 = build_phi_unitary(dim=dim, phi=phi, cache_dir=tmp_path)
    file_phi = f"{phi:.8f}".replace(".", "_")
    cache_file = tmp_path / f"phi_unitary_{dim}_{file_phi}.npy"
    assert cache_file.exists()

    # Limpa cache em memória e reconstrói, reutilizando o arquivo
    _compute_phi_unitary.cache_clear()
    u2 = build_phi_unitary(dim=dim, phi=phi, cache_dir=tmp_path)

    assert np.allclose(u1, u2)
