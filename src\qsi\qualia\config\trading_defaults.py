"""Access default trading parameters for QUALIA.

These defaults live in ``config/trading_defaults.yaml`` and may be
replaced by setting ``QUALIA_TRADING_DEFAULTS``.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("trading_defaults.yaml")


def load_trading_defaults() -> Dict[str, Any]:
    """Load trading default parameters from a YAML file.

    The ``QUALIA_TRADING_DEFAULTS`` environment variable can override the bundled
    file.

    Returns
    -------
    Dict[str, Any]
        Mapping of parameter names to values. Returns an empty dict on error.
    """
    return load_yaml_config(
        "QUALIA_TRADING_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_trading_defaults"]
